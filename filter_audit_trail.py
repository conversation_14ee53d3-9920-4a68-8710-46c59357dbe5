# This script filters log entries from audit_trail.log after a given cutoff timestamp
import json
from datetime import datetime

input_file = "audit_trail.log"
output_file = "audit_trail_last_20min.log"
cutoff = datetime.strptime("2025-06-13 21:29:59,799", "%Y-%m-%d %H:%M:%S,%f")

with open(input_file, "r") as fin, open(output_file, "w") as fout:
    for line in fin:
        try:
            entry = json.loads(line)
            ts = datetime.strptime(entry["timestamp"], "%Y-%m-%d %H:%M:%S,%f")
            if ts > cutoff:
                fout.write(line)
        except Exception:
            continue
