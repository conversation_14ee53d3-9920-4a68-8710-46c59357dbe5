#!/usr/bin/env python3
"""
Example script to create memory examples based on prompt_manager.py analysis.
This demonstrates how to use the memory system to store knowledge from the Alfred logs system.
"""

import os
import sys

# Add the parent directory to the Python path to allow absolute imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))

from src.env_config import EnvConfig
from src.memory import MemoryManager


def create_example_memories():
    """Create example memories based on the prompt manager analysis."""

    # Initialize config and memory manager
    config = EnvConfig.load()
    memory_manager = MemoryManager(config)

    print("Creating example memories based on prompt_manager.py analysis...")

    # ================================
    # DOMAIN WORKFLOW MEMORIES - Common log analysis queries
    # ================================

    # 1. Finding errors in logs
    domain_workflow_memory_1 = memory_manager.teach_domain_workflow(
        question="Show me all the errors from yesterday",
        logic={
            "steps": [
                "Identify Error Scope: Determine time range and service filters for error investigation",
                "Gather Error Overview: Query for error logs using status_code >= 400 with summary projection to get overview",
                "Analyze Error Patterns: Review summary results to identify common error types, affected services, and frequency patterns",
                "Investigate Critical Errors: Drill down to full logs by _id for high-impact or recurring errors requiring detailed analysis",
                "Synthesize Findings: Present error analysis with patterns, root causes, and impact assessment",
            ],
            "strategy": "Always use summary fields for large result sets to prevent context overflow",
        },
        example_phrasings=[
            "Show me all errors from yesterday",
            "Find yesterday's error logs",
            "What errors occurred yesterday?",
            "Give me error logs from the previous day",
        ],
        created_by="test",
    )

    # 2. Tracking application journey
    domain_workflow_memory_2 = memory_manager.teach_domain_workflow(
        question="What happened to application 1234567890?",
        logic={
            "steps": [
                "Query for a Summary Timeline: Retrieve all log events for application ID using projection(['_id', 'summary', 'timestamp']) sorted by timestamp ascending",
                "Construct the Application Narrative: Analyze summaries chronologically to build step-by-step story from submission to final decision",
                "Determine the Final Business Outcome: Identify the last significant event to determine final status (APPROVED, DECLINED, EXPIRED, or BOOKED)",
                "Synthesize and Present Findings: Summarize entire sequence in clear, non-technical format suitable for business stakeholder",
            ],
            "strategy": "Start with summary overview, then drill down by _id for specific details",
            "note": "Applications can have 50+ logs, always use summary fields first",
        },
        example_phrasings=[
            "application tracking",
            "app timeline",
            "what happened to application",
            "What happened to application ID abc123?",
            "Track application journey for ID xyz",
            "Show me the timeline for application 12345",
        ],
        created_by="test",
    )

    print(
        f"Created domain workflow memories: {domain_workflow_memory_1}, {domain_workflow_memory_2}"
    )

    # ================================
    # FIELD KNOWLEDGE MEMORIES - Log schema knowledge
    # ================================

    # 1. Core log structure knowledge
    field_knowledge_memory_1 = memory_manager.teach_field_knowledge(
        title="MongoDB Log Document Structure",
        body="Each log entry is a JSON document in MongoDB representing a single request-response cycle in a microservice. Key fields include: doc_id (groups related operations), context.application_id (application identifier), context.order_id (order identifier), source.service_name (microservice name), url (API endpoint), status_code (HTTP status), summary (human-readable event summary). Timestamps are stored as epoch or UTC ISO 8601 format and must be converted to New York timezone for display.",
        field_type="log_schema",
        aliases=[
            "log structure",
            "document format",
            "mongodb schema",
            "log entry format",
        ],
        created_by="test",
    )

    # 2. Field knowledge about application ID
    field_knowledge_memory_2 = memory_manager.teach_field_knowledge(
        title="context.application_id field",
        body="Unique identifier for an application being processed. Use for tracking application lifecycle across services.",
        field_type="log_schema",
        aliases=["app id", "application identifier", "application ID"],
        created_by="test",
    )

    print(
        f"Created field knowledge memories: {field_knowledge_memory_1}, {field_knowledge_memory_2}"
    )

    # ================================
    # TRANSLATION MAPPING MEMORIES - Business lingo mappings
    # ================================

    # 1. Business status terms
    translation_mapping_memory_1 = memory_manager.teach_translation_mapping(
        business_term="application was declined",
        technical_mapping="response.body.data.status == 'DECLINED'",
        aliases=["declined", "rejected", "denial", "application decline"],
        created_by="test",
    )

    # 2. Error terminology
    translation_mapping_memory_2 = memory_manager.teach_translation_mapping(
        business_term="system errors",
        technical_mapping="status_code >= 400",
        aliases=["errors", "failures", "issues", "problems"],
        created_by="test",
    )

    print(
        f"Created translation mapping memories: {translation_mapping_memory_1}, {translation_mapping_memory_2}"
    )

    # ================================
    # AGENT GUIDANCE MEMORIES - Internal guidance and rules
    # ================================

    # 1. Query optimization thresholds
    agent_guidance_memory_1 = memory_manager.teach_agent_guidance(
        title="Query optimization thresholds",
        body="Use full logs for <10 results, summary+key fields for 10-50 results, summary-only for >50 results",
        created_by="test",
    )

    # 2. Large dataset handling strategy
    agent_guidance_memory_2 = memory_manager.teach_agent_guidance(
        title="Large dataset handling strategy",
        body="When query might return >10 logs, MUST use projection(['_id', 'summary', 'timestamp']) to prevent context overflow",
        created_by="test",
    )

    print(
        f"Created agent guidance memories: {agent_guidance_memory_1}, {agent_guidance_memory_2}"
    )

    # ================================
    # Display memory statistics
    # ================================

    stats = memory_manager.get_memory_stats()
    print(f"\nMemory System Statistics:")
    print(f"Total memories: {stats['total_memories']}")
    print(f"By type: {stats['by_type']}")
    print(f"Average confidence: {stats['average_confidence']:.2f}")

    return {
        "domain_workflow": [domain_workflow_memory_1, domain_workflow_memory_2],
        "field_knowledge": [field_knowledge_memory_1, field_knowledge_memory_2],
        "translation_mapping": [
            translation_mapping_memory_1,
            translation_mapping_memory_2,
        ],
        "agent_guidance": [agent_guidance_memory_1, agent_guidance_memory_2],
    }


if __name__ == "__main__":
    try:
        print("Initializing memory system and creating examples...")
        created_memories = create_example_memories()

        print("\n" + "=" * 60)
        print("MEMORY CREATION COMPLETE!")
        print("=" * 60)

        for memory_type, memory_ids in created_memories.items():
            print(f"\n{memory_type.upper()} MEMORIES:")
            for i, memory_id in enumerate(memory_ids, 1):
                print(f"  {i}. {memory_id}")

        print("\nThese memories are now available for semantic search and retrieval.")
        print("The memory system can now help with common log analysis tasks!")

    except Exception as e:
        print(f"Error creating memories: {e}")
        import traceback

        traceback.print_exc()
