#!/usr/bin/env python3
"""
Gemini Model Latency Comparison Script
=====================================

This script compares the latency of different Gemini models using the same
interface as log_agent.py. It tests various models with the same prompt
to measure response times and provide performance insights.
"""

import argparse
import logging
import statistics
import time
from typing import Dict, List, Tuple

from src.client_managers import get_genai_manager
from src.env_config import EnvConfig
from src.retry_utils import call_google_genai_with_retry

# Available Gemini models to test
GEMINI_MODELS = [
    "gemini-2.5-pro",
    "gemini-2.5-flash",
    "gemini-2.0-flash",
    "gemini-2.0-flash-001",
    "gemini-2.0-flash-lite",
]

# Test prompts of varying complexity
TEST_PROMPTS = {
    "simple": "What is 2 + 2?",
    "medium": "Explain the concept of database indexing in 2-3 sentences.",
    "complex": """Analyze this log entry and explain what it means:
    [2024-01-15 14:32:18] ERROR: Database connection timeout after 30s.
    Retrying connection attempt 3/5. Pool size: 15/20 active connections.
    Query: SELECT * FROM user_sessions WHERE last_active > NOW() - INTERVAL 1 HOUR
    """,
}


class GeminiLatencyTester:
    """Test latency across different Gemini models."""

    def __init__(self, config: EnvConfig):
        """Initialize the tester with configuration."""
        self.config = config
        self.genai_manager = get_genai_manager(config)
        self.client = self.genai_manager.get_client()

        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    def test_model_latency(
        self, model: str, prompt: str, iterations: int = 3
    ) -> Tuple[float, float, List[float]]:
        """
        Test latency for a specific model and prompt.

        Args:
            model: Gemini model name
            prompt: Test prompt
            iterations: Number of test iterations

        Returns:
            Tuple of (average_latency, std_dev, all_times)
        """
        times = []

        for i in range(iterations):
            self.logger.info(f"Testing {model} - iteration {i+1}/{iterations}")

            start_time = time.time()

            try:
                response = call_google_genai_with_retry(
                    client=self.client,
                    model=model,
                    contents=prompt,
                    max_retries=2,
                    logger=self.logger,
                )

                end_time = time.time()
                latency = end_time - start_time
                times.append(latency)

                self.logger.info(f"  Iteration {i+1}: {latency:.3f}s")

            except Exception as e:
                self.logger.error(f"  Error in iteration {i+1}: {e}")
                # Still record the time up to the error
                end_time = time.time()
                times.append(end_time - start_time)

        if times:
            avg_time = statistics.mean(times)
            std_dev = statistics.stdev(times) if len(times) > 1 else 0.0
            return avg_time, std_dev, times
        else:
            return float("inf"), 0.0, []

    def run_comparison(
        self, models: List[str] = None, prompt_type: str = "medium", iterations: int = 3
    ) -> Dict[str, Dict]:
        """
        Run latency comparison across models.

        Args:
            models: List of models to test (default: all available)
            prompt_type: Type of prompt to use (simple/medium/complex)
            iterations: Number of iterations per model

        Returns:
            Dictionary with results for each model
        """
        if models is None:
            models = GEMINI_MODELS

        if prompt_type not in TEST_PROMPTS:
            raise ValueError(
                f"Invalid prompt type. Choose from: {list(TEST_PROMPTS.keys())}"
            )

        prompt = TEST_PROMPTS[prompt_type]
        results = {}

        print(f"\n{'='*60}")
        print(f"GEMINI MODEL LATENCY COMPARISON")
        print(f"{'='*60}")
        print(f"Prompt Type: {prompt_type}")
        print(f"Iterations per model: {iterations}")
        print(f"Testing {len(models)} models...")
        print(f"{'='*60}\n")

        for model in models:
            print(f"Testing {model}...")
            avg_latency, std_dev, all_times = self.test_model_latency(
                model, prompt, iterations
            )

            results[model] = {
                "avg_latency": avg_latency,
                "std_dev": std_dev,
                "all_times": all_times,
                "min_time": min(all_times) if all_times else float("inf"),
                "max_time": max(all_times) if all_times else 0,
            }

            print(f"  Average: {avg_latency:.3f}s (±{std_dev:.3f}s)")
            print(
                f"  Range: {results[model]['min_time']:.3f}s - {results[model]['max_time']:.3f}s\n"
            )

        return results

    def print_summary(self, results: Dict[str, Dict]):
        """Print a summary of results sorted by performance."""
        print(f"{'='*60}")
        print(f"SUMMARY - RANKED BY AVERAGE LATENCY")
        print(f"{'='*60}")

        # Sort by average latency
        sorted_results = sorted(results.items(), key=lambda x: x[1]["avg_latency"])

        print(
            f"{'Rank':<4} {'Model':<25} {'Avg Latency':<12} {'Std Dev':<10} {'Range'}"
        )
        print(f"{'-'*4} {'-'*25} {'-'*12} {'-'*10} {'-'*15}")

        for rank, (model, data) in enumerate(sorted_results, 1):
            avg = data["avg_latency"]
            std = data["std_dev"]
            min_t = data["min_time"]
            max_t = data["max_time"]

            if avg == float("inf"):
                latency_str = "FAILED"
                std_str = "-"
                range_str = "-"
            else:
                latency_str = f"{avg:.3f}s"
                std_str = f"±{std:.3f}s"
                range_str = f"{min_t:.3f}-{max_t:.3f}s"

            print(f"{rank:<4} {model:<25} {latency_str:<12} {std_str:<10} {range_str}")

        print(f"\n{'='*60}")

        # Performance insights
        if sorted_results and sorted_results[0][1]["avg_latency"] != float("inf"):
            fastest = sorted_results[0]
            print(f"🏆 Fastest Model: {fastest[0]} ({fastest[1]['avg_latency']:.3f}s)")

            if len(sorted_results) > 1:
                slowest = sorted_results[-1]
                if slowest[1]["avg_latency"] != float("inf"):
                    speedup = slowest[1]["avg_latency"] / fastest[1]["avg_latency"]
                    print(
                        f"⚡ Speed Improvement: {speedup:.1f}x faster than {slowest[0]}"
                    )


def main():
    """Main function to run the latency comparison."""
    parser = argparse.ArgumentParser(
        description="Compare latency across different Gemini models"
    )
    parser.add_argument(
        "--env-file", type=str, help="Path to environment file (default: auto-detect)"
    )
    parser.add_argument(
        "--models",
        nargs="+",
        choices=GEMINI_MODELS,
        default=GEMINI_MODELS,
        help="Models to test (default: all)",
    )
    parser.add_argument(
        "--prompt-type",
        choices=list(TEST_PROMPTS.keys()),
        default="medium",
        help="Type of prompt to use for testing (default: medium)",
    )
    parser.add_argument(
        "--iterations",
        type=int,
        default=3,
        help="Number of iterations per model (default: 3)",
    )

    args = parser.parse_args()

    try:
        # Load configuration
        config = EnvConfig.load(args.env_file)

        # Create tester and run comparison
        tester = GeminiLatencyTester(config)
        results = tester.run_comparison(
            models=args.models, prompt_type=args.prompt_type, iterations=args.iterations
        )

        # Print summary
        tester.print_summary(results)

    except Exception as e:
        print(f"Error running latency comparison: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
