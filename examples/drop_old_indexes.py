#!/usr/bin/env python3
"""
Script to drop old indexes that reference the removed memory_id field.
"""

import os
import sys

# Add the parent directory to the Python path to allow absolute imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))

from src.env_config import EnvConfig
from src.memory import MemoryManager


def drop_old_indexes():
    """Drop old indexes that reference removed fields."""

    # Initialize config and memory manager
    config = EnvConfig.load()
    memory_manager = MemoryManager(config)

    database = memory_manager.db_manager.database
    collection = database[memory_manager.memory_collection]

    print("Dropping old indexes...")

    try:
        # Get current indexes
        indexes = collection.list_indexes()
        print("Current indexes:")
        for index in indexes:
            print(f"  - {index}")

        # Drop the old memory_id index
        try:
            collection.drop_index("memory_id_1")
            print("✓ Dropped memory_id_1 index")
        except Exception as e:
            print(f"  memory_id_1 index not found or already dropped: {e}")

        # Drop the old parent_memory_id index if it exists
        try:
            collection.drop_index("parent_memory_id_1")
            print("✓ Dropped parent_memory_id_1 index")
        except Exception as e:
            print(f"  parent_memory_id_1 index not found or already dropped: {e}")

        print("\nOld indexes dropped successfully!")

        # List remaining indexes
        print("\nRemaining indexes:")
        for index in collection.list_indexes():
            print(f"  - {index['name']}: {index.get('key', {})}")

    except Exception as e:
        print(f"Error dropping indexes: {e}")
        raise


if __name__ == "__main__":
    try:
        drop_old_indexes()
    except Exception as e:
        print(f"Error: {e}")
        import traceback

        traceback.print_exc()
