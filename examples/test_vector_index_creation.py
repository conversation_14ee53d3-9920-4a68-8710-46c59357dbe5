#!/usr/bin/env python3
"""
Test script to explore the correct API for creating vector search indexes.
"""

import os
import sys

# Add the parent directory to the Python path to allow absolute imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))

from src.env_config import EnvConfig
from src.memory import MemoryManager


def test_vector_index_creation():
    """Test and explore vector search index creation API."""

    # Initialize config and memory manager
    config = EnvConfig.load()
    memory_manager = MemoryManager(config)

    database = memory_manager.db_manager.database
    collection = database[memory_manager.memory_collection]

    print("Testing vector search index creation API...")

    # Check current search indexes
    try:
        existing_indexes = list(collection.list_search_indexes())
        print(f"Current search indexes: {len(existing_indexes)}")
        for idx in existing_indexes:
            print(f"  - {idx}")
    except Exception as e:
        print(f"Error listing search indexes: {e}")

    # Test different API approaches
    vector_index_definition = {
        "fields": [
            {
                "type": "vector",
                "path": "embedding",
                "numDimensions": config.embedding_dimension,
                "similarity": "cosine",
            },
            {"type": "filter", "path": "type"},
            {"type": "filter", "path": "is_latest_version"},
        ]
    }

    print(f"\nVector index definition: {vector_index_definition}")

    # Try to understand the correct API
    try:
        # Check what methods are available
        search_methods = [
            method for method in dir(collection) if "search" in method.lower()
        ]
        print(f"\nAvailable search-related methods: {search_methods}")

        # Check create_search_index signature
        import inspect

        if hasattr(collection, "create_search_index"):
            sig = inspect.signature(collection.create_search_index)
            print(f"\ncreate_search_index signature: {sig}")
    except Exception as e:
        print(f"Error exploring API: {e}")


if __name__ == "__main__":
    try:
        test_vector_index_creation()
    except Exception as e:
        print(f"Error during API exploration: {e}")
        import traceback

        traceback.print_exc()
