#!/usr/bin/env python3
"""
Script to plot total API usage and cost per run ID from Alfred database.
"""

import argparse
import os
import sys

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

# Add src directory to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), "src"))

from src.client_managers import get_mongo_manager
from src.env_config import EnvConfig


def connect_to_database():
    """Connect to MongoDB database using Secret Manager credentials."""
    config = EnvConfig.load()
    mongo_manager = get_mongo_manager(config)
    return mongo_manager.get_database()


def get_api_usage_by_run_id(db):
    """Get API usage and cost data aggregated by run_id with separation by usage type."""

    # First, let's check if there are any summarization-related logs at all
    print("🔍 Searching for summarization-related logs...")

    # Check for logs that might be summarization
    summarization_search_pipeline = [
        {
            "$match": {
                "$or": [
                    {"message": {"$regex": "summary", "$options": "i"}},
                    {"message": {"$regex": "summariz", "$options": "i"}},
                    {"message_type": {"$regex": "summariz", "$options": "i"}},
                    {"details.summary_source": {"$exists": True}},
                    {"details.summary_model": {"$exists": True}},
                    {"details.summary": {"$exists": True}},
                ]
            }
        },
        {"$limit": 10},
        {
            "$project": {
                "message": 1,
                "message_type": 1,
                "details.summary_source": 1,
                "details.summary_model": 1,
                "details.summary": 1,
                "details.api_metrics": 1,
            }
        },
    ]

    summarization_logs = list(db.session_logs.aggregate(summarization_search_pipeline))

    if summarization_logs:
        print(f"Found {len(summarization_logs)} potential summarization logs:")
        for i, log in enumerate(summarization_logs[:3]):
            print(f"  Log {i+1}:")
            print(f"    message: {log.get('message', 'N/A')}")
            print(f"    message_type: {log.get('message_type', 'N/A')}")
            print(f"    has api_metrics: {'api_metrics' in log.get('details', {})}")
            print(
                f"    summary_source: {log.get('details', {}).get('summary_source', 'N/A')}"
            )
            print(
                f"    summary_model: {log.get('details', {}).get('summary_model', 'N/A')}"
            )
            print(f"    has summary: {'summary' in log.get('details', {})}")
            print()
    else:
        print("❌ No summarization logs found in session_logs collection")

        # Check if there are any logs in the summarized collection
        try:
            from src.env_config import EnvConfig

            config = EnvConfig.load()
            summarized_collection = db.get_collection(
                config.mongodb_summarized_collection
            )

            summarized_count = summarized_collection.count_documents({})
            print(
                f"📊 Found {summarized_count} documents in summarized collection: {config.mongodb_summarized_collection}"
            )

            if summarized_count > 0:
                # Check if summarized logs have API metrics
                summarized_with_metrics = summarized_collection.count_documents(
                    {"api_metrics": {"$exists": True}}
                )
                print(
                    f"📊 Found {summarized_with_metrics} summarized documents with API metrics"
                )

                if summarized_with_metrics > 0:
                    sample_summarized = list(
                        summarized_collection.find(
                            {"api_metrics": {"$exists": True}}
                        ).limit(2)
                    )
                    print("Sample summarized logs with API metrics:")
                    for i, log in enumerate(sample_summarized):
                        print(f"  Summarized Log {i+1}:")
                        print(f"    has api_metrics: {'api_metrics' in log}")
                        print(f"    summary_model: {log.get('summary_model', 'N/A')}")
                        print(f"    summary_source: {log.get('summary_source', 'N/A')}")
                        print()

        except Exception as e:
            print(f"❌ Error checking summarized collection: {e}")

    # Aggregate data from session_logs collection with detailed breakdown
    pipeline = [
        {
            "$match": {
                "run_id": {"$exists": True, "$ne": None},
                "details.api_metrics": {"$exists": True},
            }
        },
        {
            "$addFields": {
                "usage_type": {
                    "$cond": {
                        "if": {
                            "$eq": ["$details.step", "background_log_summarization"]
                        },
                        "then": "summarization",
                        "else": "agent_run",
                    }
                }
            }
        },
        {
            "$group": {
                "_id": "$run_id",
                "total_api_calls": {"$sum": 1},
                "total_input_tokens": {
                    "$sum": {"$ifNull": ["$details.api_metrics.prompt_token_count", 0]}
                },
                "total_output_tokens": {
                    "$sum": {
                        "$ifNull": ["$details.api_metrics.candidates_token_count", 0]
                    }
                },
                "total_tokens": {
                    "$sum": {"$ifNull": ["$details.api_metrics.total_token_count", 0]}
                },
                "total_cost_usd": {
                    "$sum": {"$ifNull": ["$details.estimated_cost_usd", 0]}
                },
                # Agent run costs
                "agent_api_calls": {
                    "$sum": {"$cond": [{"$eq": ["$usage_type", "agent_run"]}, 1, 0]}
                },
                "agent_input_tokens": {
                    "$sum": {
                        "$cond": [
                            {"$eq": ["$usage_type", "agent_run"]},
                            {"$ifNull": ["$details.api_metrics.prompt_token_count", 0]},
                            0,
                        ]
                    }
                },
                "agent_output_tokens": {
                    "$sum": {
                        "$cond": [
                            {"$eq": ["$usage_type", "agent_run"]},
                            {
                                "$ifNull": [
                                    "$details.api_metrics.candidates_token_count",
                                    0,
                                ]
                            },
                            0,
                        ]
                    }
                },
                "agent_tokens": {
                    "$sum": {
                        "$cond": [
                            {"$eq": ["$usage_type", "agent_run"]},
                            {"$ifNull": ["$details.api_metrics.total_token_count", 0]},
                            0,
                        ]
                    }
                },
                "agent_cost_usd": {
                    "$sum": {
                        "$cond": [
                            {"$eq": ["$usage_type", "agent_run"]},
                            {"$ifNull": ["$details.estimated_cost_usd", 0]},
                            0,
                        ]
                    }
                },
                # Summarization costs
                "summarization_api_calls": {
                    "$sum": {"$cond": [{"$eq": ["$usage_type", "summarization"]}, 1, 0]}
                },
                "summarization_input_tokens": {
                    "$sum": {
                        "$cond": [
                            {"$eq": ["$usage_type", "summarization"]},
                            {"$ifNull": ["$details.api_metrics.prompt_token_count", 0]},
                            0,
                        ]
                    }
                },
                "summarization_output_tokens": {
                    "$sum": {
                        "$cond": [
                            {"$eq": ["$usage_type", "summarization"]},
                            {
                                "$ifNull": [
                                    "$details.api_metrics.candidates_token_count",
                                    0,
                                ]
                            },
                            0,
                        ]
                    }
                },
                "summarization_tokens": {
                    "$sum": {
                        "$cond": [
                            {"$eq": ["$usage_type", "summarization"]},
                            {"$ifNull": ["$details.api_metrics.total_token_count", 0]},
                            0,
                        ]
                    }
                },
                "summarization_cost_usd": {
                    "$sum": {
                        "$cond": [
                            {"$eq": ["$usage_type", "summarization"]},
                            {"$ifNull": ["$details.estimated_cost_usd", 0]},
                            0,
                        ]
                    }
                },
                "first_timestamp": {"$min": "$timestamp"},
                "last_timestamp": {"$max": "$timestamp"},
            }
        },
        {"$sort": {"first_timestamp": 1}},
    ]

    results = list(db.session_logs.aggregate(pipeline))

    # Also get data from chat_sessions collection for additional context
    sessions_pipeline = [
        {
            "$match": {
                "run_id": {"$exists": True, "$ne": None},
                "metadata.runs": {"$exists": True},
            }
        },
        {"$unwind": "$metadata.runs"},
        {
            "$project": {
                "run_id": "$metadata.runs.run_id",
                "session_id": "$session_id",
                "runtime_ms": "$metadata.runs.runtime_ms",
                "api_calls": "$metadata.runs.api_calls",
                "workflow_success": "$metadata.runs.workflow_success",
            }
        },
    ]

    session_results = list(db.chat_sessions.aggregate(sessions_pipeline))

    return results, session_results


def create_visualizations(api_data, session_data):
    """Create visualizations for API usage and cost per run ID."""

    if not api_data:
        print("No API usage data found in the database.")
        return

    # Convert to DataFrame for easier manipulation
    df = pd.DataFrame(api_data)
    df["run_id"] = df["_id"]

    # Create session data lookup
    session_df = pd.DataFrame(session_data)
    session_lookup = (
        session_df.set_index("run_id").to_dict("index") if session_data else {}
    )

    # Add session context to main dataframe
    df["session_id"] = df["run_id"].map(
        lambda x: session_lookup.get(x, {}).get("session_id", "Unknown")
    )
    df["runtime_ms"] = df["run_id"].map(
        lambda x: session_lookup.get(x, {}).get("runtime_ms", 0)
    )
    df["workflow_success"] = df["run_id"].map(
        lambda x: session_lookup.get(x, {}).get("workflow_success", None)
    )

    # Convert timestamps to datetime for daily analysis
    df["first_datetime"] = pd.to_datetime(df["first_timestamp"])
    df["date"] = df["first_datetime"].dt.date

    # Sort by timestamp for better visualization
    df = df.sort_values("first_timestamp")

    # Create daily spending analysis with breakdown
    daily_spending = (
        df.groupby("date")
        .agg(
            {
                "total_cost_usd": "sum",
                "total_api_calls": "sum",
                "total_tokens": "sum",
                "agent_cost_usd": "sum",
                "summarization_cost_usd": "sum",
                "agent_api_calls": "sum",
                "summarization_api_calls": "sum",
                "run_id": "count",
            }
        )
        .rename(columns={"run_id": "num_runs"})
    )

    # Create figure with subplots
    fig, ((ax1, ax2), (ax3, ax4), (ax5, ax6)) = plt.subplots(3, 2, figsize=(16, 18))
    fig.suptitle(
        "API Usage and Cost Analysis by Run ID", fontsize=16, fontweight="bold"
    )

    # 1. Total API Calls per Run ID
    ax1.bar(range(len(df)), df["total_api_calls"], color="skyblue", alpha=0.7)
    ax1.set_title("Total API Calls per Run ID")
    ax1.set_xlabel("Run Index")
    ax1.set_ylabel("Number of API Calls")
    ax1.grid(True, alpha=0.3)

    # Add run_id labels on x-axis (rotated for readability)
    ax1.set_xticks(range(0, len(df), max(1, len(df) // 10)))
    ax1.set_xticklabels(
        [
            df.iloc[i]["run_id"][:8] + "..."
            for i in range(0, len(df), max(1, len(df) // 10))
        ],
        rotation=45,
        ha="right",
    )

    # 2. Cost Breakdown per Run ID (Stacked)
    width = 0.8
    ax2.bar(
        range(len(df)),
        df["agent_cost_usd"],
        width,
        label="Agent Run",
        color="lightcoral",
        alpha=0.7,
    )
    ax2.bar(
        range(len(df)),
        df["summarization_cost_usd"],
        width,
        bottom=df["agent_cost_usd"],
        label="Summarization",
        color="lightsalmon",
        alpha=0.7,
    )
    ax2.set_title("Cost Breakdown (USD) per Run ID")
    ax2.set_xlabel("Run Index")
    ax2.set_ylabel("Cost (USD)")
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # Format y-axis to show cost in dollars
    ax2.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f"${x:.4f}"))

    ax2.set_xticks(range(0, len(df), max(1, len(df) // 10)))
    ax2.set_xticklabels(
        [
            df.iloc[i]["run_id"][:8] + "..."
            for i in range(0, len(df), max(1, len(df) // 10))
        ],
        rotation=45,
        ha="right",
    )

    # 3. Token Usage per Run ID
    width = 0.35
    x = np.arange(len(df))

    ax3.bar(
        x - width / 2,
        df["total_input_tokens"],
        width,
        label="Input Tokens",
        color="lightgreen",
        alpha=0.7,
    )
    ax3.bar(
        x + width / 2,
        df["total_output_tokens"],
        width,
        label="Output Tokens",
        color="orange",
        alpha=0.7,
    )
    ax3.set_title("Token Usage per Run ID")
    ax3.set_xlabel("Run Index")
    ax3.set_ylabel("Number of Tokens")
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    ax3.set_xticks(range(0, len(df), max(1, len(df) // 10)))
    ax3.set_xticklabels(
        [
            df.iloc[i]["run_id"][:8] + "..."
            for i in range(0, len(df), max(1, len(df) // 10))
        ],
        rotation=45,
        ha="right",
    )

    # 4. Cost vs API Calls Scatter Plot
    colors = ["green" if success else "red" for success in df["workflow_success"]]
    scatter = ax4.scatter(
        df["total_api_calls"], df["total_cost_usd"], c=colors, alpha=0.6, s=60
    )
    ax4.set_title("Cost vs API Calls (Green=Success, Red=Failed)")
    ax4.set_xlabel("Number of API Calls")
    ax4.set_ylabel("Total Cost (USD)")
    ax4.grid(True, alpha=0.3)

    # Add trend line
    if len(df) > 1:
        z = np.polyfit(df["total_api_calls"], df["total_cost_usd"], 1)
        p = np.poly1d(z)
        ax4.plot(df["total_api_calls"], p(df["total_api_calls"]), "r--", alpha=0.8)

    # 5. Daily Spending Over Time (Stacked)
    ax5.plot(
        daily_spending.index,
        daily_spending["agent_cost_usd"],
        marker="o",
        linewidth=2,
        markersize=6,
        color="darkred",
        label="Agent Run",
    )
    ax5.plot(
        daily_spending.index,
        daily_spending["summarization_cost_usd"],
        marker="s",
        linewidth=2,
        markersize=6,
        color="orange",
        label="Summarization",
    )
    ax5.plot(
        daily_spending.index,
        daily_spending["total_cost_usd"],
        marker="^",
        linewidth=2,
        markersize=6,
        color="black",
        label="Total",
        linestyle="--",
    )
    ax5.set_title("Daily Spending Over Time by Type")
    ax5.set_xlabel("Date")
    ax5.set_ylabel("Daily Cost (USD)")
    ax5.legend()
    ax5.grid(True, alpha=0.3)
    ax5.tick_params(axis="x", rotation=45)

    # Format y-axis to show cost in dollars
    ax5.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f"${x:.4f}"))

    # 6. Daily API Calls Over Time by Type
    ax6.plot(
        daily_spending.index,
        daily_spending["agent_api_calls"],
        marker="o",
        linewidth=2,
        markersize=6,
        color="darkblue",
        label="Agent Run",
    )
    ax6.plot(
        daily_spending.index,
        daily_spending["summarization_api_calls"],
        marker="s",
        linewidth=2,
        markersize=6,
        color="lightblue",
        label="Summarization",
    )
    ax6.plot(
        daily_spending.index,
        daily_spending["total_api_calls"],
        marker="^",
        linewidth=2,
        markersize=6,
        color="black",
        label="Total",
        linestyle="--",
    )
    ax6.set_title("Daily API Calls Over Time by Type")
    ax6.set_xlabel("Date")
    ax6.set_ylabel("Daily API Calls")
    ax6.legend()
    ax6.grid(True, alpha=0.3)
    ax6.tick_params(axis="x", rotation=45)

    plt.tight_layout()
    plt.show()

    # Print detailed statistics
    print("\n" + "=" * 80)
    print("DETAILED API USAGE & COST ANALYSIS")
    print("=" * 80)

    # Overall Summary with Cost Breakdown
    print(f"📊 OVERALL SUMMARY")
    print(f"   Total Runs Analyzed: {len(df):,}")
    print(f"   Date Range: {df['date'].min()} to {df['date'].max()}")
    print(f"   Total Days: {len(daily_spending)} days")
    print(f"   Total API Calls: {df['total_api_calls'].sum():,}")
    print(f"   Total Input Tokens: {df['total_input_tokens'].sum():,}")
    print(f"   Total Output Tokens: {df['total_output_tokens'].sum():,}")
    print(f"   Total Tokens: {df['total_tokens'].sum():,}")
    print(f"   Total Cost: ${df['total_cost_usd'].sum():.6f}")
    print(f"")
    print(f"🔍 COST BREAKDOWN BY TYPE")
    agent_cost_total = df["agent_cost_usd"].sum()
    summarization_cost_total = df["summarization_cost_usd"].sum()
    total_cost = df["total_cost_usd"].sum()

    print(
        f"   Agent Run Cost: ${agent_cost_total:.6f} ({agent_cost_total/total_cost*100:.1f}%)"
    )
    print(
        f"   Summarization Cost: ${summarization_cost_total:.6f} ({summarization_cost_total/total_cost*100:.1f}%)"
    )
    print(f"   Agent API Calls: {df['agent_api_calls'].sum():,}")
    print(f"   Summarization API Calls: {df['summarization_api_calls'].sum():,}")
    print(f"   Agent Tokens: {df['agent_tokens'].sum():,}")
    print(f"   Summarization Tokens: {df['summarization_tokens'].sum():,}")

    # Cost efficiency metrics
    if df["agent_api_calls"].sum() > 0:
        print(
            f"   Avg Cost per Agent API Call: ${agent_cost_total/df['agent_api_calls'].sum():.6f}"
        )
    if df["summarization_api_calls"].sum() > 0:
        print(
            f"   Avg Cost per Summarization API Call: ${summarization_cost_total/df['summarization_api_calls'].sum():.6f}"
        )

    # Per-Run Statistics with Percentiles
    print(f"\n💰 COST PER RUN STATISTICS")
    cost_percentiles = df["total_cost_usd"].quantile(
        [0.1, 0.25, 0.5, 0.75, 0.9, 0.95, 0.99]
    )
    print(f"   Mean Cost per Run: ${df['total_cost_usd'].mean():.6f}")
    print(f"   Median Cost per Run: ${df['total_cost_usd'].median():.6f}")
    print(f"   Standard Deviation: ${df['total_cost_usd'].std():.6f}")
    print(f"   Min Cost: ${df['total_cost_usd'].min():.6f}")
    print(f"   Max Cost: ${df['total_cost_usd'].max():.6f}")
    print(f"   10th Percentile: ${cost_percentiles[0.1]:.6f}")
    print(f"   25th Percentile: ${cost_percentiles[0.25]:.6f}")
    print(f"   75th Percentile: ${cost_percentiles[0.75]:.6f}")
    print(f"   90th Percentile: ${cost_percentiles[0.9]:.6f}")
    print(f"   95th Percentile: ${cost_percentiles[0.95]:.6f}")
    print(f"   99th Percentile: ${cost_percentiles[0.99]:.6f}")

    # API Calls Statistics with Percentiles
    print(f"\n📞 API CALLS PER RUN STATISTICS")
    api_percentiles = df["total_api_calls"].quantile(
        [0.1, 0.25, 0.5, 0.75, 0.9, 0.95, 0.99]
    )
    print(f"   Mean API Calls per Run: {df['total_api_calls'].mean():.1f}")
    print(f"   Median API Calls per Run: {df['total_api_calls'].median():.1f}")
    print(f"   Standard Deviation: {df['total_api_calls'].std():.1f}")
    print(f"   Min API Calls: {df['total_api_calls'].min()}")
    print(f"   Max API Calls: {df['total_api_calls'].max()}")
    print(f"   10th Percentile: {api_percentiles[0.1]:.0f}")
    print(f"   25th Percentile: {api_percentiles[0.25]:.0f}")
    print(f"   75th Percentile: {api_percentiles[0.75]:.0f}")
    print(f"   90th Percentile: {api_percentiles[0.9]:.0f}")
    print(f"   95th Percentile: {api_percentiles[0.95]:.0f}")
    print(f"   99th Percentile: {api_percentiles[0.99]:.0f}")

    # Token Statistics with Percentiles
    print(f"\n🔤 TOKEN USAGE PER RUN STATISTICS")
    token_percentiles = df["total_tokens"].quantile(
        [0.1, 0.25, 0.5, 0.75, 0.9, 0.95, 0.99]
    )
    print(f"   Mean Tokens per Run: {df['total_tokens'].mean():,.0f}")
    print(f"   Median Tokens per Run: {df['total_tokens'].median():,.0f}")
    print(f"   Standard Deviation: {df['total_tokens'].std():,.0f}")
    print(f"   Min Tokens: {df['total_tokens'].min():,}")
    print(f"   Max Tokens: {df['total_tokens'].max():,}")
    print(f"   10th Percentile: {token_percentiles[0.1]:,.0f}")
    print(f"   25th Percentile: {token_percentiles[0.25]:,.0f}")
    print(f"   75th Percentile: {token_percentiles[0.75]:,.0f}")
    print(f"   90th Percentile: {token_percentiles[0.9]:,.0f}")
    print(f"   95th Percentile: {token_percentiles[0.95]:,.0f}")
    print(f"   99th Percentile: {token_percentiles[0.99]:,.0f}")

    # Daily Spending Statistics with Breakdown
    print(f"\n📅 DAILY SPENDING ANALYSIS")
    print(f"   Mean Daily Spending: ${daily_spending['total_cost_usd'].mean():.6f}")
    print(f"   Median Daily Spending: ${daily_spending['total_cost_usd'].median():.6f}")
    print(f"   Max Daily Spending: ${daily_spending['total_cost_usd'].max():.6f}")
    print(f"   Min Daily Spending: ${daily_spending['total_cost_usd'].min():.6f}")
    print(f"   Total Days with Activity: {len(daily_spending)}")
    print(f"   Average Runs per Day: {daily_spending['num_runs'].mean():.1f}")
    print(
        f"   Average API Calls per Day: {daily_spending['total_api_calls'].mean():.0f}"
    )
    print(f"")
    print(f"🔍 DAILY COST BREAKDOWN")
    print(f"   Mean Daily Agent Cost: ${daily_spending['agent_cost_usd'].mean():.6f}")
    print(
        f"   Mean Daily Summarization Cost: ${daily_spending['summarization_cost_usd'].mean():.6f}"
    )
    print(
        f"   Mean Daily Agent API Calls: {daily_spending['agent_api_calls'].mean():.0f}"
    )
    print(
        f"   Mean Daily Summarization API Calls: {daily_spending['summarization_api_calls'].mean():.0f}"
    )

    # Calculate daily cost ratios
    daily_agent_ratio = (
        daily_spending["agent_cost_usd"] / daily_spending["total_cost_usd"] * 100
    )
    daily_summarization_ratio = (
        daily_spending["summarization_cost_usd"]
        / daily_spending["total_cost_usd"]
        * 100
    )
    print(f"   Average Daily Agent Cost Ratio: {daily_agent_ratio.mean():.1f}%")
    print(
        f"   Average Daily Summarization Cost Ratio: {daily_summarization_ratio.mean():.1f}%"
    )

    # Most expensive days
    top_expensive_days = daily_spending.nlargest(5, "total_cost_usd")
    print(f"\n🔥 TOP 5 MOST EXPENSIVE DAYS:")
    for date, row in top_expensive_days.iterrows():
        agent_pct = (
            row["agent_cost_usd"] / row["total_cost_usd"] * 100
            if row["total_cost_usd"] > 0
            else 0
        )
        summ_pct = (
            row["summarization_cost_usd"] / row["total_cost_usd"] * 100
            if row["total_cost_usd"] > 0
            else 0
        )
        print(
            f"   {date}: ${row['total_cost_usd']:.6f} | {row['num_runs']} runs | {row['total_api_calls']} API calls"
        )
        print(
            f"      Agent: ${row['agent_cost_usd']:.6f} ({agent_pct:.1f}%) | Summarization: ${row['summarization_cost_usd']:.6f} ({summ_pct:.1f}%)"
        )
        print(
            f"      Agent Calls: {row['agent_api_calls']} | Summarization Calls: {row['summarization_api_calls']}"
        )

    # Top 5 most expensive runs
    top_cost_runs = df.nlargest(5, "total_cost_usd")
    print(f"\n💸 TOP 5 MOST EXPENSIVE RUNS:")
    for _, row in top_cost_runs.iterrows():
        agent_pct = (
            row["agent_cost_usd"] / row["total_cost_usd"] * 100
            if row["total_cost_usd"] > 0
            else 0
        )
        summ_pct = (
            row["summarization_cost_usd"] / row["total_cost_usd"] * 100
            if row["total_cost_usd"] > 0
            else 0
        )
        print(
            f"   Run ID: {row['run_id'][:12]}... | Cost: ${row['total_cost_usd']:.6f} | API Calls: {row['total_api_calls']} | Date: {row['date']}"
        )
        print(
            f"      Agent: ${row['agent_cost_usd']:.6f} ({agent_pct:.1f}%) | Summarization: ${row['summarization_cost_usd']:.6f} ({summ_pct:.1f}%)"
        )
        print(
            f"      Agent Calls: {row['agent_api_calls']} | Summarization Calls: {row['summarization_api_calls']}"
        )

    # Top 5 runs with most API calls
    top_api_runs = df.nlargest(5, "total_api_calls")
    print(f"\n📈 TOP 5 RUNS WITH MOST API CALLS:")
    for _, row in top_api_runs.iterrows():
        print(
            f"   Run ID: {row['run_id'][:12]}... | API Calls: {row['total_api_calls']} | Cost: ${row['total_cost_usd']:.6f} | Date: {row['date']}"
        )

    # Success rate analysis
    success_rate = df["workflow_success"].value_counts()
    total_with_status = success_rate.sum()
    if total_with_status > 0:
        print(f"\n✅ SUCCESS RATE ANALYSIS:")
        print(
            f"   Successful Runs: {success_rate.get(True, 0)} ({success_rate.get(True, 0)/total_with_status*100:.1f}%)"
        )
        print(
            f"   Failed Runs: {success_rate.get(False, 0)} ({success_rate.get(False, 0)/total_with_status*100:.1f}%)"
        )

        # Cost comparison by success
        if True in success_rate.index and False in success_rate.index:
            successful_cost = df[df["workflow_success"] == True][
                "total_cost_usd"
            ].mean()
            failed_cost = df[df["workflow_success"] == False]["total_cost_usd"].mean()
            print(f"   Average Cost - Successful: ${successful_cost:.6f}")
            print(f"   Average Cost - Failed: ${failed_cost:.6f}")

    print("=" * 80)


def main():
    """Main function to run the analysis."""
    parser = argparse.ArgumentParser(description="Plot API usage and cost per run ID")
    parser.add_argument(
        "--save", action="store_true", help="Save plots to file instead of displaying"
    )
    parser.add_argument(
        "--output",
        type=str,
        default="api_usage_plots.png",
        help="Output filename for saved plots",
    )
    args = parser.parse_args()

    try:
        # Connect to database
        print("Connecting to database...")
        db = connect_to_database()

        # Get API usage data
        print("Retrieving API usage data...")
        api_data, session_data = get_api_usage_by_run_id(db)

        if not api_data:
            print(
                "No API usage data found. Make sure you have run some workflows that generated API calls."
            )
            return

        print(f"Found {len(api_data)} runs with API usage data")

        # Create visualizations
        if args.save:
            import matplotlib

            matplotlib.use("Agg")  # Use non-interactive backend

        create_visualizations(api_data, session_data)

        if args.save:
            plt.savefig(args.output, dpi=300, bbox_inches="tight")
            print(f"Plots saved to {args.output}")

    except Exception as e:
        print(f"Error: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
