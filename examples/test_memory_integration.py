#!/usr/bin/env python3
"""
Test script for memory integration with LangGraph workflow.

This script demonstrates how the memory-driven system prompt architecture works
by testing various queries that should trigger different memory types.
"""

import logging
import os
import sys
import uuid

# Add the parent directory to the Python path to allow absolute imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))

# Configure logging to output to both console and audit.log file
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("audit.log", mode="w", encoding="utf-8"),
    ],
)

from src.log_agent import LogIntelligenceAgentLangGraph


def test_memory_integration():
    """Test the memory integration with various query types."""

    # Create agent
    print("🚀 Initializing LangGraph agent with memory integration...", flush=True)
    try:
        agent = LogIntelligenceAgentLangGraph()
        print("✅ Agent initialized successfully!", flush=True)
    except Exception as e:
        print(f"❌ Failed to initialize agent: {e}", flush=True)
        import traceback

        traceback.print_exc()
        return

    # Test queries that should trigger different memory types
    test_queries = [
        # {
        #     "name": "Domain Workflow Memory Test",
        #     "description": "Should trigger error analysis workflow from domain memory",
        #     "query": "Show me all the errors from yesterday",
        #     "expected_memory": "domain_workflow"
        # },
        {
            "name": "Application Tracking Test",
            "description": "Should trigger application timeline workflow",
            "query": "What happened to application 01K079PTY61TFF3QNV5Z12WTV9?",
            "expected_memory": "domain_workflow",
        }
    ]

    print(f"\n📝 Running {len(test_queries)} memory integration tests...\n")

    for i, test in enumerate(test_queries, 1):
        print("=" * 80)
        print(f"TEST {i}: {test['name']}")
        print(f"Description: {test['description']}")
        print(f"Expected Memory Type: {test['expected_memory']}")
        print(f"Query: {test['query']}")
        print("=" * 80)

        # Generate unique session ID for each test
        session_id = f"test-session-{uuid.uuid4()}"

        try:
            print(f"🔍 Executing query...", flush=True)
            result = agent.run(
                question=test["query"], session_id=session_id, persona="business"
            )

            print(f"\n✅ SUCCESS - Result:", flush=True)
            print("-" * 40, flush=True)
            print(result, flush=True)
            print("-" * 40, flush=True)

        except Exception as e:
            print(f"\n❌ ERROR: {e}", flush=True)
            import traceback

            traceback.print_exc()

        print(f"\n{'='*80}\n")


def interactive_test():
    """Interactive testing mode where you can enter custom queries."""

    print("🚀 Starting interactive memory integration test...")
    agent = LogIntelligenceAgentLangGraph()

    print("\n" + "=" * 60)
    print("INTERACTIVE MEMORY TESTING MODE")
    print("=" * 60)
    print("Enter queries to test memory integration.")
    print("Type 'quit' or 'exit' to stop.")
    print("Type 'help' for example queries.")
    print("=" * 60 + "\n")

    while True:
        try:
            query = input("🔍 Enter your query: ").strip()

            if query.lower() in ["quit", "exit", "q"]:
                print("👋 Goodbye!")
                break

            if query.lower() == "help":
                print("\n📚 Example queries to test different memory types:")
                print("• 'Show me errors from yesterday' (domain workflow)")
                print("• 'What is context.application_id?' (field knowledge)")
                print("• 'Find declined applications' (translation mapping)")
                print("• 'Query optimization strategy' (agent guidance)")
                print("• 'Track application 12345' (application timeline)")
                print()
                continue

            if not query:
                print("⚠️ Please enter a query or 'quit' to exit.")
                continue

            # Generate session ID
            session_id = f"interactive-{uuid.uuid4()}"

            print(f"\n🧠 Processing query with memory consultation...")
            print(f"Session ID: {session_id}")

            result = agent.run(
                question=query, session_id=session_id, persona="business"
            )

            print("\n" + "=" * 60)
            print("📋 RESULT:")
            print("=" * 60)
            print(result)
            print("=" * 60 + "\n")

        except KeyboardInterrupt:
            print("\n\n👋 Interrupted by user. Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")
            print("Please try again with a different query.\n")


if __name__ == "__main__":
    print("🎯 Starting memory integration test script...", flush=True)

    import argparse

    parser = argparse.ArgumentParser(
        description="Test memory integration with LangGraph workflow"
    )
    parser.add_argument(
        "--mode",
        choices=["auto", "interactive"],
        default="auto",
        help="Test mode: 'auto' runs predefined tests, 'interactive' allows custom queries",
    )
    parser.add_argument(
        "--query", type=str, help="Single query to test (overrides mode)"
    )

    args = parser.parse_args()

    if args.query:
        # Single query mode
        print(f"🚀 Testing single query: {args.query}")
        agent = LogIntelligenceAgentLangGraph()
        session_id = f"single-test-{uuid.uuid4()}"

        try:
            result = agent.run(
                question=args.query, session_id=session_id, persona="business"
            )
            print(f"\n✅ Result:\n{result}")
        except Exception as e:
            print(f"❌ Error: {e}")

    elif args.mode == "interactive":
        interactive_test()
    else:
        test_memory_integration()
