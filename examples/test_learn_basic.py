#!/usr/bin/env python3
"""
Basic test to verify /learn command doesn't break normal functionality.
"""

import os
import sys

# Add the parent directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))


def test_learn_command_detection():
    """Test that /learn command is detected and processed correctly."""

    print("🧪 Testing /learn command detection...")

    try:
        from src.log_agent import LogIntelligenceAgentLangGraph

        # Create agent instance (this will test initialization)
        print("  ✓ Importing LogIntelligenceAgentLangGraph...")
        LogIntelligenceAgentLangGraph()
        print("  ✓ Agent initialization successful!")

        # Test normal question processing (should not trigger /learn)
        print("  🔍 Testing normal question processing...")
        normal_question = "What is the status of application 123?"

        # This should go through normal workflow, not /learn
        # We'll mock this since we don't have DB connection
        print(
            f"  ✓ Normal question '{normal_question}' would go through regular workflow"
        )

        # Test /learn command detection
        print("  🎓 Testing /learn command detection...")
        learn_command = "/learn"

        # Check if our logic would detect this correctly
        if learn_command.strip() == "/learn":
            print("  ✓ /learn command detection works correctly")
        else:
            print("  ❌ /learn command detection failed")
            return False

        # Test edge cases
        edge_cases = [
            "  /learn  ",  # with whitespace
            "/learn",  # exact match
            "regular question with /learn in middle",  # should not trigger
            "/LEARN",  # case sensitive check
        ]

        print("  🔍 Testing edge cases...")
        for case in edge_cases:
            if case.strip() == "/learn":
                result = "would trigger /learn"
            else:
                result = "would go through normal workflow"
            print(f"    '{case}' -> {result}")

        print("✅ All basic tests passed! /learn implementation looks good.")
        return True

    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_method_existence():
    """Test that all the new /learn methods exist and are callable."""

    print("🔍 Testing /learn method existence...")

    try:
        from src.log_agent import LogIntelligenceAgentLangGraph

        agent = LogIntelligenceAgentLangGraph()

        # Check if all the new methods exist
        methods_to_check = [
            "_handle_learn_command",
            "_session_already_learned",
            "_get_session_conversation_history",
            "_extract_memory_contexts_from_conversation",
            "_analyze_conversation_for_learnings",
            "_process_learnings_for_category",
            "_create_memory_from_learning",
            "_format_learning_results",
        ]

        for method_name in methods_to_check:
            if hasattr(agent, method_name):
                method = getattr(agent, method_name)
                if callable(method):
                    print(f"  ✓ {method_name} exists and is callable")
                else:
                    print(f"  ❌ {method_name} exists but is not callable")
                    return False
            else:
                print(f"  ❌ {method_name} does not exist")
                return False

        print("✅ All /learn methods exist and are callable!")
        return True

    except Exception as e:
        print(f"❌ Method existence test failed: {e}")
        return False


def test_workflow_unchanged():
    """Test that the main workflow creation still works."""

    print("🔧 Testing workflow creation...")

    try:
        from src.log_agent import LogIntelligenceAgentLangGraph

        agent = LogIntelligenceAgentLangGraph()

        # Test that workflow can still be created
        workflow = agent._create_workflow_graph()
        print("  ✓ Workflow graph creation successful")

        # Check that workflow is compiled
        if hasattr(workflow, "invoke"):
            print("  ✓ Workflow is properly compiled and has invoke method")
        else:
            print("  ❌ Workflow compilation issue")
            return False

        print("✅ Workflow creation unchanged and working!")
        return True

    except Exception as e:
        print(f"❌ Workflow test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 Starting basic /learn functionality tests...\n")

    tests = [
        test_learn_command_detection,
        test_method_existence,
        test_workflow_unchanged,
    ]

    passed = 0
    failed = 0

    for test in tests:
        print(f"\n{'='*60}")
        try:
            if test():
                passed += 1
                print(f"✅ {test.__name__} PASSED")
            else:
                failed += 1
                print(f"❌ {test.__name__} FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test.__name__} FAILED with exception: {e}")
        print(f"{'='*60}")

    print(f"\n🎯 Test Summary:")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📊 Total: {passed + failed}")

    if failed == 0:
        print("\n🎉 All tests passed! /learn implementation is working correctly.")
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please check the implementation.")
