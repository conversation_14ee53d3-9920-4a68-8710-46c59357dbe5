#!/usr/bin/env python3
"""
Test script to verify vector search functionality with Exact Nearest Neighbor.
"""

import os
import sys

# Add the parent directory to the Python path to allow absolute imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))

from src.env_config import EnvConfig
from src.memory import MemoryManager


def test_vector_search():
    """Test vector search functionality."""

    # Initialize config and memory manager
    config = EnvConfig.load()
    memory_manager = MemoryManager(config)

    print("Testing vector search functionality...")
    print(
        "This will attempt MongoDB Atlas Vector Search with ENN, then fall back if needed."
    )

    # Test search queries
    test_queries = [
        ("find errors from yesterday", "domain_workflow"),
        ("log document structure", "field_knowledge"),
        ("declined application", "translation_mapping"),
        ("query optimization strategy", "agent_guidance"),
        ("application tracking", None),  # Search all types
    ]

    for i, (query, type_filter) in enumerate(test_queries, 1):
        print(f"\n{i}. Testing query: '{query}' (filter: {type_filter or 'all types'})")

        try:
            results = memory_manager.search(query, type_filter=type_filter, top_k=3)

            if results:
                print(f"   Found {len(results)} results:")
                for result in results:
                    title = result.get("canonical_question") or result.get(
                        "title", "Unknown"
                    )
                    print(
                        f"   - [{result['type']}] {title} (similarity: {result['similarity_score']:.3f})"
                    )
            else:
                print("   No results found")

        except Exception as e:
            print(f"   Error: {e}")

    # Test memory statistics
    stats = memory_manager.get_memory_stats()
    print(f"\nMemory System Statistics:")
    print(f"Total memories: {stats['total_memories']}")
    print(f"By type: {stats['by_type']}")
    print(f"Average confidence: {stats['average_confidence']:.2f}")

    print("\nVector search test completed!")


if __name__ == "__main__":
    try:
        test_vector_search()
    except Exception as e:
        print(f"Error during vector search test: {e}")
        import traceback

        traceback.print_exc()
