#!/usr/bin/env python3
"""
Test script to verify memory search functionality works correctly.
"""

import os
import sys

# Add the parent directory to the Python path to allow absolute imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))

from src.env_config import EnvConfig
from src.memory import MemoryManager


def test_memory_search():
    """Test memory search functionality."""

    # Initialize config and memory manager
    config = EnvConfig.load()
    memory_manager = MemoryManager(config)

    print("Testing memory search functionality...")

    # Test 1: Search for domain workflow memory about errors
    print("\n1. Searching for error-related domain workflow memories:")
    error_results = memory_manager.search(
        "find errors from yesterday", type_filter="domain_workflow"
    )
    for result in error_results:
        print(
            f"   - {result['canonical_question']} (similarity: {result['similarity_score']:.3f})"
        )

    # Test 2: Search for field knowledge memory about log structure
    print("\n2. Searching for log structure knowledge:")
    structure_results = memory_manager.search(
        "log document structure", type_filter="field_knowledge"
    )
    for result in structure_results:
        print(f"   - {result['title']} (similarity: {result['similarity_score']:.3f})")

    # Test 3: Search for translation mapping memory about declines
    print("\n3. Searching for decline terminology:")
    decline_results = memory_manager.search(
        "declined application", type_filter="translation_mapping"
    )
    for result in decline_results:
        print(f"   - {result['title']} (similarity: {result['similarity_score']:.3f})")

    # Test 4: Search for agent guidance memory
    print("\n4. Searching for agent guidance:")
    guidance_results = memory_manager.search(
        "query optimization strategy", type_filter="agent_guidance"
    )
    for result in guidance_results:
        print(f"   - {result['title']} (similarity: {result['similarity_score']:.3f})")

    # Test 5: General search across all memory types
    print("\n5. General search for 'application tracking':")
    general_results = memory_manager.search(
        "What happened to application 1234567890", top_k=3
    )
    for result in general_results:
        print(
            f"   - [{result['type']}] {result.get('canonical_question', result.get('title', 'Unknown'))} (similarity: {result['similarity_score']:.3f})"
        )

    # Display memory statistics
    stats = memory_manager.get_memory_stats()
    print(f"\nMemory System Statistics:")
    print(f"Total memories: {stats['total_memories']}")
    print(f"By type: {stats['by_type']}")
    print(f"Average confidence: {stats['average_confidence']:.2f}")

    print("\nMemory search test completed successfully!")


if __name__ == "__main__":
    try:
        test_memory_search()
    except Exception as e:
        print(f"Error during memory search test: {e}")
        import traceback

        traceback.print_exc()
