# MongoDB Real-Time Chat Storage Implementation

## Overview

This implementation provides **persistent, real-time chat storage** using MongoDB, solving the ephemeral storage problem in Google Cloud Run environments.

## Architecture

### Three Collections Design

#### 1. `chat_sessions` Collection
Stores session metadata, status, and log summaries:
```javascript
{
  "_id": ObjectId("..."),
  "session_id": "session-1750785557691-xmdzlpq",
  "title": "What is the total number of logs...",
  "persona": "business",
  "user_id": "user-1749926326595-b18k9ef",
  "status": "active|completed|abandoned",
  "created_at": ISODate("2025-01-24T..."),
  "updated_at": ISODate("2025-01-24T..."),
  "message_count": 4,
  "last_activity": ISODate("2025-01-24T..."),
  "metadata": {
    "total_runtime_ms": 2140,
    "total_iterations": 3,
    "api_calls": 5,
    "efficiency_chars_per_second": 124.7,
    "run_id": "run-uuid-here",
    "log_summary": {
      "total_logs": 15,
      "error_count": 1,
      "api_calls": 3,
      "tool_executions": 2,
      "iterations": 2,
      "by_level": {"INFO": 12, "WARNING": 2, "ERROR": 1},
      "by_type": {"api_interaction": 3, "tool_execution": 2, "session_lifecycle": 2, "error": 1, "general": 7}
    }
  }
}
```

#### 2. `chat_messages` Collection
Stores individual messages in order:
```javascript
{
  "_id": ObjectId("..."),
  "session_id": "session-1750785557691-xmdzlpq",
  "message_index": 0,
  "type": "user|bot",
  "content": "What is the total number of errors?",
  "timestamp": ISODate("2025-01-24T..."),
  "metadata": {
    "input_length": 34,
    "user_id": "user-1749926326595-b18k9ef",
    "run_id": "run-uuid-here",
    "total_runtime_ms": 1250
  }
}
```

#### 3. `session_logs` Collection 🆕
Stores all agent processing logs for each session:
```javascript
{
  "_id": ObjectId("..."),
  "session_id": "session-1750785557691-xmdzlpq",
  "run_id": "run-uuid-here",
  "timestamp": ISODate("2025-01-24T..."),
  "log_level": "INFO|WARNING|ERROR",
  "message": "MongoDB query executed successfully",
  "message_type": "api_interaction|tool_execution|session_lifecycle|error|general",
  "details": {
    "collection": "service_logs",
    "query_time_ms": 45,
    "result_count": 150
  },
  "source": "intelligent_agent",
  "raw_log": {...}  // Complete original log entry
}
```

## Key Features

### ✅ Real-Time Storage
- **User messages** stored immediately when `/ask` endpoint is called
- **Agent logs** stored in real-time during processing via custom logging handler
- **Bot responses** stored when intelligent agent completes processing
- **Session metadata** updated in real-time with each interaction and log summary

### ✅ Cloud Run Compatible
- **Primary**: MongoDB (persistent across container restarts)
  - Chat sessions and messages
  - **🆕 Agent processing logs**
  - Real-time log summaries in session metadata
- **Fallback**: Local files + audit_trail.log (ephemeral, backward compatibility)

### ✅ Comprehensive API

#### Core Endpoints

**1. `/session/<session_id>` - Retrieve Complete Session**
```bash
GET /session/session-1750785557691-xmdzlpq
```
Returns:
```json
{
  "session_id": "session-1750785557691-xmdzlpq",
  "chat": {
    "title": "What is the total number of logs...",
    "history": [
      {"type": "user", "content": "What is the total number of errors?"},
      {"type": "bot", "content": "Based on analysis, there are 42 errors..."}
    ],
    "persona": "business",
    "message_count": 4,
    "created_at": "2025-01-24T...",
    "session_metadata": {...}
  },
  "logs": [...],  // From audit_trail.log if available
  "metadata": {
    "chat_data_source": "mongodb",
    "persistence_warning": "Audit trail logs are ephemeral..."
  }
}
```

**2. `/sessions` - List All Sessions**
```bash
GET /sessions?page=1&limit=10&status=active&user_id=user123&persona=business
```

**3. `/sessions/stats` - Session Statistics**
```bash
GET /sessions/stats
```

**4. `/session/<session_id>/logs` - Session Logs Only** 🆕
```bash
GET /session/session-123/logs?limit=50&log_types=error,api_interaction
```

**5. `/sessions/logs/stats` - Log Analytics** 🆕
```bash
GET /sessions/logs/stats
```

#### Chat Processing

**6. `/ask` - Enhanced with MongoDB Storage** 🆕
```bash
POST /ask
{
  "query": "What errors occurred today?",
  "session_id": "session-123",
  "persona": "business",
  "user_id": "user-456"
}
```

**Enhanced Flow:**
1. Store user message → MongoDB immediately
2. Set up real-time log capture for session
3. Process with intelligent agent (logs stored in real-time)
4. Store bot response → MongoDB when complete
5. Update session metadata with log summary
6. Return processed response to user

## Database Schema & Indexes

### Indexes Created Automatically
```javascript
// chat_sessions collection
db.chat_sessions.createIndex({"session_id": 1}, {unique: true})
db.chat_sessions.createIndex({"user_id": 1})
db.chat_sessions.createIndex({"created_at": 1})
db.chat_sessions.createIndex({"last_activity": 1})

// chat_messages collection
db.chat_messages.createIndex({"session_id": 1, "message_index": 1}, {unique: true})
db.chat_messages.createIndex({"timestamp": 1})
db.chat_messages.createIndex({"type": 1})

// session_logs collection 🆕
db.session_logs.createIndex({"session_id": 1, "timestamp": 1})
db.session_logs.createIndex({"run_id": 1})
db.session_logs.createIndex({"log_level": 1})
db.session_logs.createIndex({"message_type": 1})
```

## Implementation Details

### Files Modified

#### 1. `src/database_manager.py`
**Added Methods:**
- `create_chat_session()` - Create new session
- `store_user_message()` - Store user input immediately
- `store_bot_message()` - Store agent response with metadata
- `update_session_completion()` - Update session status and final metadata
- `get_chat_session()` - Retrieve complete session data
- `ensure_chat_indexes()` - Create necessary database indexes

#### 2. `app/routes.py`
**Modified Endpoints:**
- `/ask` - Enhanced to store user messages immediately to MongoDB
- `/session/<id>` - Enhanced to read from MongoDB first, local files as fallback
- Added `/sessions` - List sessions with filtering and pagination
- Added `/sessions/stats` - Overall session statistics

#### 3. `src/intelligent_agent.py`
**Enhanced `_log_session_completion()`:**
- Store bot response to MongoDB with run metadata
- Update session completion status
- Maintain backward compatibility with existing logging

#### 4. `app/__init__.py`
**App Initialization:**
- Added automatic MongoDB index creation on startup
- Graceful error handling if MongoDB is unavailable

## Error Handling & Fallbacks

### Graceful Degradation
```python
try:
    # Store to MongoDB
    db_manager.store_user_message(session_id, message, user_id)
except Exception as e:
    # Log error but continue processing
    logging.error(f"MongoDB storage failed: {e}")
    # Falls back to existing local file system
```

### Data Source Priority
1. **Primary**: MongoDB (persistent)
2. **Fallback**: Local JSON files (ephemeral in Cloud Run)
3. **Logs**: audit_trail.log (ephemeral, current session only)

## Testing

### Run Test Suite
```bash
python test_mongodb_chat.py
```

**Test Coverage:**
- ✅ Session creation
- ✅ User message storage
- ✅ Bot response storage
- ✅ Multi-message conversations
- ✅ Session completion updates
- ✅ Data retrieval and consistency
- ✅ Cleanup and data integrity

## Benefits

### For Cloud Run Deployment
- **✅ Persistent Storage**: Survives container restarts
- **✅ Scalability**: Multiple container instances can share session data
- **✅ Performance**: Fast MongoDB queries with proper indexing
- **✅ Reliability**: Automatic failover to local storage if MongoDB unavailable

### For Development/Operations
- **✅ Session Management**: List, filter, and analyze chat sessions
- **✅ Analytics**: Session statistics and usage patterns
- **✅ Debugging**: Complete conversation history with metadata
- **✅ Audit Trail**: Comprehensive logging with session correlation

## Configuration

### Environment Variables
No new environment variables needed - uses existing MongoDB configuration:
- `MONGODB_URI` - MongoDB connection string
- `MONGODB_DATABASE` - Database name
- `MONGODB_COLLECTION` - Main collection name (logs)

### MongoDB Collections
- `chat_sessions` - Session metadata (auto-created)
- `chat_messages` - Individual messages (auto-created)
- Existing log collections remain unchanged

## Migration Path

### Current State → Enhanced State
1. **Immediate**: New chats automatically stored in MongoDB
2. **Backward Compatible**: Existing local file chats still accessible via fallback
3. **Gradual**: Old sessions naturally migrate as users continue conversations
4. **Optional**: Bulk migration script can be created if needed

## Security Considerations

- **Session Isolation**: Each session_id is unique and isolated
- **User Privacy**: user_id stored for tracking but content not user-specific
- **Data Retention**: Consider implementing TTL indexes for data cleanup
- **Access Control**: MongoDB connection should use appropriate authentication

---

**Status**: ✅ **IMPLEMENTATION COMPLETE**

The system now provides comprehensive, persistent chat storage with real-time updates, Cloud Run compatibility, and full backward compatibility.
