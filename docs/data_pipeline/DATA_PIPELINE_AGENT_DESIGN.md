# Simple Data Pipeline Service Design (MVP)

## Overview
A simple service that receives MongoDB queries from LogIntelligenceAgent and creates scheduled Airflow pipelines to populate SQL tables.

## Problem Statement
- Users explore logs with `LogIntelligenceAgent` to identify valuable queries
- These queries need to be productionized into scheduled data pipelines
- Keep it simple: no new agent, just a service that accepts pipeline specifications

## Architecture

### System Flow
```
User → LogIntelligenceAgent (explores logs, refines query)
           ↓
       Pipeline Specification (JSON)
           ↓
       REST API → Airflow Service
           ↓
       Creates DAG → Scheduled Execution → SQL Database
```

## Deployment Architecture

### Components
```
┌─────────────────────────┐     ┌──────────────────────────┐
│   Alfred Application    │     │    Airflow Cluster       │
│                         │     │                          │
│  - LogIntelligenceAgent │────►│  - REST API (8081)       │
│  - Pipeline API Client  │     │  - Scheduler             │
│                         │     │  - Executor (Celery)     │
└─────────────────────────┘     │  - Worker Nodes          │
                                │  - DAGs Folder           │
                                └──────────────────────────┘
                                          │
                                          ▼
                                ┌──────────────────────────┐
                                │   Databases              │
                                │  - MongoDB (source)      │
                                │  - BigQuery (dest)       │
                                └──────────────────────────┘
```

### Where Airflow Runs

#### Option 1: Docker Compose (Start Here)
**Setup**: Containers on same VM as Alfred
**Persistence**:
- PostgreSQL container with local volume for metadata
- Redis container for task queue
- Local filesystem for DAGs
**Recovery**: Manual - restart containers, data persists in volumes
**Cost**: ~$50/month (shared with Alfred VM)
**Use when**: <10 pipelines, MVP phase, cost-sensitive
**Limitations**: Single point of failure, manual scaling, DIY backups

#### Option 2: Kubernetes (Scale Phase)
**Setup**: Airflow on GKE cluster with Helm chart
**Persistence**:
- Cloud SQL for metadata (managed PostgreSQL)
- Cloud Memorystore for Redis (managed)
- GCS bucket for DAGs
**Recovery**: Auto-restart pods, managed DB failover
**Cost**: ~$150-250/month
**Use when**: 10-50 pipelines, need auto-scaling, multi-team usage
**Benefits**: Horizontal scaling, better isolation, zone redundancy

#### Option 3: Cloud Composer (Enterprise)
**Setup**: Fully managed Airflow by Google
**Persistence**:
- Cloud SQL (automatic backups, replication)
- Cloud Memorystore (automatic persistence)
- Cloud Storage (multi-region replication)
- Cloud Logging (30-day retention)
**Recovery**: Automatic failover, point-in-time recovery
**Cost**: ~$300-500/month minimum
**Use when**: >50 pipelines, compliance requirements, zero maintenance
**Benefits**: No ops overhead, auto-scaling, SLA guarantees, built-in monitoring

## Pipeline Specification

### Minimal Input (JSON)
```json
{
  "pipeline_name": "application_status_daily",
  "mongodb_query": {
    "type": "find",
    "filter": {
      "context.application_id": "YOUR_APPLICATION_ID",
      "response.body.data.status": {"$exists": true},
      "timestamp": {
        "$gte": "{{data_interval_start}}",
        "$lt": "{{data_interval_end}}"
      }
    },
    "projection": {
      "_id": 1,
      "summary": 1,
      "timestamp": 1,
      "response.body.data.status": 1
    }
  },
  "source_collection": "events-summarized",
  "output_table": {
    "dataset": "alfred_datawarehouse",
    "table": "application_status_snapshots",
    "columns": [
      {"name": "event_id", "type": "STRING", "source": "_id"},
      {"name": "summary", "type": "STRING", "source": "summary"},
      {"name": "status", "type": "STRING", "source": "response.body.data.status"},
      {"name": "event_timestamp", "type": "TIMESTAMP", "source": "timestamp"},
      {"name": "pipeline_run_date", "type": "DATE", "source": "{{ds}}"}
    ]
  },
  "schedule": "0 2 * * *",  // Cron expression
  "write_mode": "append"     // append | replace | merge
}
```

## Airflow Service Setup

### 1. Pre-built DAG Factory
**Execution**: Runs every 30 seconds (Airflow's DAG refresh interval). Existing running jobs continue unaffected.

```python
# /opt/airflow/dags/pipeline_factory.py
import json
from pathlib import Path
from airflow import DAG
from airflow.operators.python import PythonOperator
from datetime import datetime, timedelta

# Watch for new pipeline specifications
PIPELINE_SPECS_DIR = Path("/opt/airflow/pipeline_specs")

def create_dag_from_spec(spec_file):
    """Dynamically create DAG from specification"""
    with open(spec_file) as f:
        spec = json.load(f)

    dag = DAG(
        spec['pipeline_name'],
        default_args={
            'owner': 'pipeline_service',
            'retries': 2,
            'retry_delay': timedelta(minutes=5)
        },
        schedule_interval=spec['schedule'],
        start_date=datetime(2024, 1, 1),
        catchup=False
    )

    # Create tasks...
    return dag

# Auto-discover and create DAGs
for spec_file in PIPELINE_SPECS_DIR.glob("*.json"):
    dag_id = spec_file.stem  # filename without extension (e.g., "user_pipeline.json" -> "user_pipeline")
    globals()[dag_id] = create_dag_from_spec(spec_file)
```

### 2. REST API Endpoint
```python
# /opt/airflow/plugins/pipeline_api.py
from flask import Blueprint, request, jsonify
import json
from pathlib import Path

pipeline_bp = Blueprint('pipeline_api', __name__)
PIPELINE_SPECS_DIR = Path("/opt/airflow/pipeline_specs")

@pipeline_bp.route('/api/v1/pipeline', methods=['POST'])
def create_pipeline():
    """Create new pipeline from specification"""
    spec = request.json

    # Validate specification
    errors = validate_spec(spec)
    if errors:
        return jsonify({"errors": errors}), 400

    # Save specification
    spec_file = PIPELINE_SPECS_DIR / f"{spec['pipeline_name']}.json"
    with open(spec_file, 'w') as f:
        json.dump(spec, f, indent=2)

    # Trigger DAG refresh
    return jsonify({
        "status": "created",
        "pipeline_name": spec['pipeline_name'],
        "dag_id": spec['pipeline_name']
    }), 201
```

## Integration with LogIntelligenceAgent

### In LogIntelligenceAgent
```python
def create_pipeline_from_query(self, session_id: str, run_id: str) -> dict:
    """Send refined query to pipeline service"""

    # Get the MongoDB query from session
    query = self.get_final_query(session_id, run_id)

    # Infer output schema from sample results
    sample_results = self.database_manager.query_mongodb(
        query_type=query['type'],
        pipeline=query.get('pipeline'),
        limit=10
    )

    output_columns = infer_columns(sample_results)

    # Create pipeline specification
    spec = {
        "pipeline_name": f"pipeline_{session_id}_{run_id}",
        "mongodb_query": query,
        "source_collection": self.database_manager.summarized_collection_name,
        "output_table": {
            "schema": "analytics",
            "table": f"results_{session_id}",
            "columns": output_columns
        },
        "schedule": "0 2 * * *",  # Daily at 2 AM
        "write_mode": "replace"
    }

    # Send to Airflow API
    response = requests.post(
        f"{self.config.AIRFLOW_API_URL}/api/v1/pipeline",
        json=spec,
        headers={"Authorization": f"Bearer {self.config.AIRFLOW_API_TOKEN}"}
    )

    return response.json()
```

## Docker Compose Setup (Development)

```yaml
# docker-compose.airflow.yml
version: '3.8'

services:
  postgres:
    image: postgres:13
    environment:
      POSTGRES_USER: airflow
      POSTGRES_PASSWORD: airflow
      POSTGRES_DB: airflow
    volumes:
      - postgres-db-volume:/var/lib/postgresql/data

  redis:
    image: redis:latest

  airflow-webserver:
    image: apache/airflow:2.7.0
    depends_on:
      - postgres
      - redis
    environment:
      AIRFLOW__CORE__EXECUTOR: CeleryExecutor
      AIRFLOW__DATABASE__SQL_ALCHEMY_CONN: postgresql+psycopg2://airflow:airflow@postgres/airflow
      AIRFLOW__CELERY__BROKER_URL: redis://redis:6379/0
      AIRFLOW__API__AUTH_BACKENDS: 'airflow.api.auth.backend.basic_auth'
    volumes:
      - ./airflow/dags:/opt/airflow/dags
      - ./airflow/plugins:/opt/airflow/plugins
      - ./airflow/pipeline_specs:/opt/airflow/pipeline_specs
    ports:
      - "8081:8080"  # Map to 8081 to avoid conflict with Alfred
    command: webserver

  airflow-scheduler:
    image: apache/airflow:2.7.0
    depends_on:
      - airflow-webserver
    volumes:
      - ./airflow/dags:/opt/airflow/dags
      - ./airflow/pipeline_specs:/opt/airflow/pipeline_specs
    environment:
      # Same as webserver
    command: scheduler

  airflow-worker:
    image: apache/airflow:2.7.0
    depends_on:
      - airflow-scheduler
    volumes:
      - ./airflow/dags:/opt/airflow/dags
      - ./airflow/pipeline_specs:/opt/airflow/pipeline_specs
    environment:
      # Same as webserver
    command: celery worker

volumes:
  postgres-db-volume:
```

## Setup Instructions

### 1. Start Airflow Service
```bash
# Create directories
mkdir -p airflow/{dags,plugins,pipeline_specs}

# Copy factory DAG and API plugin
cp pipeline_factory.py airflow/dags/
cp pipeline_api.py airflow/plugins/

# Start services
docker-compose -f docker-compose.airflow.yml up -d

# Initialize Airflow
docker-compose exec airflow-webserver airflow db init
docker-compose exec airflow-webserver airflow users create \
    --username admin \
    --password admin \
    --firstname Admin \
    --lastname User \
    --role Admin \
    --email <EMAIL>
```

### 2. Configure Connections
```bash
# Add MongoDB connection
docker-compose exec airflow-webserver airflow connections add \
    'mongo_default' \
    --conn-type 'mongo' \
    --conn-host 'mongodb://localhost:27017'

# Add BigQuery connection (using service account key)
docker-compose exec airflow-webserver airflow connections add \
    'bigquery_default' \
    --conn-type 'google_cloud_platform' \
    --conn-extra '{"extra__google_cloud_platform__key_path": "/opt/airflow/gcp/service-account-key.json", "extra__google_cloud_platform__project": "${GCP_PROJECT_ID}"}'
```

### 3. Environment Variables for Alfred
```bash
# .env.prod
AIRFLOW_API_URL=http://localhost:8081
AIRFLOW_API_TOKEN=<generate_token>  # Generated via: airflow users create --role Admin
GCP_PROJECT_ID=your-project-id
BIGQUERY_DATASET=alfred_datawarehouse
```

## MVP Implementation Tasks

### Phase 1: Basic Setup
- [ ] Deploy Airflow with Docker Compose
- [ ] Create pipeline factory DAG
- [ ] Implement REST API endpoint
- [ ] Test with sample MongoDB query

### Phase 2: Integration
- [ ] Add pipeline creation method to LogIntelligenceAgent
- [ ] Implement schema inference from query results
- [ ] Add validation for pipeline specifications
- [ ] Create UI button "Create Pipeline" in Alfred

### Phase 3: Production Ready
- [ ] Move to Kubernetes deployment
- [ ] Add authentication/authorization
- [ ] Implement pipeline versioning
- [ ] Add monitoring and alerting

## Example End-to-End Flow

1. **User explores logs in Alfred UI**
```
User: "Show me daily active users for app-123"
LogAgent: [Refines query through conversation]
```

2. **User clicks "Create Pipeline" button**
```python
# Alfred sends to Airflow API
POST /api/v1/pipeline
{
  "pipeline_name": "daily_active_users_app123",
  "mongodb_query": {...},
  "output_table": {...},
  "schedule": "0 2 * * *"
}
```

3. **Airflow creates and schedules DAG**
- DAG appears in Airflow UI
- Runs daily at 2 AM
- Populates PostgreSQL table

4. **User queries SQL table for dashboards**
```sql
SELECT * FROM `your-project.analytics.daily_active_users_app123`
WHERE date >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
```

## Data Type Mapping

### MongoDB to SQL Type Conversion
```python
# Simple mapping - MongoDB types are automatically converted
MONGODB_TO_BIGQUERY_TYPE_MAP = {
    # MongoDB Type -> BigQuery Type
    "string": "STRING",
    "int": "INTEGER",
    "long": "INTEGER",
    "double": "FLOAT",
    "decimal": "NUMERIC",
    "bool": "BOOLEAN",
    "date": "TIMESTAMP",
    "objectId": "STRING",
    "array": "REPEATED",  # BigQuery array type
    "object": "STRUCT",   # BigQuery nested object
}

def infer_bigquery_type(sample_value):
    """Infer BigQuery type from MongoDB value"""
    if isinstance(sample_value, str):
        return "STRING"
    elif isinstance(sample_value, bool):
        return "BOOLEAN"
    elif isinstance(sample_value, int):
        return "INTEGER"
    elif isinstance(sample_value, float):
        return "FLOAT"
    elif isinstance(sample_value, datetime):
        return "TIMESTAMP"
    elif isinstance(sample_value, dict):
        return "STRUCT"  # BigQuery nested object
    elif isinstance(sample_value, list):
        return "REPEATED"  # BigQuery array
    else:
        return "STRING"  # Default fallback
```

## Incremental Loading

### Date Range Handling in Pipeline
The key is to inject Airflow execution date variables into MongoDB queries at runtime.

#### Pipeline Specification with Incremental Loading
```json
{
  "pipeline_name": "user_activity_hourly",
  "mongodb_query": {
    "type": "aggregate",
    "pipeline": [
      {
        "$match": {
          "timestamp": {
            "$gte": "{{data_interval_start}}",  // Airflow template variable
            "$lt": "{{data_interval_end}}"       // Airflow template variable
          }
        }
      },
      {
        "$group": {
          "_id": "$context.user_id",
          "count": {"$sum": 1}
        }
      }
    ]
  },
  "schedule": "@hourly",  // or "0 * * * *" for hourly
  "incremental_config": {
    "enabled": true,
    "date_field": "timestamp",
    "lookback_hours": 1  // Process 1 hour of data per run
  },
  "output_table": {
    "dataset": "analytics",
    "table": "user_activity_hourly",
    "columns": [
      {"name": "user_id", "type": "STRING", "source": "_id"},
      {"name": "activity_count", "type": "INTEGER", "source": "count"},
      {"name": "interval_start", "type": "TIMESTAMP", "source": "{{data_interval_start}}"},
      {"name": "interval_end", "type": "TIMESTAMP", "source": "{{data_interval_end}}"}
    ]
  },
  "write_mode": "append"  // Always append for incremental
}
```

#### DAG Implementation for Incremental Loading
```python
def extract_from_mongodb(**context):
    """Extract data for specific time window"""
    spec = context['dag_run'].conf or json.loads(spec_json)

    # Get Airflow's data interval (new in Airflow 2.2+)
    data_interval_start = context['data_interval_start']
    data_interval_end = context['data_interval_end']

    # Replace template variables in MongoDB query
    query = spec['mongodb_query'].copy()
    query_str = json.dumps(query)
    query_str = query_str.replace('{{data_interval_start}}', data_interval_start.isoformat())
    query_str = query_str.replace('{{data_interval_end}}', data_interval_end.isoformat())
    query = json.loads(query_str)

    # Execute query
    mongo_hook = MongoHook(conn_id='mongo_default')
    results = mongo_hook.aggregate(
        collection=spec['source_collection'],
        pipeline=query['pipeline']
    )

    # Add interval metadata to each record
    for record in results:
        record['interval_start'] = data_interval_start
        record['interval_end'] = data_interval_end
        record['load_timestamp'] = datetime.utcnow()

    return results

# In the DAG
extract_task = PythonOperator(
    task_id='extract_mongodb',
    python_callable=extract_from_mongodb,
    provide_context=True
)
```

### Schedule Options
```python
SCHEDULE_PRESETS = {
    "@hourly": "0 * * * *",        # Every hour
    "@daily": "0 2 * * *",         # Daily at 2 AM
    "@weekly": "0 2 * * 0",        # Weekly on Sunday at 2 AM
    "@monthly": "0 2 1 * *",       # Monthly on 1st at 2 AM
    "business_hours": "0 9-17 * * 1-5",  # Every hour during business hours
    "every_6_hours": "0 */6 * * *",      # Every 6 hours
}
```

### Backfill Support
```python
# Allow backfilling historical data
def create_backfill_command(pipeline_name, start_date, end_date):
    """Generate Airflow CLI command for backfilling"""
    return f"""
    airflow dags backfill \\
        -s {start_date} \\
        -e {end_date} \\
        {pipeline_name}
    """
```

## Error Handling & Alerting

### Pipeline Error Configuration
**Alfred Integration**: Alerts POST to Alfred webhook endpoint for UI display
```json
{
  "pipeline_name": "user_activity_daily",
  "error_handling": {
    "retries": 3,
    "retry_delay_minutes": 5,
    "alert_on_failure": true,
    "alert_email": "<EMAIL>",
    "slack_webhook": "https://hooks.slack.com/services/xxx",
    "on_failure_callback": "alert_pipeline_owner"
  }
}
```

### DAG Error Handling Implementation
```python
from airflow.providers.email.operators.email import EmailOperator
from airflow.providers.slack.operators.slack_webhook import SlackWebhookOperator

def alert_on_failure(context):
    """Send alerts when pipeline fails"""
    dag_id = context['dag'].dag_id
    task_id = context['task'].task_id
    execution_date = context['execution_date']
    error = context.get('exception', 'Unknown error')

    # Send Slack alert
    slack_alert = SlackWebhookOperator(
        task_id='slack_alert',
        http_conn_id='slack_webhook',
        message=f"""
        :red_circle: Pipeline Failed
        *Pipeline:* {dag_id}
        *Task:* {task_id}
        *Time:* {execution_date}
        *Error:* {error}
        *Logs:* {context['task_instance'].log_url}
        """,
        channel='#data-alerts'
    )
    slack_alert.execute(context)

    # Send email alert
    email_alert = EmailOperator(
        task_id='email_alert',
        to=['<EMAIL>'],
        subject=f'Pipeline Failed: {dag_id}',
        html_content=f"""
        <h3>Pipeline Failure Alert</h3>
        <p><b>Pipeline:</b> {dag_id}</p>
        <p><b>Task:</b> {task_id}</p>
        <p><b>Execution Time:</b> {execution_date}</p>
        <p><b>Error:</b> {error}</p>
        <p><a href="{context['task_instance'].log_url}">View Logs</a></p>
        """
    )
    email_alert.execute(context)

# In DAG creation
dag = DAG(
    pipeline_name,
    default_args={
        'owner': spec.get('owner', 'data_team'),
        'retries': spec.get('error_handling', {}).get('retries', 3),
        'retry_delay': timedelta(
            minutes=spec.get('error_handling', {}).get('retry_delay_minutes', 5)
        ),
        'on_failure_callback': alert_on_failure,
        'email': [spec.get('error_handling', {}).get('alert_email')],
        'email_on_failure': True,
        'email_on_retry': False,
    }
)
```

### Monitoring Dashboard Queries
```sql
-- Pipeline health monitoring
CREATE VIEW pipeline_health AS
SELECT
    dag_id as pipeline_name,
    state,
    COUNT(*) as run_count,
    AVG(EXTRACT(EPOCH FROM (end_date - start_date))) as avg_duration_seconds,
    MAX(execution_date) as last_run,
    SUM(CASE WHEN state = 'failed' THEN 1 ELSE 0 END) as failure_count,
    ROUND(100.0 * SUM(CASE WHEN state = 'success' THEN 1 ELSE 0 END) / COUNT(*), 2) as success_rate
FROM dag_run
WHERE execution_date > NOW() - INTERVAL '7 days'
GROUP BY dag_id, state;

-- Data freshness check
CREATE VIEW data_freshness AS
SELECT
    table_schema,
    table_name,
    MAX(interval_end) as last_data_timestamp,
    NOW() - MAX(interval_end) as data_lag
FROM analytics.pipeline_metadata
GROUP BY table_schema, table_name;
```

## Complete Example: Hourly User Activity Pipeline

### LogIntelligenceAgent creates specification:
```python
def create_hourly_pipeline(self, session_id: str, run_id: str):
    """Create hourly pipeline from refined query"""

    spec = {
        "pipeline_name": f"user_activity_hourly_{session_id[:8]}",
        "mongodb_query": {
            "type": "aggregate",
            "pipeline": [
                {
                    "$match": {
                        "timestamp": {
                            "$gte": "{{data_interval_start}}",
                            "$lt": "{{data_interval_end}}"
                        },
                        "context.application_id": "app-123"
                    }
                },
                {
                    "$group": {
                        "_id": {
                            "user": "$context.user_id",
                            "hour": {"$dateToString": {
                                "format": "%Y-%m-%d %H:00",
                                "date": "$timestamp"
                            }}
                        },
                        "event_count": {"$sum": 1},
                        "unique_sessions": {"$addToSet": "$context.session_id"}
                    }
                },
                {
                    "$project": {
                        "user_id": "$_id.user",
                        "hour": "$_id.hour",
                        "event_count": 1,
                        "session_count": {"$size": "$unique_sessions"}
                    }
                }
            ]
        },
        "source_collection": self.config.SUMMARIZED_COLLECTION_NAME,  # From env config
        "schedule": "@hourly",
        "incremental_config": {
            "enabled": true,
            "date_field": "timestamp",
            "lookback_hours": 1
        },
        "output_table": {
            "dataset": "alfred_datawarehouse",
            "table": f"user_activity_hourly_{session_id[:8]}",
            "columns": [
                {"name": "user_id", "type": "STRING", "source": "user_id"},
                {"name": "hour", "type": "TIMESTAMP", "source": "hour"},
                {"name": "event_count", "type": "INTEGER", "source": "event_count"},
                {"name": "session_count", "type": "INTEGER", "source": "session_count"},
                {"name": "pipeline_run_time", "type": "TIMESTAMP", "source": "NOW()"}
            ]
        },
        "write_mode": "append",
        "error_handling": {
            "retries": 3,
            "retry_delay_minutes": 5,
            "alert_email": f"{self.get_user_email(session_id)}",
            "alert_on_failure": true
        }
    }

    # Send to Airflow
    response = requests.post(
        f"{self.config.AIRFLOW_API_URL}/api/v1/pipeline",
        json=spec,
        headers={"Authorization": f"Bearer {self.config.AIRFLOW_API_TOKEN}"}
    )

    return response.json()
```

## Benefits of This Approach
- **Simple**: No new agent, just API calls
- **Leverages Airflow**: Battle-tested orchestration
- **Flexible**: Can add complexity later
- **Maintainable**: Clear separation of concerns
- **Incremental**: Handles time-based data efficiently
- **Observable**: Built-in monitoring and alerting
