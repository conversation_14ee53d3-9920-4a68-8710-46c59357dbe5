# Tool Response Capture - Complete Flow

## Overview

This document explains **all the places** where tool responses are captured throughout the intelligent agent processing pipeline.

## Complete Tool Response Flow

### 1. **Tool Execution** 🔧
**Location**: `intelligent_agent.py` → `_execute_mongodb_query()`
**What happens**: Tool is called and returns raw response data

### 2. **Raw Tool Response Logging** ✅
**Location**: `LoggingUtils.log_tool_execution()`
**What's captured**:
```json
{
  "tool_name": "query_mongodb",
  "execution_timestamp": "2025-01-24T...",
  "args": {...},
  "execution_time_ms": 120.5,
  "success": true,
  "response": "Raw tool response data up to 2000 chars"
}
```
**Stored in**: MongoDB `session_logs` collection

### 3. **Tool Response Analytics** 📊
**Location**: `intelligent_agent.py` → Tool response analytics
**What's captured**:
```json
{
  "response_length": 15420,
  "execution_time_ms": 120.5,
  "iteration": 1,
  "tool_index": 1,
  "result_count": 150,
  "result_type": "list"
}
```

### 4. **Function Response Formatting** 🔄
**Location**: `intelligent_agent.py` → `Part.from_dict()`
**What happens**: Raw response converted to model-compatible format:
```python
Part.from_dict({
    "function_response": {
        "name": "query_mongodb",
        "response": {"content": tool_response_str}
    }
})
```

### 5. **🆕 Function Response to Model Logging** ✅
**Location**: `LoggingUtils.log_function_responses_to_model()`
**What's captured**:
```json
{
  "iteration": 1,
  "total_responses": 2,
  "function_responses": [
    {
      "index": 1,
      "function_name": "query_mongodb",
      "response_content": "Formatted response sent to model up to 1000 chars",
      "response_length": 15420
    }
  ]
}
```
**Message**: `📤 FUNCTION RESPONSES TO MODEL`
**Stored in**: MongoDB `session_logs` collection

### 6. **Model's Response to Tool Results** 🤖
**Location**: `LoggingUtils.log_model_response()`
**What's captured**: The model's reaction after receiving tool results
```json
{
  "step": "iteration_1_response",
  "text_content": "Model's analysis of tool results",
  "has_function_calls": false,
  "api_metrics": {...}
}
```

## Key Differences Between Captures

| Stage | Purpose | Content | Truncation Limit |
|-------|---------|---------|------------------|
| **Raw Tool Response** | Audit what tool returned | Original tool output | 2000 chars |
| **Function Response to Model** | See what model received | Formatted for model consumption | 1000 chars |
| **Model Response After Tools** | Model's reaction | Model's analysis/reasoning | 2000 chars |

## Before vs After Fix

### ❌ Before (Missing)
```
1. Tool executes → ✅ Raw response logged
2. Response formatted for model → ❌ NOT LOGGED
3. Formatted response sent to model → ❌ NOT LOGGED
4. Model processes tool results → ✅ Model response logged
```

### ✅ After (Complete)
```
1. Tool executes → ✅ Raw response logged
2. Response formatted for model → ✅ NOW LOGGED
3. Formatted response sent to model → ✅ NOW LOGGED
4. Model processes tool results → ✅ Model response logged
```

## Access Methods

### **Via MongoDB Logs**
```javascript
// Get all function responses sent to model
db.session_logs.find({
  "message": "📤 FUNCTION RESPONSES TO MODEL",
  "session_id": "session-123"
})

// Get specific iteration's function responses
db.session_logs.find({
  "message": "📤 FUNCTION RESPONSES TO MODEL",
  "details.iteration": 1
})
```

### **Via API Endpoints**
```bash
# Get all session logs (includes function responses)
GET /session/<session_id>

# Filter for tool-related logs
GET /session/<session_id>/logs?log_types=tool_execution

# Get specific run
GET /session/<session_id>/run/<run_id>
```

### **Via Log Viewer**
```bash
# View all tool-related logs
python view_model_logs.py tools

# Search for function responses
python view_model_logs.py search "FUNCTION RESPONSES"
```

## Example Complete Tool Flow

**1. Tool Execution Log**:
```json
{
  "message": "Tool execution completed",
  "details": {
    "tool_name": "query_mongodb",
    "response": "[{\"_id\":\"1\",\"error\":\"timeout\"},{\"_id\":\"2\",\"error\":\"network\"}]",
    "execution_time_ms": 120.5
  }
}
```

**2. Function Response to Model Log** (🆕):
```json
{
  "message": "📤 FUNCTION RESPONSES TO MODEL",
  "details": {
    "iteration": 1,
    "function_responses": [{
      "function_name": "query_mongodb",
      "response_content": "[{\"_id\":\"1\",\"error\":\"timeout\"},{\"_id\":\"2\",\"error\":\"network\"}]",
      "response_length": 67
    }]
  }
}
```

**3. Model Response After Tools Log**:
```json
{
  "message": "🔄 MODEL RESPONSE AFTER TOOLS (iteration 1)",
  "details": {
    "model_text": "I found 2 errors in the logs. Let me analyze the patterns...",
    "iteration": 1
  }
}
```

## Benefits

1. **Complete Audit Trail**: Every step of tool → model communication captured
2. **Debugging**: Can see exactly what data model received vs. what tool returned
3. **Format Verification**: Ensure tool responses are properly formatted for model
4. **Performance Analysis**: Track data size and processing at each stage
5. **Error Diagnosis**: Identify if issues are in tool execution vs. model processing

## Storage and Performance

- **Efficient Truncation**: Function responses limited to 1000 chars to balance detail with storage
- **Structured Data**: All captures stored in MongoDB with proper indexing
- **Searchable**: Can filter by iteration, function name, or content
- **Correlated**: All logs tied to specific run_id and session_id

This implementation ensures **complete transparency** in the tool execution → model communication pipeline!
