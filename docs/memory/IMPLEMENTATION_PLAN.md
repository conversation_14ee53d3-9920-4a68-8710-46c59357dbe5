# Memory Module Implementation Plan

## Overview
Build a persistent memory system that enables the log agent to learn from user interactions, store Q&A patterns, and improve responses over time through semantic search and user feedback.

**Key Innovation**: Replace the static 700+ line system prompt with a dynamic, memory-driven approach that constructs targeted prompts on-demand using semantic search to find only relevant knowledge for each query.

### Key Concepts

The memory system supports four types of memory designed to replace the static 700+ line system prompt with dynamic, targeted knowledge retrieval:

1. **Domain Workflow Memory**: Step-by-step analytical workflows for specific domain tasks (replaces procedural memory)
2. **Field Knowledge Memory**: Factual information about log fields and data structure (replaces declarative memory)
3. **Translation Mapping Memory**: Business terminology to technical log field mappings
4. **Agent Guidance Memory**: Internal guidance, rules, and decision-making logic for the agent

All memory types share a base interface with:
- Vector embeddings for semantic search
- Version control with rollback capability
- Feedback collection and teaching flows
- User attribution and history tracking

### Memory Workflow

#### Dynamic Prompt Construction
The core innovation is replacing static prompts with dynamic assembly:

```python
def construct_system_prompt(user_query: str, persona: str, context: dict) -> str:
    # 1. Search for relevant memories by type
    domain_workflows = memory_manager.search(user_query, type_filter="domain_workflow", top_k=3)
    field_knowledge = memory_manager.search(user_query, type_filter="field_knowledge", top_k=5)
    translation_mappings = memory_manager.search(user_query, type_filter="translation_mapping", top_k=3)
    agent_guidance = memory_manager.search(user_query, type_filter="agent_guidance", top_k=3)

    # 2. Construct dynamic prompt with only relevant knowledge
    prompt = f"""
You are an expert log intelligence agent analyzing logs from {context['database']}.{context['collection']}.

## Relevant Knowledge for This Query:

### Analytical Workflows:
{format_domain_workflows(domain_workflows)}

### Field Information:
{format_field_knowledge(field_knowledge)}

### Business Terminology:
{format_translation_mappings(translation_mappings)}

### Agent Guidance:
{format_agent_guidance(agent_guidance)}

## Current Context:
- Database: {context['database']}
- Collection: {context['collection']}
- Current time: {context['current_time']}
- Persona: {persona}

{get_persona_instructions(persona)}
"""
    return prompt
```

#### Memory Query Processing
1. **New Question Processing**:
   - Generate embedding for incoming question
   - Search across all 4 memory types using semantic similarity
   - If domain workflows found: Use analytical steps as context
   - If field knowledge found: Include relevant field information
   - If translation mappings found: Apply business term conversions
   - If agent guidance found: Include decision-making rules
   - If no matches: Create new memories after successful resolution

2. **Feedback Integration**:
   - Accumulate feedback until threshold reached (e.g., 3+ corrections or low confidence)
   - Process feedback to refine memory content by type
   - Create new version of memory with improved content
   - Track acceptance/rejection ratio for quality metrics

3. **Semantic Matching Engine**:
   ```pseudocode
   function findRelevantMemories(userQuestion, typeFilter):
       userEmbedding = generateEmbedding(userQuestion)
       candidateMemories = searchMemoryCollection(typeFilter)

       for each memory in candidateMemories:
           similarity = cosineSimilarity(userEmbedding, memory.embedding)
           if similarity > SIMILARITY_THRESHOLD (0.7):
               matches.add(memory, similarity)

       return sortByConfidenceAndSimilarity(matches)
   ```
## Phase 1: Foundation & Architecture

### Step 1.1: Memory Data Model Design
**Objective**: Define the complete data structure for all memory types

**Base Memory Unit Schema**:
All memory types inherit from a base MemoryUnit with common fields:
- `memory_id`: Unique identifier (UUID)
- `type`: Memory type ("procedural", "declarative")
- `version`: Version number for tracking evolution
- `embedding`: Vector embedding for semantic search (array of floats)
- `tags`: Categorization tags (array of strings)
- `created_at`, `updated_at`: Timestamps
- `created_by`: User who created this memory
- `history`: Array of version history objects
- `feedback`: Array of feedback objects with user ratings and notes

**Domain Workflow Memory Schema**:
Extends base MemoryUnit for analytical workflows:
- `canonical_question`: Generalized question representing this workflow pattern
- `answer_plan`: Structured plan with steps and strategy for analysis
- `example_phrasings`: Array of user questions that map to this canonical question
- `usage_count`: How many times this memory was referenced
- `acceptance_count`: Number of thumbs up received
- `rejection_count`: Number of thumbs down received

**Field Knowledge Memory Schema**:
Extends base MemoryUnit for factual domain knowledge:
- `title`: Title/name of the field or concept
- `body`: Detailed explanation or description
- `field_type`: Type of knowledge ("log_schema", "query_syntax", "data_processing", etc.)
- `aliases`: Alternative names/terms for this concept

**Translation Mapping Memory Schema**:
Extends base MemoryUnit for business terminology mapping:
- `title`: Business term formatted as "Business term: {term}"
- `body`: Technical mapping or definition
- `aliases`: Alternative phrasings of the business term

**Agent Guidance Memory Schema**:
Extends base MemoryUnit for internal agent rules and guidance:
- `title`: Title of the guidance rule or strategy
- `body`: Detailed guidance content and decision-making logic

**Testing Approach**:
- Create sample memory documents
- Validate schema with MongoDB
- Test serialization/deserialization
- Verify index performance

**Integration Points**:
- Extend existing DatabaseManager to handle memory collection
- Ensure compatibility with current MongoDB setup

### Step 1.2: Embedding Service Integration
**Objective**: Set up semantic search capabilities using Google's embedding service

**Detailed Implementation**:
- Use Google's `text-embedding-004` model via existing GenAI client
- Create embedding generation function with error handling
- Implement cosine similarity calculation for vector comparison
- Log embedding computation costs for tracking (no aggressive caching needed initially)
- Handle embedding API rate limits and failures gracefully

**Testing Approach**:
- Test embedding generation for various question types
- Validate cosine similarity calculations
- Test similarity thresholds with sample data
- Benchmark embedding generation performance

**Integration Points**:
- Leverage existing `get_genai_manager` for client access
- Integrate with current retry and error handling patterns

### Step 1.3: Memory Manager Core Module
**Objective**: Create the central memory management class

**Detailed Implementation**:
- Create `MemoryStore` interface with core operations:
  - `search(query, type_filter, top_k)`: Vector similarity search across memory types
  - `get(memory_id)`: Retrieve specific memory unit
  - `create(memory)`: Store new memory unit
  - `update(memory_id, changes, user)`: Update existing memory with version tracking
  - `rollback(memory_id, to_version)`: Rollback to previous version
- Create `MemoryManager` class that implements MemoryStore interface
- Implement memory type-specific operations:
  - `teach_domain_workflow(question, logic, example_phrasings, created_by)`: Create domain workflow memory
  - `teach_field_knowledge(title, body, field_type, aliases, created_by)`: Create field knowledge memory
  - `teach_translation_mapping(business_term, technical_mapping, aliases, created_by)`: Create translation mapping memory
  - `teach_agent_guidance(title, body, created_by)`: Create agent guidance memory
- Add MongoDB indexing for efficient queries across all memory types
- Implement unified versioning system with rollback capability

**Testing Approach**:
- Unit tests for each CRUD operation
- Test memory search with various similarity thresholds
- Validate version control functionality
- Test concurrent access scenarios

**Integration Points**:
- Follow existing module patterns in src/
- Use same configuration and logging systems
- Maintain consistency with current error handling

## Phase 2: LangGraph Workflow Integration

### Step 2.1: Memory Consultation Node
**Objective**: Add memory lookup step to the LangGraph workflow

**Detailed Implementation**:
- Create new workflow node: `consult_memory`
- Position between `plan` and `execute` nodes in workflow
- Memory consultation logic:
  - Search for domain workflow memories matching user question pattern
  - Search for field knowledge memories for domain-specific information
  - Search for translation mapping memories to convert business terms to technical fields
  - Search for agent guidance memories for decision-making rules
  - Compile all relevant memories into structured context:
    - Domain workflows: Include analytical steps and strategies
    - Field knowledge: Include field descriptions and technical details
    - Translation mappings: Include business-to-technical term conversions
    - Agent guidance: Include rules and optimization strategies
  - Replace static 700+ line prompt with dynamic, targeted context injection

**Testing Approach**:
- Test memory node integration in isolation
- Verify workflow continues correctly with/without memory matches
- Test memory context injection into prompts
- Validate performance impact on workflow execution

**Integration Points**:
- Extend `PlanExecuteState` to include memory context
- Modify existing prompt templates to include memory information
- Ensure backward compatibility with current workflow

### Step 2.2: Session-Based Learning System
**Objective**: Enable learning from complete user conversations through `/learn` command

**Detailed Implementation**:
- Detect `/learn` command in user input during normal workflow
- Session learning logic:
  - Retrieve complete conversation history for the session
  - Extract all memory contexts used throughout the session
  - Use LLM to analyze conversation and identify missing knowledge gaps
  - Categorize learnings into 4 memory types (domain_workflow, field_knowledge, translation_mapping, agent_guidance)
  - For each learning: semantic search existing memories → create new or add feedback
  - Prevent duplicate learning sessions (only allow once per session)
  - Mark session as "learned from" to prevent future `/learn` commands

**LLM Analysis Process**:
- Analyze conversation between user and log analyzer agent
- Identify knowledge that was missing from original memory context
- Focus on gaps that caused confusion or required user guidance
- Organize findings by memory type with specific examples
- Only identify reusable patterns worth storing

**Storage Decision Logic**:
```python
if similar_memory_exists(similarity > 0.8):
    add_feedback(memory_id, "knowledge_gap_identified", learning_context)
else:
    create_new_memory(learning)
```

**Command Integration**:
- Intercept `/learn` in user input processing
- Check if session already learned from
- Execute learning process and return summary of memories created/updated
- Handle as special command (not passed to normal workflow)

**Testing Approach**:
- Test `/learn` command detection and processing
- Verify LLM gap analysis accuracy
- Test feedback vs new memory creation logic
- Validate session learning prevention (once per session)

**Integration Points**:
- Add command detection to input processing
- Leverage existing conversation history storage
- Use existing memory management system
- Non-blocking error handling with user feedback

## Phase 3: Feedback System

### Step 3.1: Feedback Collection
**Objective**: Collect user feedback on memory-based responses

**How to Collect Feedback**:
- Add UI components after each agent response:
  - Thumbs up/down buttons
  - "Improve this response" text input field
  - "This is wrong" button with correction field
- Associate feedback with specific memory_id that was used in response
- Track session_id and user_id for attribution

**What to Collect as Feedback**:
```json
{
  "feedback_id": "uuid",
  "memory_id": "uuid",
  "session_id": "string",
  "user_id": "string",
  "feedback_type": "thumbs_up|thumbs_down|text_correction|wrong_answer",
  "content": "string", // for text corrections
  "timestamp": "ISO datetime",
  "context": {
    "original_question": "string",
    "agent_response": "string",
    "memory_used": {...} // snapshot of memory that was used
  }
}
```

### Step 3.2: Memory Refinement Triggers
**Objective**: Determine when to refine memory based on feedback

**Refinement Triggers**:
1. **Immediate triggers**:
   - Any "wrong_answer" feedback with correction
   - 3+ "thumbs_down" within 24 hours
   - Confidence score drops below 0.3

2. **Batch triggers**:
   - Weekly review of memories with mixed feedback
   - Monthly review of low-usage memories

3. **Manual triggers**:
   - Admin/moderator can trigger refinement
   - User requests memory improvement

### Step 3.3: Memory Refinement Process
**Objective**: Improve memory based on accumulated feedback

**Refinement Steps**:
1. **Collect feedback for memory_id**:
   - Get all feedback entries for the memory
   - Group by feedback_type
   - Analyze text corrections for common patterns

2. **Generate refinement proposal**:
   - For domain workflow memory: Update answer_plan steps and strategy based on corrections
   - For field knowledge memory: Update body/title based on corrections
   - For translation mapping memory: Update mappings and aliases based on corrections
   - For agent guidance memory: Update guidance rules and decision logic based on corrections

3. **Create new version**:
   - Increment version number
   - Copy base memory with proposed changes
   - Set parent_memory_id to original
   - Reset confidence to 0.6 (slightly above neutral)
   - Mark as is_latest_version=true

4. **Atomic version switch**:
   ```javascript
   // Transaction to ensure atomic update
   session.startTransaction();
   try {
     // Update old version to not be latest
     db.memory.updateOne(
       {memory_id: parent_id},
       {$set: {is_latest_version: false}}
     );
     // Insert new version as latest
     db.memory.insertOne(new_version);
     session.commitTransaction();
   } catch (error) {
     session.abortTransaction();
   }
   ```

### Step 3.4: Version Management
**Objective**: Manage memory versions and ensure latest is used

**Version Selection Logic**:
```python
def get_active_memory(memory_id):
    return db.memory.find_one({
        "$or": [
            {"memory_id": memory_id, "is_latest_version": True},
            {"parent_memory_id": memory_id, "is_latest_version": True}
        ]
    })
```

**Rollback Process**:
1. **Identify target version**:
   - Get version history for memory
   - Select target version to rollback to

2. **Atomic rollback**:
   ```javascript
   session.startTransaction();
   try {
     // Mark current version as not latest
     db.memory.updateOne(
       {memory_id: current_id},
       {$set: {is_latest_version: false}}
     );
     // Mark target version as latest
     db.memory.updateOne(
       {memory_id: target_id},
       {$set: {is_latest_version: true}}
     );
     session.commitTransaction();
   } catch (error) {
     session.abortTransaction();
   }
   ```

### Step 3.5: Concurrency and Conflict Resolution
**Objective**: Handle concurrent feedback and updates safely

**Concurrency Control**:
1. **Optimistic locking**:
   - Use version numbers in update conditions
   - Retry on version conflicts

2. **Feedback queuing**:
   - Queue feedback updates for processing
   - Process in order to avoid conflicts

3. **Memory update locks**:
   - Acquire lock before refinement process
   - Release after version switch completes

**Conflict Resolution**:
1. **Feedback conflicts**:
   - If multiple users provide conflicting corrections simultaneously
   - Merge corrections if possible, otherwise trigger manual review

2. **Version conflicts**:
   - If two refinements happen simultaneously
   - Keep both versions, mark newer one as latest
   - Log conflict for manual review

### Step 3.6: Confidence Score Updates
**Objective**: Maintain accurate confidence scores based on feedback

**Confidence Update Rules**:
```python
def update_confidence(memory_id, feedback_type):
    memory = get_memory(memory_id)

    if feedback_type == "thumbs_up":
        memory.acceptance_count += 1
        memory.confidence_score = min(1.0, memory.confidence_score + 0.05)

    elif feedback_type == "thumbs_down":
        memory.rejection_count += 1
        memory.confidence_score = max(0.0, memory.confidence_score - 0.1)

    elif feedback_type == "wrong_answer":
        memory.rejection_count += 1
        memory.confidence_score = max(0.0, memory.confidence_score - 0.2)
        # Trigger immediate refinement if confidence drops below 0.3
        if memory.confidence_score < 0.3:
            trigger_refinement(memory_id)

    # Calculate confidence based on acceptance ratio
    total_feedback = memory.acceptance_count + memory.rejection_count
    if total_feedback > 0:
        acceptance_ratio = memory.acceptance_count / total_feedback
        # Blend algorithmic score with acceptance ratio
        memory.confidence_score = (memory.confidence_score * 0.7) + (acceptance_ratio * 0.3)

    save_memory(memory)
```

**Testing Approach**:
- Test concurrent feedback submission
- Verify atomic version switches
- Test rollback functionality
- Validate confidence score calculations
- Test conflict resolution scenarios

**Integration Points**:
- Add feedback API endpoints to existing web interface
- Integrate with session tracking for user attribution
- Add memory management UI for admins
- Monitor refinement triggers and success rates


## Testing Strategy

### Unit Testing
- Individual method testing for MemoryManager
- Mock external dependencies (MongoDB, GenAI API)
- Test edge cases and error conditions
- Validate data serialization/deserialization

### Integration Testing
- Test memory system with real MongoDB instance
- Validate embedding API integration
- Test complete feedback workflow end-to-end
- Verify LangGraph workflow integration

### Performance Testing
- Benchmark memory search performance
- Test system under high query load
- Validate embedding generation rate limits
- Measure workflow performance impact

### User Acceptance Testing
- Test feedback UI with sample interactions
- Validate memory improvement over time
- Test rollback functionality with users
- Verify learning effectiveness

## Risk Mitigation

### Technical Risks
- **Embedding API failures**: Implement fallback to keyword search
- **MongoDB performance**: Add proper indexing and query optimization
- **Memory quality degradation**: Implement quality scoring and cleanup
- **Version conflicts**: Add conflict resolution and merging logic

### Data Risks
- **Memory storage growth**: Implement automated cleanup and archival
- **Embedding costs**: Cache embeddings and batch processing
- **Data consistency**: Add transaction support for memory operations
- **Privacy concerns**: Implement memory anonymization options

## Implementation Order

1. **Phase 1**: Foundation (Memory data model, embedding service, core MemoryManager)
2. **Phase 2**: LangGraph Integration (Memory consultation and response storage nodes)
3. **Phase 3**: Feedback System (User feedback processing and memory refinement)
4. **Testing & Refinement**
5. **Documentation & Deployment**

## Technical Specifications

### Memory Schemas

#### Base Memory Unit
```json
{
  "memory_id": "uuid4-string",
  "type": "domain_workflow|field_knowledge|translation_mapping|agent_guidance",
  "version": 1,
  "embedding": [0.1, 0.2, ...],
  "tags": ["log_analysis", "error_debugging"],
  "created_at": "ISO datetime",
  "updated_at": "ISO datetime",
  "created_by": "user_id",
  "history": [
    {
      "version": 1,
      "body": {...},
      "updated_by": "user_id",
      "timestamp": "ISO datetime"
    }
  ],
  "feedback": [
    {
      "user_id": "string",
      "rating": "thumbs_up|thumbs_down|wrong_answer",
      "notes": "string",
      "timestamp": "ISO datetime"
    }
  ]
}
```

#### Domain Workflow Memory Example
```json
{
  "type": "domain_workflow",
  "canonical_question": "Show me all the errors from yesterday",
  "embedding": [0.1, 0.2, ...],
  "answer_plan": {
    "steps": [
      "Identify Error Scope: Determine time range and service filters for error investigation",
      "Gather Error Overview: Query for error logs using status_code >= 400 with summary projection to get overview",
      "Analyze Error Patterns: Review summary results to identify common error types, affected services, and frequency patterns",
      "Investigate Critical Errors: Drill down to full logs by _id for high-impact or recurring errors requiring detailed analysis",
      "Synthesize Findings: Present error analysis with patterns, root causes, and impact assessment"
    ],
    "strategy": "Always use summary fields for large result sets to prevent context overflow"
  },
  "example_phrasings": [
    "Show me all errors from yesterday",
    "Find yesterday's error logs",
    "What errors occurred yesterday?",
    "Give me error logs from the previous day"
  ],
  "usage_count": 5,
  "acceptance_count": 8,
  "rejection_count": 2,
  "version": 1
}
```

#### Field Knowledge Memory Example
```json
{
  "type": "field_knowledge",
  "title": "context.application_id field",
  "body": "Unique identifier for an application being processed. Use for tracking application lifecycle across services.",
  "field_type": "log_schema",
  "aliases": ["app id", "application identifier", "application ID"],
  "embedding": [0.1, 0.2, ...],
  "tags": ["logs", "application", "schema"],
  "version": 1
}
```

#### Translation Mapping Memory Example
```json
{
  "type": "translation_mapping",
  "title": "Business term: declined application",
  "body": "response.body.data.status == 'DECLINED'",
  "aliases": ["declined", "rejected", "denial", "application decline"],
  "embedding": [0.1, 0.2, ...],
  "tags": ["business", "status", "translation"],
  "version": 1
}
```

#### Agent Guidance Memory Example
```json
{
  "type": "agent_guidance",
  "title": "Query optimization thresholds",
  "body": "Use full logs for <10 results, summary+key fields for 10-50 results, summary-only for >50 results",
  "embedding": [0.1, 0.2, ...],
  "tags": ["guidance", "optimization", "query_strategy"],
  "version": 1
}
```

### MongoDB Indexes
```javascript
// Core indexes for memory collection
db.memory.createIndex({"memory_id": 1}, {unique: true})
db.memory.createIndex({"type": 1})
db.memory.createIndex({"tags": 1})
db.memory.createIndex({"created_by": 1})
db.memory.createIndex({"updated_at": 1})
db.memory.createIndex({"version": -1})

// Text search indexes
db.memory.createIndex({"canonical_question": "text"})
db.memory.createIndex({"title": "text"})
db.memory.createIndex({"body": "text"})

// Field-specific indexes
db.memory.createIndex({"field_type": 1})
db.memory.createIndex({"aliases": 1})

// Vector search index (if using MongoDB Atlas)
db.memory.createSearchIndex({
  "name": "memory_vector_search",
  "definition": {
    "fields": [{
      "type": "vector",
      "path": "embedding",
      "numDimensions": /* TODO: Get from embedding model (e.g., 1536 for OpenAI, 768 for text-embedding-004) */,
      "similarity": "cosine"
    }]
  }
})
```

### API Endpoints
```
POST /api/memory/feedback
- Submit user feedback for a memory

GET /api/memory/search?q={question}&limit={n}
- Search similar memories

GET /api/memory/stats
- Get memory system statistics

POST /api/memory/rollback/{memory_id}/{version}
- Rollback memory to previous version
```

### LangGraph Workflow Changes
```python
# Updated workflow structure with memory consultation
workflow.add_node("plan", plan_step)
workflow.add_node("consult_memory", consult_memory_step)  # NEW - Phase 2.1
workflow.add_node("execute", execute_step)
workflow.add_node("agent", call_agent)
workflow.add_node("tools", debug_tool_node)
workflow.add_node("process_result", process_step_result)
workflow.add_node("finish", finish_step)

# Memory consultation integrated into workflow
workflow.add_edge(START, "consult_memory")  # NEW
workflow.add_edge("consult_memory", "plan")  # NEW
workflow.add_edge("plan", "execute")
# ... rest of workflow unchanged

# Learning happens via /learn command processing (outside workflow)
# - Intercept /learn in input processing
# - Execute session learning analysis
# - Return learning summary to user
```

## Implementation Notes

Based on feedback:
- **Memory Scope**: Global memory across all users
- **Embedding Costs**: Track costs through logging, no aggressive caching initially
- **Feedback UI**: Thumbs up/down buttons + prompts for text feedback
- **Version Control**: Simple parent-child versioning without branching
- **Quality Metrics**: Track acceptance vs rejection ratio for canonical questions
- **Performance**: Monitor and optimize as needed during implementation

The plan is now ready for implementation with all key questions addressed.
