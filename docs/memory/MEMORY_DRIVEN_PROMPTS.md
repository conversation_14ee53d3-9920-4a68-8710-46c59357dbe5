# Memory-Driven System Prompt Architecture

## Overview

This document outlines the proposed architecture for transforming the current static system prompt into a dynamic, memory-driven system that uses semantic search to provide relevant context for each user query.

## Current State Analysis

### Existing System Prompt Structure (`src/prompt_manager.py`)

The current system prompt contains approximately 700+ lines of static knowledge across several categories:

1. **Log structure documentation** (lines 36-180)
2. **Query optimization strategies** (lines 540-597)
3. **Task management workflows** (lines 304-538)
4. **MongoDB query patterns** (lines 73-175)
5. **Persona-specific instructions** (lines 10-34)
6. **Field mappings and business rules** (throughout)

### Problems with Current Approach

1. **Context window usage**: Large static prompt consumes significant context space
2. **Maintenance overhead**: Updates require modifying large prompt strings
3. **Irrelevant information**: User queries only need subset of knowledge, but get everything
4. **Knowledge silos**: Related information scattered across different prompt sections
5. **No learning capability**: Static knowledge cannot be updated based on user feedback

## Proposed Memory-Driven Architecture

### Core Concept

Replace the static system prompt with a dynamic system that:
1. **Stores knowledge as searchable memories**
2. **Uses semantic search** to find relevant memories for each query
3. **Constructs minimal, targeted system prompts** on-demand
4. **Enables continuous learning** through memory updates

### Memory Types Overview

| Memory Type | Key Fields | Schema | Purpose | Example |
|-------------|------------|---------|---------|---------|
| **Domain Workflow** | `canonical_question`, `answer_plan`, `example_phrasings` | Procedural structure with step-by-step plans | Store analytical workflows for specific domain tasks | "How to analyze error logs" |
| **Field Knowledge** | `title`, `body`, `field_type`, `aliases` | Declarative structure with descriptions | Store factual information about log fields and data structure | "context.application_id field description" |
| **Translation Mapping** | `title`, `body`, `aliases` | Declarative structure with mappings | Map business terminology to technical log fields | "declined application" → status field |
| **Agent Guidance** | `title`, `body` | Declarative structure with guidance rules | Store internal guidance, rules, and decision-making logic for the agent | "Query optimization thresholds" |

### Memory Examples by Type

#### 1. Field Knowledge Memories

**Purpose**: Store factual information about log structure and fields

**Examples**:
- **Field Documentation**:
  - Title: "context.application_id field"
  - Body: "Unique identifier for an application being processed. Use for tracking application lifecycle across services."
  - Aliases: ["app id", "application identifier", "application ID"]
  - Field_type: "log_schema"

- **Data Structure Rules**:
  - Title: "Nested field querying"
  - Body: "Use dot notation for nested fields (e.g., `context.application_id`, `request.body.applicant.email`)"
  - Field_type: "query_syntax"

- **Timestamp Handling**:
  - Title: "Timestamp conversion requirement"
  - Body: "All timestamps stored as epoch or UTC must be converted to New York timezone (EST/EDT) before user display"
  - Field_type: "data_processing"

#### 2. Domain Workflow Memories

**Purpose**: Store step-by-step approaches for analyzing specific types of log data

**Examples**:
- **Error Analysis**:
  - Canonical_question: "Analyze error logs?"
  - Answer_plan: {
    "step1": "Identify Error Scope: Determine time range and service filters for error investigation",
    "step2": "Gather Error Overview: Query for error logs using status_code >= 400 with summary projection to get overview",
    "step3": "Analyze Error Patterns: Review summary results to identify common error types, affected services, and frequency patterns",
    "step4": "Investigate Critical Errors: Drill down to full logs by _id for high-impact or recurring errors requiring detailed analysis",
    "step5": "Synthesize Findings: Present error analysis with patterns, root causes, and impact assessment"
  }
  - Example_phrasings: ["find errors", "show me errors", "error analysis", "failed requests", "system failures"]

- **Application Timeline Analysis**:
  - Canonical_question: "How do I track what happened to a specific application?"
  - Answer_plan: {
    "step1": "Query for a Summary Timeline: Retrieve all log events for application ID using projection(['_id', 'summary', 'timestamp']) sorted by timestamp ascending",
    "step2": "Construct the Application Narrative: Analyze summaries chronologically to build step-by-step story from submission to final decision",
    "step3": "Determine the Final Business Outcome: Identify the last significant event to determine final status (APPROVED, DECLINED, EXPIRED, or BOOKED)",
    "step4": "Synthesize and Present Findings: Summarize entire sequence in clear, non-technical format suitable for business stakeholder"
  }
  - Example_phrasings: ["application tracking", "app timeline", "what happened to application"]

#### 3. Translation Mapping Memories

**Purpose**: Map business terminology to technical log fields

**Examples**:
- **Status Mappings**:
  - Title: "Business term: declined application"
  - Body: "response.body.data.status == 'DECLINED'"
  - Aliases: ["declined", "rejected", "denial", "application decline"]

- **Error Terminology**:
  - Title: "Business term: system errors"
  - Body: "status_code >= 400"
  - Aliases: ["errors", "failures", "issues", "problems"]

- **Field Mappings**:
  - Title: "Business term: application ID"
  - Body: "context.application_id"
  - Aliases: ["app id", "application identifier", "application number"]

#### 4. Agent Guidance Memories

**Purpose**: Store internal guidance, rules, and decision-making logic for the agent

**Examples**:
- **Query Size Rules**:
  - Title: "Query optimization thresholds"
  - Body: "Use full logs for <10 results, summary+key fields for 10-50 results, summary-only for >50 results"

- **Context Window Protection**:
  - Title: "Large dataset handling strategy"
  - Body: "When query might return >10 logs, MUST use projection(['_id', 'summary', 'timestamp']) to prevent context overflow"

- **Query Strategy Selection**:
  - Title: "How to choose query approach"
  - Body: "For targeted queries (doc_id, transaction_id, order_id), fetch full logs directly. For broad queries (time ranges, error types, service logs), start with summary projection. Time-based and service-wide queries almost always require summary fields first."

- **Error Handling Approach**:
  - Title: "Large dataset warning response"
  - Body: "If receiving 'LARGE DATASET DETECTED' warning, immediately switch to summary-only strategy with projection(['_id', 'summary', 'timestamp']). Use two-step approach: summary overview first, then detailed drill-down by _id for specific cases."

### Dynamic System Prompt Construction

The memory-driven approach replaces the static 700+ line prompt with dynamic assembly:

```python
def construct_system_prompt(user_query: str, persona: str, context: dict) -> str:
    # 1. Search for relevant memories by type
    domain_workflows = memory_manager.search(user_query, type_filter="domain_workflow", top_k=3)
    field_knowledge = memory_manager.search(user_query, type_filter="field_knowledge", top_k=5)
    translation_mappings = memory_manager.search(user_query, type_filter="translation_mapping", top_k=3)
    agent_guidance = memory_manager.search(user_query, type_filter="agent_guidance", top_k=3)

    # 2. Construct dynamic prompt with only relevant knowledge
    prompt = f"""
You are an expert log intelligence agent analyzing logs from {context['database']}.{context['collection']}.

## Relevant Knowledge for This Query:

### Analytical Workflows:
{format_domain_workflows(domain_workflows)}

### Field Information:
{format_field_knowledge(field_knowledge)}

### Business Terminology:
{format_translation_mappings(translation_mappings)}

### Agent Guidance:
{format_agent_guidance(agent_guidance)}

## Current Context:
- Database: {context['database']}
- Collection: {context['collection']}
- Current time: {context['current_time']}
- Persona: {persona}

{get_persona_instructions(persona)}
"""

    return prompt
```

## Why Switch to Memory-Driven Prompts?

### Problems with Current Static Approach

1. **Massive context usage**: 700+ line prompt consumes significant context window space
2. **Irrelevant information**: Every query gets all knowledge, even if only 10% is relevant
3. **Maintenance burden**: Updates require modifying large, complex prompt strings
4. **No adaptability**: Static knowledge cannot improve based on usage patterns
5. **Knowledge fragmentation**: Related information scattered across different sections

### Benefits of Memory-Driven Approach

#### Immediate Benefits
- **Reduced context usage**: Only include relevant knowledge per query (60-80% reduction)
- **Faster response times**: Less prompt processing overhead
- **Better maintenance**: Update individual memories vs large prompt strings
- **Targeted responses**: More precise context for specific query types
- **Organized knowledge**: Clear separation between workflows, field info, translations, and guidance

#### Long-term Benefits
- **Continuous learning**: Add new memories from successful query patterns
- **Quality improvement**: Refine memories based on effectiveness per type
- **Scalability**: Easy to add new domains, fields, or business terms
- **Personalization**: Adapt memory selection based on user/persona patterns
- **Knowledge evolution**: Update domain workflows and agent guidance independently

### Strategic Advantages

1. **Intelligence over Size**: Small, relevant context vs large, comprehensive context
2. **Adaptability**: System can learn and improve over time
3. **Modularity**: Update specific knowledge areas without affecting others
4. **Efficiency**: Computational resources focused on relevant knowledge
5. **Maintainability**: Clear structure for knowledge management and updates

This represents a fundamental shift from static, monolithic prompts to dynamic, intelligent knowledge retrieval that can adapt and improve over time.
