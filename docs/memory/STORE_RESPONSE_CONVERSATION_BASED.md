# Store Response: Conversation-Based Learning Implementation

## Core Strategy

Use the full conversation history from the session to identify knowledge gaps that were filled through user guidance, then store these learnings as new memories.

**Trigger**: UI button "Learn from this session" (allowed only once per session) rather than automatic after each run.

## Implementation Flow

### 1. Session-Level Learning Function

```python
def learn_from_session(session_id: str) -> Dict[str, Any]:
    """
    Analyze complete session conversation to identify missing knowledge and store learnings.
    Triggered by UI button, not automatic workflow.
    """

    # Get complete conversation history for this session
    conversation_history = get_session_conversation_history(session_id)

    # Get all memory contexts used throughout the session
    all_memory_contexts = extract_memory_contexts_from_conversation(conversation_history)

    return analyze_and_store_learnings(
        conversation_history=conversation_history,
        memory_contexts_used=all_memory_contexts
    )
```

### 2. LLM Analysis for Knowledge Gaps

```python
def analyze_and_store_learnings(conversation_history, memory_contexts_used) -> Dict[str, Any]:
    """
    Use LLM to identify what knowledge was missing from memory contexts used in the session.
    """

    analysis_prompt = f"""
    Review this conversation between a user and a log analyzer agent.

    MEMORY CONTEXTS USED THROUGHOUT SESSION:
    {format_memory_contexts(memory_contexts_used)}

    FULL CONVERSATION HISTORY:
    {format_conversation_history(conversation_history)}

    TASK: Identify key knowledge that was MISSING from the original memory context
    that would have helped the agent arrive at the correct answer faster.

    Organize findings into these categories:

    1. DOMAIN WORKFLOW - New analytical approaches or step-by-step procedures
       Example: "A systematic approach to trace application lifecycle across services"

    2. FIELD KNOWLEDGE - Log field information not previously documented
       Example: "context.parent_request_id links requests to their originating request"

    3. TRANSLATION MAPPING - Business terms that needed technical field translation
       Example: "pending applications" maps to "response.body.data.status == 'PENDING'"

    4. AGENT GUIDANCE - Decision-making rules or optimization strategies
       Example: "When tracking cross-service requests, always start with doc_id for grouping"

    REQUIREMENTS:
    - Only identify knowledge that was demonstrably missing and caused confusion
    - Focus on learnings that would be reusable for similar future queries
    - Be specific about what the agent should have known but didn't
    - Skip obvious or trivial information

    OUTPUT FORMAT:
    Return JSON with identified learnings:
    {{
        "domain_workflow": [{{
            "title": "...",
            "description": "...",
            "would_have_helped": "..."
        }}],
        "field_knowledge": [...],
        "translation_mapping": [...],
        "agent_guidance": [...]
    }}
    """

    # Call LLM to analyze gaps
    learnings = llm_analyze_knowledge_gaps(analysis_prompt)

    # Process each category of learnings
    results = {}
    for category, items in learnings.items():
        results[category] = process_learnings_for_category(category, items)

    return results
```

### 3. Process Learnings by Category

```python
def process_learnings_for_category(category: str, learnings: List[dict]) -> Dict[str, Any]:
    """
    For each learning, check if similar memory exists and create/update accordingly.
    """
    from src.memory import MemoryManager

    memory_manager = MemoryManager()
    results = {
        "new_memories_created": [],
        "feedback_added_to": [],
        "learnings_processed": len(learnings)
    }

    for learning in learnings:
        # Search for similar existing memories of this type
        similar_memories = memory_manager.search(
            query=learning['description'],
            type_filter=category,
            top_k=3
        )
        if similar_memories and similar_memories[0]['similarity'] > 0.8:
            # Very similar memory exists - add feedback about the knowledge gap
            memory_id = similar_memories[0]['memory_id']
            memory_manager.add_feedback(
                memory_id=memory_id,
                feedback_type="knowledge_gap_identified",
                context={
                    "learning": learning,
                    "gap_description": learning.get('would_have_helped', ''),
                    "source": "conversation_analysis"
                }
            )
            results["feedback_added_to"].append(memory_id)

        else:
            # No similar memory - create new one
            new_memory = create_memory_from_learning(category, learning)
            memory_id = memory_manager.create_memory(new_memory)
            results["new_memories_created"].append(memory_id)

    return results
```

### 4. Create Memory from Learning

```python
def create_memory_from_learning(category: str, learning: dict) -> dict:
    """
    Convert identified learning into properly structured memory.
    """

    base_memory = {
        "type": category,
        "created_from": "conversation_learning",
        "confidence": 0.6,  # Default confidence for conversation-derived memories
        "tags": ["conversation_learning", "knowledge_gap"]
    }

    if category == "domain_workflow":
        return {
            **base_memory,
            "canonical_question": learning['title'],
            "answer_plan": {
                "steps": extract_steps_from_description(learning['description']),
                "strategy": learning['description']
            },
            "example_phrasings": [learning['title']],
            "usage_count": 0,
            "acceptance_count": 0,
            "rejection_count": 0
        }

    elif category == "field_knowledge":
        return {
            **base_memory,
            "title": learning['title'],
            "body": learning['description'],
            "field_type": "conversation_learned",
            "aliases": extract_aliases_from_learning(learning)
        }

    elif category == "translation_mapping":
        return {
            **base_memory,
            "title": f"Business term: {extract_business_term(learning)}",
            "body": extract_technical_mapping(learning),
            "aliases": [extract_business_term(learning)]
        }

    elif category == "agent_guidance":
        return {
            **base_memory,
            "title": learning['title'],
            "body": learning['description']
        }

    return base_memory
```

### 5. Conversation History Retrieval

```python
def get_session_conversation_history(session_id: str) -> List[dict]:
    """
    Retrieve complete conversation history for the session.
    """
    # This would integrate with existing session management
    # to get all user messages and agent responses

    # Example structure:
    return [
        {
            "timestamp": "2025-01-18T10:00:00Z",
            "type": "user_message",
            "content": "Show me errors from yesterday",
            "persona": "business"
        },
        {
            "timestamp": "2025-01-18T10:00:30Z",
            "type": "agent_response",
            "content": "I found 45 error logs from yesterday...",
            "memory_context_used": {...}
        },
        {
            "timestamp": "2025-01-18T10:01:00Z",
            "type": "user_message",
            "content": "Actually, I only care about errors from the credit service"
        },
        # ... continue conversation
    ]
```

### 6. API Integration for UI Button

```python
# New API endpoint for session learning
@app.route('/api/sessions/<session_id>/learn', methods=['POST'])
def learn_from_session_endpoint(session_id: str):
    """
    API endpoint triggered by UI "Learn from this session" button.
    """
    try:
        # Check if learning already done for this session
        if session_learning_exists(session_id):
            return {"error": "Learning already completed for this session"}, 400

        results = learn_from_session(session_id)

        # Mark session as learned from
        mark_session_learned(session_id)

        return {
            "success": True,
            "results": results,
            "new_memories": len(results.get('new_memories_created', [])),
            "feedback_added": len(results.get('feedback_added_to', []))
        }

    except Exception as e:
        logging.error(f"Error in session learning for {session_id}: {e}")
        return {"error": str(e)}, 500

def get_session_conversation_history(session_id: str) -> List[dict]:
    """
    Retrieve complete conversation history - leverage existing chat history function.
    This likely exists since UI can show chat history and continue conversations.
    """
    # TODO: Find existing function that powers chat history in UI
    # Probably something like: return chat_history_manager.get_session_history(session_id)
    pass
```

## Key Benefits

1. **Context-Aware**: Uses actual user guidance to identify real knowledge gaps
2. **Validated Learning**: Only stores knowledge that was demonstrably needed
3. **Conversation-Driven**: Leverages the full interaction context, not just final answer
4. **Gap-Focused**: Specifically targets missing knowledge rather than duplicating existing
5. **Non-Blocking**: Errors in learning don't impact user experience

## Implementation Requirements

- Access to session conversation history storage
- Integration with existing memory management system
- LLM analysis capabilities for knowledge gap identification
- Proper error handling to maintain workflow reliability

## Success Metrics

- Number of useful memories created from conversations
- Reduction in similar knowledge gaps in future sessions
- User satisfaction with improved agent responses over time
- Quality of automatically generated memories (manual review)
