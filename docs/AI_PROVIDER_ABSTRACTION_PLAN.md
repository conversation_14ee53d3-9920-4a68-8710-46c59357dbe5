# AI Provider Abstraction Implementation Plan

## Overview

This document outlines the comprehensive plan for abstracting away AI provider dependencies in the Alfred log analysis system. The goal is to enable seamless switching between different LLM providers (Google GenAI, OpenAI, Anthropic, etc.) while maintaining all existing functionality including cost tracking, logging, structured output, and tool usage.

## Current State Analysis

### Existing Google GenAI Integration
- **Primary Integration**: `src/log_agent.py` contains all core AI interactions
- **Query Interface**: `src/gemini_query.py` provides simplified querying
- **Client Management**: `src/client_managers.py` handles singleton client instances
- **Configuration**: Environment-based model selection with 4 model types:
  - `model_name`: Primary model (gemini-2.5-pro)
  - `lite_model`: Lightweight tasks (gemini-2.5-flash)
  - `summarization_model`: Log summarization (gemini-2.5-flash)
  - `embedding_model`: Text embeddings (text-embedding-004)

### Current AI Call Patterns
1. **Planning**: Complex plan generation using primary model
2. **Step Execution**: Individual task execution using lite model
3. **Classification**: Routing decisions using lite model
4. **Final Answer**: Response synthesis using primary model
5. **Tool Execution**: MongoDB queries via function calling

## Proposed Architecture

### 1. LangChain/LangGraph Based Abstraction

After analysis (detailed in the LangChain Analysis section below), **we recommend using LangChain/LangGraph** as the abstraction layer instead of building a custom provider interface. LangChain provides:

- ✅ Multi-model workflows with per-node model selection
- ✅ Comprehensive usage tracking (`usage_metadata`) for cost calculation
- ✅ Provider-agnostic structured output with `with_structured_output()`
- ✅ Standardized response processing across all providers
- ✅ Advanced auto-retry mechanisms with `with_retry()`
- ✅ Access to provider-specific features (cached tokens, etc.)

### 2. Centralized UnifiedProviderService

The core abstraction will be a `UnifiedProviderService` that:

- **Centralizes model registry** with all supported providers (Google GenAI, OpenAI, Anthropic)
- **Manages LangChain model instances** with caching and proper configuration
- **Provides unified cost calculation** across all providers
- **Handles provider-specific setup** while exposing a consistent interface
- **Integrates with existing client managers** for resource management

### 3. Key Components

1. **Model Registry**: Central configuration for all supported models with costs and capabilities
2. **LangChain Integration**: Provider-specific LangChain model creation and management
3. **Cost Tracking**: Unified cost calculation using provider-specific token metrics
4. **Client Manager Integration**: Reuses existing singleton patterns for resource management

## Implementation Approach

The implementation follows a **LangChain/LangGraph-based approach** as detailed in the analysis section below. This provides:

- **Proven abstraction layer** with provider-agnostic interfaces
- **Built-in cost tracking** through `usage_metadata`
- **Advanced retry mechanisms** with provider-specific exception handling
- **Structured output support** across all providers
- **Multi-model workflow capabilities** for different task types

The detailed implementation is provided in the **Centralized UnifiedProviderService** section below.

## Benefits

### Immediate Benefits
- **Provider Flexibility**: Switch between Google GenAI, OpenAI, Anthropic with configuration changes
- **Cost Optimization**: Compare costs across providers and choose optimal models
- **Vendor Independence**: Reduce lock-in to any single AI provider
- **Unified Interface**: Consistent API regardless of underlying provider

### Long-term Benefits
- **Easy Integration**: Add new providers without changing core logic
- **A/B Testing**: Compare provider performance on real workloads
- **Fallback Strategies**: Automatic failover between providers
- **Feature Adoption**: Leverage new capabilities as they become available

## Risk Mitigation

### Technical Risks
- **Feature Parity**: Ensure all providers support required functionality
- **Performance Impact**: Minimize abstraction overhead
- **Breaking Changes**: Maintain backward compatibility during migration

### Business Risks
- **Cost Increases**: Monitor costs during provider transitions
- **Quality Regression**: Extensive testing to maintain response quality
- **Vendor Dependencies**: Ensure multiple viable provider options

## Success Metrics

1. **Functionality**: All existing features work across all providers
2. **Performance**: <5% performance overhead from abstraction
3. **Cost**: Ability to reduce costs by 20-30% through optimal provider selection
4. **Maintainability**: 50% reduction in provider-specific code
5. **Extensibility**: New provider integration in <1 week

## LangChain/LangGraph vs Custom Abstraction Analysis

### **Research Questions & Findings**

After deeper investigation into LangChain/LangGraph's 2025 capabilities, here are the key findings:

#### **1. Multi-Model Support in LangGraph** ✅ **FULLY SUPPORTED**
```python
# LangGraph allows different models per node
def planning_node(state):
    planner = ChatGoogleGenerativeAI(model="gemini-2.5-pro")  # Expensive, smart
    return planner.invoke(state.messages)

def execution_node(state):
    executor = ChatGoogleGenerativeAI(model="gemini-2.5-flash")  # Fast, cheap
    return executor.invoke(state.messages)

def classification_node(state):
    classifier = ChatOpenAI(model="gpt-4o-mini")  # Different provider entirely
    return classifier.invoke(state.messages)
```

**Verdict**: LangGraph fully supports Alfred's multi-model strategy within the same workflow.

#### **2. Usage Tracking for Cost Calculation** ✅ **COMPREHENSIVE SUPPORT**
```python
# LangChain provides standardized usage tracking
response = llm.invoke(messages)
usage = response.usage_metadata

# Standard interface across all providers
input_tokens = usage.input_tokens       # Sufficient for cost tracking
output_tokens = usage.output_tokens     # Sufficient for cost tracking
cached_tokens = usage.get("cached_tokens", 0)  # Provider-specific features
```

**Verdict**: LangChain's `usage_metadata` provides sufficient information for cost tracking. The 2025 version includes cached tokens, reasoning tokens, and provider-specific usage details.

#### **3. Structured Output Support** ✅ **PROVIDER-AGNOSTIC**
```python
# Same schema works across all providers
from pydantic import BaseModel

class ResponseSchema(BaseModel):
    requires_db_query: bool
    direct_response: str

# Provider-agnostic structured output
llm_with_schema = llm.with_structured_output(ResponseSchema)
response = llm_with_schema.invoke(messages)  # Returns typed object
```

**Verdict**: LangChain's `with_structured_output()` handles provider differences automatically (function calling, JSON mode, etc.).

#### **4. Response Processing** ✅ **STANDARDIZED**
```python
# Standard LangChain message handling replaces custom ResponseProcessor
response = llm.invoke(messages)
text_content = response.content                    # Standard across providers
tool_calls = response.tool_calls                   # Standardized format
tool_call_id = tool_calls[0]["id"]                # Consistent structure
tool_name = tool_calls[0]["name"]
tool_args = tool_calls[0]["args"]
```

**Verdict**: Alfred's `ResponseProcessor.get_texts_and_tool_calls_from_response()` can be replaced with standard LangChain message handling.

#### **5. Auto-Retry Mechanisms** ✅ **BUILT-IN SUPPORT**
```python
# Replace custom retry_utils.py with LangChain's built-in retry
llm_with_retry = llm.with_retry(
    retry_if_exception_type=(ConnectionError, TimeoutError, RateLimitError),
    stop_after_attempt=3,
    wait_exponential_jitter=True,
    retry_if_result=lambda result: result is None
)

# Can replace call_google_genai_with_retry entirely
response = llm_with_retry.invoke(messages)
```

**Verdict**: LangChain's retry mechanisms are more sophisticated than Alfred's custom implementation.

### **Revised Implementation Options**

#### **Option A: Pure LangChain/LangGraph Approach (Recommended)**

**Advantages:**
```python
# Alfred can leverage LangChain's full ecosystem
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent

# Multi-model, provider-agnostic setup with all Alfred's requirements
models = {
    "planning": ChatGoogleGenerativeAI(model="gemini-2.5-pro"),
    "execution": ChatGoogleGenerativeAI(model="gemini-2.5-flash"),
    "classification": ChatOpenAI(model="gpt-4o-mini")
}

# Built-in cost tracking, retry, structured output, tool handling
agent = create_react_agent(
    model=models["execution"]
        .with_retry(stop_after_attempt=3)
        .with_structured_output(QueryClassificationSchema),
    tools=[mongodb_tool]
)
```

**Benefits:**
- ✅ **Reduced Complexity**: Eliminate custom `TaskManager`, `ResponseProcessor`, `retry_utils.py`
- ✅ **Better Observability**: Built-in usage tracking and LangSmith integration
- ✅ **Battle-Tested**: Mature retry mechanisms and error handling
- ✅ **Future-Proof**: Provider-agnostic design, automatic feature adoption
- ✅ **Community Support**: Active development, extensive documentation
- ✅ **Cost Optimization**: Advanced token tracking and multi-model support

**Migration Path:**
1. Replace current LangGraph implementation with multi-model nodes
2. Convert `ResponseProcessor` to standard LangChain message handling
3. Replace `retry_utils.py` with `.with_retry()` methods
4. Migrate to `usage_metadata` for cost tracking
5. Use `with_structured_output()` for consistent response parsing
6. Leverage provider-specific features through LangChain integrations

#### **Option B: Custom Abstraction (Original Plan)**

**Only justified if Alfred needs:**
- Very specific optimizations not available in LangChain
- Custom cost calculation logic beyond standard token tracking
- Provider-specific features not exposed by LangChain integrations
- Performance requirements that LangChain's abstraction can't meet

### **Final Recommendation**

**Use LangChain/LangGraph** as the primary abstraction layer. The 2025 version fully supports Alfred's requirements:

1. ✅ Multi-model workflows with per-node model selection
2. ✅ Comprehensive usage tracking for cost calculation
3. ✅ Provider-agnostic structured output
4. ✅ Standardized response processing
5. ✅ Advanced auto-retry mechanisms
6. ✅ Access to provider-specific features (cached tokens, etc.)

This approach significantly reduces implementation complexity while providing a more robust, battle-tested foundation for Alfred's AI provider abstraction needs.

This analysis positions Alfred to leverage the full LangChain ecosystem while maintaining all existing functionality and enabling seamless provider switching through configuration rather than code changes.

## Centralized UnifiedProviderService

As requested, all model-to-provider mapping, cost calculation, and LangChain instance creation should be centralized in a single service that both `src/log_agent.py` and `app/routes.py` can use.

### Implementation

```python
# src/ai_providers/unified_service.py
import logging
import time
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum

from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_openai import ChatOpenAI
from langchain_anthropic import ChatAnthropic
from langchain_core.language_models.base import BaseLanguageModel

from src.env_config import EnvConfig
from src.database_manager import DatabaseManager
from src.client_managers import get_genai_manager, get_mongo_manager

class ModelType(Enum):
    """Different model types used in Alfred"""
    PRIMARY = "primary"           # Complex planning, final answers
    LITE = "lite"                # Step execution, classification
    SUMMARIZATION = "summarization"  # Log summarization
    EMBEDDING = "embedding"      # Text embeddings

@dataclass
class ModelInfo:
    """Information about a model"""
    model_id: str
    provider: str
    model_type: ModelType
    cost_per_input_token: float  # USD per million tokens
    cost_per_output_token: float
    capabilities: List[str]
    max_context_length: int

class UnifiedProviderService:
    """
    Centralized service for AI provider management, model mapping,
    and cost calculation used by both log_agent.py and routes.py
    """

    # Model registry with all supported models
    MODEL_REGISTRY: Dict[str, ModelInfo] = {
        # Google GenAI Models
        "gemini-2.5-pro": ModelInfo(
            model_id="gemini-2.5-pro",
            provider="google_genai",
            model_type=ModelType.PRIMARY,
            cost_per_input_token=1.25,    # $1.25 per million tokens
            cost_per_output_token=5.00,   # $5.00 per million tokens
            capabilities=["text_generation", "function_calling", "structured_output"],
            max_context_length=2000000
        ),
        "gemini-2.5-flash": ModelInfo(
            model_id="gemini-2.5-flash",
            provider="google_genai",
            model_type=ModelType.LITE,
            cost_per_input_token=0.075,   # $0.075 per million tokens
            cost_per_output_token=0.30,   # $0.30 per million tokens
            capabilities=["text_generation", "function_calling", "structured_output"],
            max_context_length=1000000
        ),
        "text-embedding-004": ModelInfo(
            model_id="text-embedding-004",
            provider="google_genai",
            model_type=ModelType.EMBEDDING,
            cost_per_input_token=0.00001, # $0.00001 per million tokens
            cost_per_output_token=0.0,    # No output cost for embeddings
            capabilities=["embeddings"],
            max_context_length=2048
        ),

        # OpenAI Models
        "gpt-4o": ModelInfo(
            model_id="gpt-4o",
            provider="openai",
            model_type=ModelType.PRIMARY,
            cost_per_input_token=2.50,    # $2.50 per million tokens
            cost_per_output_token=10.00,  # $10.00 per million tokens
            capabilities=["text_generation", "function_calling", "structured_output"],
            max_context_length=128000
        ),
        "gpt-4o-mini": ModelInfo(
            model_id="gpt-4o-mini",
            provider="openai",
            model_type=ModelType.LITE,
            cost_per_input_token=0.150,   # $0.150 per million tokens
            cost_per_output_token=0.600,  # $0.600 per million tokens
            capabilities=["text_generation", "function_calling", "structured_output"],
            max_context_length=128000
        ),
        "text-embedding-3-large": ModelInfo(
            model_id="text-embedding-3-large",
            provider="openai",
            model_type=ModelType.EMBEDDING,
            cost_per_input_token=0.130,   # $0.130 per million tokens
            cost_per_output_token=0.0,
            capabilities=["embeddings"],
            max_context_length=8191
        ),

        # Anthropic Models
        "claude-3-5-sonnet-20250115": ModelInfo(
            model_id="claude-3-5-sonnet-20250115",
            provider="anthropic",
            model_type=ModelType.PRIMARY,
            cost_per_input_token=3.00,    # $3.00 per million tokens
            cost_per_output_token=15.00,  # $15.00 per million tokens
            capabilities=["text_generation", "function_calling", "structured_output"],
            max_context_length=200000
        ),
        "claude-3-5-haiku-20241022": ModelInfo(
            model_id="claude-3-5-haiku-20241022",
            provider="anthropic",
            model_type=ModelType.LITE,
            cost_per_input_token=0.80,    # $0.80 per million tokens
            cost_per_output_token=4.00,   # $4.00 per million tokens
            capabilities=["text_generation", "function_calling", "structured_output"],
            max_context_length=200000
        )
    }

    def __init__(self, config: EnvConfig = None, database_manager: DatabaseManager = None):
        """Initialize the unified provider service"""
        self.config = config or EnvConfig.load()
        self.database_manager = database_manager
        self.logger = logging.getLogger(__name__)

        # Thread-safe cache for LangChain model instances
        self._model_cache: Dict[str, BaseLanguageModel] = {}
        self._cache_lock = threading.RLock()  # Reentrant lock for thread safety

        # Integration with existing client managers (these are already thread-safe singletons)
        self.genai_manager = get_genai_manager(self.config)
        self.mongo_manager = get_mongo_manager(self.config) if database_manager is None else None

        # Load current model configuration from environment (immutable after init)
        self._current_models = {
            ModelType.PRIMARY: self.config.model_name,
            ModelType.LITE: self.config.lite_model,
            ModelType.SUMMARIZATION: self.config.summarization_model,
            ModelType.EMBEDDING: self.config.embedding_model
        }

    def get_langchain_model(self, model_id: str, **kwargs) -> BaseLanguageModel:
        """
        Get a LangChain model instance for the given model ID.
        Thread-safe method used by both log_agent.py and routes.py.
        """
        # Create deterministic cache key
        cache_key = f"{model_id}_{hash(frozenset(kwargs.items()))}"

        # Thread-safe cache check
        with self._cache_lock:
            if cache_key in self._model_cache:
                return self._model_cache[cache_key]

        # Get model info (MODEL_REGISTRY is immutable class variable)
        if model_id not in self.MODEL_REGISTRY:
            raise ValueError(f"Unknown model: {model_id}")

        model_info = self.MODEL_REGISTRY[model_id]

        # Create LangChain instance based on provider
        if model_info.provider == "google_genai":
            # Use existing GenAI client manager (thread-safe singleton)
            genai_client = self.genai_manager.get_client()
            model = ChatGoogleGenerativeAI(
                model=model_id,
                temperature=kwargs.get("temperature", 0.0),
                max_tokens=kwargs.get("max_tokens"),
                client=genai_client,  # Use existing singleton client
                **kwargs
            )
        elif model_info.provider == "openai":
            model = ChatOpenAI(
                model=model_id,
                temperature=kwargs.get("temperature", 0.0),
                max_tokens=kwargs.get("max_tokens"),
                **kwargs
            )
        elif model_info.provider == "anthropic":
            model = ChatAnthropic(
                model=model_id,
                temperature=kwargs.get("temperature", 0.0),
                max_tokens=kwargs.get("max_tokens"),
                **kwargs
            )
        else:
            raise ValueError(f"Unsupported provider: {model_info.provider}")

        # Add retry configuration
        model = model.with_retry(
            stop_after_attempt=kwargs.get("max_retries", 3),
            wait_exponential_multiplier=kwargs.get("retry_delay", 1.0)
        )

        # Thread-safe cache update
        with self._cache_lock:
            # Double-check in case another thread created it
            if cache_key not in self._model_cache:
                self._model_cache[cache_key] = model
            return self._model_cache.get(cache_key, model)

    def get_model_for_task(self, task_type: ModelType, **kwargs) -> BaseLanguageModel:
        """
        Get the configured model for a specific task type.
        E.g., get_model_for_task(ModelType.LITE) returns the lite model
        """
        model_id = self._current_models.get(task_type)
        if not model_id:
            raise ValueError(f"No model configured for task type: {task_type}")

        return self.get_langchain_model(model_id, **kwargs)

    def calculate_cost(self, model_id: str, input_tokens: int, output_tokens: int,
                      cached_tokens: int = 0) -> float:
        """
        Calculate cost for API usage across any provider.
        Used by both log_agent.py and routes.py for unified cost tracking
        """
        if model_id not in self.MODEL_REGISTRY:
            self.logger.warning(f"Unknown model for cost calculation: {model_id}")
            return 0.0

        model_info = self.MODEL_REGISTRY[model_id]

        # Calculate base cost
        input_cost = (input_tokens / 1_000_000) * model_info.cost_per_input_token
        output_cost = (output_tokens / 1_000_000) * model_info.cost_per_output_token

        # Handle cached tokens (typically much cheaper)
        # For Google GenAI, cached tokens are ~50% cheaper
        cached_cost = 0.0
        if cached_tokens > 0 and model_info.provider == "google_genai":
            cached_cost = (cached_tokens / 1_000_000) * (model_info.cost_per_input_token * 0.5)

        total_cost = input_cost + output_cost + cached_cost

        # Log cost calculation for audit trail
        self.logger.debug(
            f"Cost calculated for {model_id}: "
            f"${total_cost:.6f} (input: {input_tokens}, output: {output_tokens}, cached: {cached_tokens})"
        )

        return total_cost

    def log_usage(self, model_id: str, usage_metrics: Dict[str, Any],
                  session_id: str = None, run_id: str = None, call_type: str = "unknown"):
        """
        Unified usage logging for both log_agent.py and routes.py
        """
        if model_id not in self.MODEL_REGISTRY:
            self.logger.warning(f"Unknown model for usage logging: {model_id}")
            return

        model_info = self.MODEL_REGISTRY[model_id]

        # Calculate cost
        cost = self.calculate_cost(
            model_id=model_id,
            input_tokens=usage_metrics.get("input_tokens", usage_metrics.get("prompt_tokens", 0)),
            output_tokens=usage_metrics.get("output_tokens", usage_metrics.get("completion_tokens", 0)),
            cached_tokens=usage_metrics.get("cached_tokens", 0)
        )

        # Create unified log entry
        log_entry = {
            "model_id": model_id,
            "provider": model_info.provider,
            "model_type": model_info.model_type.value,
            "call_type": call_type,
            "usage_metrics": usage_metrics,
            "estimated_cost_usd": cost,
            "session_id": session_id,
            "run_id": run_id,
            "timestamp": time.time()
        }

        # Log to application logger
        self.logger.info(
            f"AI API usage: {model_id} - ${cost:.6f}",
            extra={"details": log_entry}
        )

        # Persist to database if available
        if self.database_manager and session_id:
            try:
                self.database_manager.log_ai_usage(session_id, log_entry)
            except Exception as e:
                self.logger.error(f"Failed to persist usage log: {e}")

    def get_available_models(self, model_type: ModelType = None,
                            provider: str = None) -> List[ModelInfo]:
        """
        Get available models, optionally filtered by type or provider
        """
        models = list(self.MODEL_REGISTRY.values())

        if model_type:
            models = [m for m in models if m.model_type == model_type]

        if provider:
            models = [m for m in models if m.provider == provider]

        return models

    # Note: Dynamic model switching is not supported in this implementation
    # Models are configured at startup and remain constant for the session

    def cleanup(self):
        """
        Clean up UnifiedProviderService resources.
        Called by client_managers.py during shutdown.
        """
        try:
            # Clear model cache
            self._model_cache.clear()

            # Log cleanup
            self.logger.info("UnifiedProviderService cache cleared")

            # Note: We don't cleanup genai_manager or mongo_manager here
            # because they are managed by client_managers.py

        except Exception as e:
            self.logger.error(f"Error during UnifiedProviderService cleanup: {e}")

# NOTE: Singleton management is handled by client_managers.py
# Use get_unified_service_manager(config) from client_managers.py instead
```

### Usage in log_agent.py

```python
# src/log_agent.py - Updated to use centralized service
from src.client_managers import get_unified_service_manager
from src.ai_providers.unified_service import ModelType

class LogIntelligenceAgentLangGraph:
    def __init__(self, config: EnvConfig = None, env_file: str = None):
        # ... existing initialization ...

        # Use centralized provider service via client_managers.py
        self.ai_service = get_unified_service_manager(self.config)

    def planner(self, state: PlanExecuteState):
        """Planning node - uses PRIMARY model"""
        # Get the configured primary model (e.g., gemini-2.5-pro)
        planner_model = self.ai_service.get_model_for_task(ModelType.PRIMARY)

        # Use LangChain directly instead of custom retry logic
        messages = [
            SystemMessage(content=PLANNING_PROMPT),
            HumanMessage(content=state["input"])
        ]

        response = planner_model.invoke(messages)

        # Log usage through centralized service
        self.ai_service.log_usage(
            model_id=self.config.model_name,  # Primary model
            usage_metrics=response.usage_metadata,
            session_id=state["session_id"],
            run_id=state["run_id"],
            call_type="planning"
        )

        return {"plan": self._extract_plan_steps(response.content)}

    def execute_step(self, state: PlanExecuteState):
        """Step execution - uses LITE model"""
        # Get the configured lite model (e.g., gemini-2.5-flash)
        executor_model = self.ai_service.get_model_for_task(
            ModelType.LITE,
            tools=[self.mongodb_query_tool]  # Add MongoDB tool
        )

        # Execute with function calling
        messages = [
            SystemMessage(content=EXECUTION_PROMPT),
            HumanMessage(content=state["current_step"])
        ]

        response = executor_model.invoke(messages)

        # Log usage through centralized service
        self.ai_service.log_usage(
            model_id=self.config.lite_model,  # Lite model
            usage_metrics=response.usage_metadata,
            session_id=state["session_id"],
            run_id=state["run_id"],
            call_type="step_execution"
        )

        return {"past_steps": [(state["current_step"], response.content)]}
```

### Usage in routes.py

```python
# app/routes.py - Updated to use centralized service
from src.client_managers import get_unified_service_manager
from src.ai_providers.unified_service import ModelType

def summarize_logs():
    """Log summarization endpoint - now provider agnostic"""
    try:
        # Use centralized provider service via client_managers.py
        ai_service = get_unified_service_manager(config)

        # Get summarization model (could be any provider)
        summarizer_model = ai_service.get_model_for_task(ModelType.SUMMARIZATION)

        # Provider-agnostic summarization
        messages = [
            {"role": "system", "content": "Summarize the following logs..."},
            {"role": "user", "content": logs_content}
        ]

        response = summarizer_model.invoke(messages)

        # Unified cost tracking and logging
        ai_service.log_usage(
            model_id=config.summarization_model,
            usage_metrics=response.usage_metadata,
            session_id=session_id,
            call_type="log_summarization"
        )

        return jsonify({
            "summary": response.content,
            "model_used": config.summarization_model,
            "cost_usd": ai_service.calculate_cost(
                config.summarization_model,
                response.usage_metadata.get("input_tokens", 0),
                response.usage_metadata.get("output_tokens", 0)
            )
        })

    except Exception as e:
        logging.error(f"Summarization failed: {e}")
        return jsonify({"error": str(e)}), 500
```

### Benefits of Centralized Service

1. **Single Source of Truth**: All model configurations, costs, and provider mappings in one place
2. **Consistent Cost Calculation**: Same cost logic used by both log_agent.py and routes.py
3. **Unified Logging**: Standardized usage tracking across the entire application
4. **Easy Model Switching**: Change models in one place, affects entire application
5. **Provider Abstraction**: Switch from Google GenAI to OpenAI by changing model IDs in environment
6. **Performance**: Model instance caching reduces initialization overhead
7. **Maintainability**: Single place to add new models or providers

This centralized approach ensures that both the agent system and Flask routes use the same model registry, cost calculation, and provider abstraction while maintaining consistency across the entire Alfred application.

## Integration with Existing Client Managers

The UnifiedProviderService integrates seamlessly with Alfred's existing `src/client_managers.py` singleton pattern:

### **Thread-Safe Singleton Pattern**
```python
# Both follow the same double-checked locking pattern
# client_managers.py:
def get_genai_manager(config) -> GenAIClientManager:
    global _genai_manager
    if _genai_manager is None:
        with _managers_lock:
            if _genai_manager is None:
                _genai_manager = GenAIClientManager(config)
    return _genai_manager

# client_managers.py handles singleton management for UnifiedProviderService
# Use get_unified_service_manager(config) from client_managers.py instead
```

### **Client Manager Integration**
```python
class UnifiedProviderService:
    def __init__(self, config: EnvConfig = None, database_manager: DatabaseManager = None):
        self.config = config or EnvConfig.load()

        # Reuse existing client managers instead of creating new ones
        self.genai_manager = get_genai_manager(self.config)  # Use existing GenAI singleton
        self.mongo_manager = get_mongo_manager(self.config)  # Use existing Mongo singleton

    def get_langchain_model(self, model_id: str, **kwargs) -> BaseLanguageModel:
        if model_info.provider == "google_genai":
            # Use the existing GenAI client from client_managers.py
            genai_client = self.genai_manager.get_client()
            model = ChatGoogleGenerativeAI(
                model=model_id,
                client=genai_client,  # Reuse existing connection
                **kwargs
            )
```

### **Centralized Resource Management in client_managers.py**

All resource management and cleanup should be centralized in `src/client_managers.py`. Here are the required changes:

```python
# src/client_managers.py - Updated to manage UnifiedProviderService

# Import the UnifiedProviderService (avoid circular imports)
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from src.ai_providers.unified_service import UnifiedProviderService

# Add global reference for unified service
_unified_service_manager = None

def get_unified_service_manager(config) -> 'UnifiedProviderService':
    """Get the global UnifiedProviderService instance."""
    if config is None:
        raise ValueError("get_unified_service_manager requires a config parameter")

    global _unified_service_manager
    if _unified_service_manager is None:
        with _managers_lock:
            if _unified_service_manager is None:
                # Import here to avoid circular dependency
                from src.ai_providers.unified_service import UnifiedProviderService
                _unified_service_manager = UnifiedProviderService(config)
    return _unified_service_manager

def cleanup_all_managers():
    """Clean up all client managers and reset global references.

    This function is intended for:
    1. Application shutdown (registered with atexit)
    2. Testing scenarios where clean state is needed between tests

    Note: After calling this function, new managers will be created
    on the next call to get_mongo_manager(), get_genai_manager(), or get_unified_service_manager().
    """
    global _mongo_manager, _genai_manager, _unified_service_manager

    # Cleanup in reverse order of dependency
    if _unified_service_manager:
        try:
            _unified_service_manager.cleanup()
            logging.info("UnifiedProviderService cleaned up successfully")
        except Exception as e:
            logging.error(f"Error cleaning up UnifiedProviderService: {e}")
        finally:
            _unified_service_manager = None

    if _mongo_manager:
        _mongo_manager.cleanup()
        _mongo_manager = None

    if _genai_manager:
        _genai_manager.cleanup()
        _genai_manager = None

    # Reset class-level singleton instances for testing scenarios
    MongoClientManager._instance = None
    GenAIClientManager._instance = None

    logging.info("All client managers cleaned up and singleton state reset")
```

### **Benefits of Integration**
1. **Consistent Resource Management**: All AI clients use the same connection pooling and lifecycle management
2. **No Resource Conflicts**: Reuses existing singleton clients instead of creating duplicate connections
3. **Unified Cleanup**: All singletons are cleaned up together during app shutdown
4. **Backward Compatibility**: Existing code using `get_genai_manager()` continues to work unchanged
5. **Thread Safety**: Same proven thread-safe pattern used throughout Alfred

### **Usage Pattern Consistency**
```python
# Current usage in log_agent.py:
genai_manager = get_genai_manager(self.config)
client = genai_manager.get_client()

# New usage with unified service (managed by client_managers.py):
ai_service = get_unified_service_manager(self.config)  # Same pattern as other managers
model = ai_service.get_model_for_task(ModelType.PRIMARY)

# All managers follow the same pattern:
mongo_manager = get_mongo_manager(self.config)
genai_manager = get_genai_manager(self.config)
unified_service = get_unified_service_manager(self.config)

# All use the same underlying client instances (no duplicates)
```

This integration ensures the UnifiedProviderService follows Alfred's established patterns while providing the provider abstraction layer that both `log_agent.py` and `routes.py` can leverage.

## Provider-Agnostic Logging

To ensure logging is adapted to the new LangChain/LangGraph API and doesn't assume provider-specific information:

### **Current Provider-Specific Logging**

```python
# Current in log_agent.py:
from src.logging_utils import LoggingUtils
LoggingUtils.log_model_response(response, "planning")

# Current in routes.py:
LoggingUtils.log_model_response(response, "background_log_summarization")
```

### **New Provider-Agnostic Logging**

```python
# src/logging_utils.py - Updated for provider-agnostic logging
class LoggingUtils:
    @staticmethod
    def log_langchain_response(response, call_type: str, model_id: str = None):
        """
        Log LangChain model response with provider-agnostic metrics.

        Args:
            response: LangChain response object with usage_metadata
            call_type: Type of call (e.g., "planning", "execution", "summarization")
            model_id: Optional model ID for tracking
        """
        try:
            # Extract standardized usage metadata
            usage = getattr(response, 'usage_metadata', {})

            log_data = {
                "call_type": call_type,
                "model_id": model_id,
                "usage": {
                    "input_tokens": usage.get("input_tokens", 0),
                    "output_tokens": usage.get("output_tokens", 0),
                    "total_tokens": usage.get("total_tokens", 0),
                    "cached_tokens": usage.get("cached_tokens", 0),
                },
                "response_metadata": {
                    "model": usage.get("model_name", "unknown"),
                    "provider": _infer_provider_from_model(usage.get("model_name", "")),
                    "finish_reason": usage.get("finish_reason", "complete"),
                },
                "timestamp": time.time()
            }

            # Log with structured data
            logging.info(
                f"LangChain API call completed: {call_type}",
                extra={"details": log_data}
            )

        except Exception as e:
            logging.warning(f"Failed to log LangChain response: {e}")

    @staticmethod
    def _infer_provider_from_model(model_name: str) -> str:
        """Infer provider from model name."""
        if "gemini" in model_name.lower():
            return "google_genai"
        elif "gpt" in model_name.lower():
            return "openai"
        elif "claude" in model_name.lower():
            return "anthropic"
        return "unknown"
```

### **Updated Usage in log_agent.py**

```python
# src/log_agent.py - Provider-agnostic logging
class LogIntelligenceAgentLangGraph:
    def planner(self, state: PlanExecuteState):
        """Planning node - uses PRIMARY model"""
        planner_model = self.ai_service.get_model_for_task(ModelType.PRIMARY)

        messages = [
            SystemMessage(content=PLANNING_PROMPT),
            HumanMessage(content=state["input"])
        ]

        response = planner_model.invoke(messages)

        # Provider-agnostic logging
        LoggingUtils.log_langchain_response(
            response,
            call_type="planning",
            model_id=self.config.model_name
        )

        # Unified usage tracking through service
        self.ai_service.log_usage(
            model_id=self.config.model_name,
            usage_metrics=response.usage_metadata,
            session_id=state["session_id"],
            run_id=state["run_id"],
            call_type="planning"
        )

        return {"plan": self._extract_plan_steps(response.content)}
```

### **Updated Usage in routes.py**

```python
# app/routes.py - Provider-agnostic logging
def _summarize_log_with_ai(log, model_name=None):
    """Summarize a log using AI model via LangChain."""
    try:
        # Get unified service
        ai_service = get_unified_service_manager(config)

        # Get summarization model
        summarizer = ai_service.get_model_for_task(ModelType.SUMMARIZATION)

        # Create messages
        messages = [
            {"role": "system", "content": prompt_manager.get_log_summarization_prompt()},
            {"role": "user", "content": json.dumps(log, default=json_converter)}
        ]

        # Invoke model
        response = summarizer.invoke(messages)

        # Provider-agnostic logging
        LoggingUtils.log_langchain_response(
            response,
            call_type="background_log_summarization",
            model_id=config.summarization_model
        )

        # Track usage
        ai_service.log_usage(
            model_id=config.summarization_model,
            usage_metrics=response.usage_metadata,
            call_type="log_summarization"
        )

        if response.content:
            log_with_summary = log.copy()
            log_with_summary["summary"] = response.content.strip()
            log_with_summary["summary_generated_at"] = datetime.datetime.utcnow().isoformat()
            log_with_summary["summary_model"] = config.summarization_model
            log_with_summary["summary_source"] = "pubsub_realtime"

            logging.info(f"Successfully generated summary: {len(response.content)} characters")
            return log_with_summary

        return log

    except Exception:
        logging.exception("Error summarizing log with AI")
        return log
```

### **Simplified Logging Benefits**

1. **Provider Independence**: No assumptions about Google GenAI response structure
2. **Standardized Metrics**: Same logging format regardless of provider
3. **Automatic Provider Detection**: Infers provider from model name
4. **Unified Cost Tracking**: Integrated with UnifiedProviderService
5. **Cleaner Code**: Single logging method for all providers
6. **Better Observability**: Consistent log structure for monitoring

### **Migration Path for Logging**

1. **Phase 1**: Add new `log_langchain_response` method alongside existing
2. **Phase 2**: Update all GenAI-specific logging calls to use new method
3. **Phase 3**: Remove old provider-specific logging methods
4. **Phase 4**: Ensure all cost tracking goes through UnifiedProviderService

This approach ensures that logging throughout Alfred is:
- **Provider-agnostic**: Works with any LangChain-supported provider
- **Simplified**: One logging method instead of provider-specific variants
- **Consistent**: Same log format for monitoring and debugging
- **Cost-aware**: Integrated with centralized cost tracking

## Removing Provider-Specific Dependencies

### **Current Provider-Specific Code to Remove**

```python
# In log_agent.py - Remove these imports:
from google.genai import types
from src.retry_utils import call_google_genai_with_retry

# Remove methods like:
def _create_google_genai_tools_DEPRECATED(self)
def _convert_to_genai_contents(self, messages)
```

### **Replace with LangChain Equivalents**

```python
# src/log_agent.py - Provider-agnostic implementation
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage, ToolMessage
from langchain_core.tools import Tool
from langchain.tools import StructuredTool

class LogIntelligenceAgentLangGraph:
    def __init__(self, config: EnvConfig = None, env_file: str = None):
        # ... existing initialization ...

        # Use centralized provider service
        self.ai_service = get_unified_service_manager(self.config)

        # Create tools using LangChain format (provider-agnostic)
        self.tools = self._create_langchain_tools()

    def _create_langchain_tools(self):
        """Create tools in LangChain format - works with any provider."""
        return [
            StructuredTool.from_function(
                func=self.query_mongodb,
                name="query_mongodb",
                description="Query MongoDB for log data and metrics",
                args_schema=MongoDBQuerySchema  # Pydantic schema
            )
        ]

    def _call_model_with_retry(self, model, messages, **kwargs):
        """
        Call any LangChain model with built-in retry.
        Replaces call_google_genai_with_retry.
        """
        # LangChain handles retries internally with .with_retry()
        return model.invoke(messages, **kwargs)
```

### **Updated routes.py Without GenAI Dependencies**

```python
# app/routes.py - Remove these imports:
from google.genai import types
from src.retry_utils import call_google_genai_with_retry

# Replace _summarize_log_with_ai method:
def _summarize_log_with_ai(log, model_name=None):
    """Summarize a log using AI model via LangChain."""
    try:
        # Get unified service
        ai_service = get_unified_service_manager(config)

        # Get summarization model (already has retry configured)
        summarizer = ai_service.get_model_for_task(ModelType.SUMMARIZATION)

        # Create messages in LangChain format
        messages = [
            SystemMessage(content=prompt_manager.get_log_summarization_prompt()),
            HumanMessage(content=json.dumps(log, default=json_converter))
        ]

        # Invoke model - retry is handled by LangChain
        response = summarizer.invoke(messages)

        # Provider-agnostic logging and usage tracking
        LoggingUtils.log_langchain_response(
            response,
            call_type="background_log_summarization",
            model_id=config.summarization_model
        )

        # ... rest of the method
```

### **Key Changes Summary**

1. **Remove all Google GenAI imports**: No more `google.genai.types`
2. **Remove custom retry utilities**: LangChain handles retries with `.with_retry()`
3. **Use LangChain message types**: `SystemMessage`, `HumanMessage`, etc.
4. **Use LangChain tools**: `StructuredTool` instead of GenAI-specific tools
5. **Standardize on LangChain interfaces**: All models use `.invoke()` method

This ensures the entire codebase is truly provider-agnostic and can switch between providers without code changes.

## Implementation Summary

### **Complete Provider Abstraction Checklist**

- [ ] **Phase 1: Setup UnifiedProviderService**
  - [ ] Create `src/ai_providers/unified_service.py` with model registry
  - [ ] Add to `src/client_managers.py` for singleton management
  - [ ] Implement cost calculation and usage tracking

- [ ] **Phase 2: Update log_agent.py**
  - [ ] Replace Google GenAI imports with LangChain imports
  - [ ] Use UnifiedProviderService for all model creation
  - [ ] Convert tools to LangChain format
  - [ ] Update logging to use `log_langchain_response`

- [ ] **Phase 3: Update routes.py**
  - [ ] Remove GenAI-specific imports and retry utilities
  - [ ] Use UnifiedProviderService for summarization
  - [ ] Update all logging to be provider-agnostic

- [ ] **Phase 4: Update logging_utils.py**
  - [ ] Add `log_langchain_response` method
  - [ ] Ensure provider-agnostic metrics collection
  - [ ] Remove old `log_model_response` method

- [ ] **Phase 5: Testing and Validation**
  - [ ] Test with Google GenAI models (existing)
  - [ ] Test with OpenAI models
  - [ ] Test with Anthropic models
  - [ ] Verify cost tracking accuracy
  - [ ] Ensure logging consistency

### **End Result**

After implementation, Alfred will be able to:

1. **Switch providers via configuration**: Change `MODEL_NAME=gpt-4o` to use OpenAI
2. **Mix providers in same workflow**: Use Gemini for planning, GPT-4 for execution
3. **Track costs universally**: Accurate cost tracking regardless of provider
4. **Log consistently**: Same log format for all providers
5. **Retry automatically**: Built-in retry logic for all providers
6. **Scale easily**: Add new providers by updating model registry

The entire system becomes provider-agnostic while maintaining all existing functionality and improving maintainability.
