# LLM Workflow Optimization Analysis

## Query Analyzed: "Give me a few app ids"

## Executive Summary

The workflow for this simple query makes **6 LLM calls** totaling **36,306 tokens** and **$0.0234** in cost. This seems excessive for retrieving a few application IDs. The analysis reveals several optimization opportunities.

## Workflow Steps Breakdown

### 1. Query Classification (263 input + 11 output = 335 tokens)
- **Model**: gemini-2.5-flash
- **Purpose**: Determine if database access is needed
- **Result**: `requires_db_query=True`
- **Issue**: The classification prompt is verbose for a simple binary decision

### 2. Planning (9,777 input + 269 output = 11,003 tokens)
- **Model**: gemini-2.5-pro (expensive model)
- **Purpose**: Create execution plan
- **Result**: Generated 3-step plan
- **Major Issue**: The input contains the ENTIRE system prompt (9,777 tokens!) including all MongoDB documentation, field descriptions, and examples

### 3. Agent Execution #1 (5,349 input + 70 output = 6,053 tokens)
- **Purpose**: Execute first step - find unique app IDs
- **Result**: MongoDB aggregation query
- **Issue**: Full system prompt repeated again

### 4. Agent Execution #2 (5,419 input + 76 output = 5,868 tokens)
- **Purpose**: Retrieve summaries for latest events
- **Result**: Another MongoDB query
- **Issue**: Still passing full system prompt, not utilizing previous results

### 5. Agent Execution #3 (5,501 input + 58 output = 5,910 tokens)
- **Purpose**: Present business-focused summary
- **Result**: Format the output
- **Issue**: This could have been done in step 2

### 6. Final Answer (5,121 input + 446 output = 7,137 tokens)
- **Model**: gemini-2.5-pro (expensive model)
- **Purpose**: Synthesize final response
- **Issue**: Using expensive model for simple formatting

## Key Problems Identified

### 1. **Excessive System Prompt Repetition**
The full system prompt (9,777 tokens) is sent with EVERY call, including:
- MongoDB schema documentation
- Field descriptions
- Timestamp handling rules
- Query examples
- Memory context

**Impact**: ~30,000 unnecessary tokens across the workflow

### 2. **Over-planning for Simple Queries**
The planner creates a 3-step plan for what should be a single MongoDB query:
```
Step 1: Find unique app IDs
Step 2: Get summaries (unnecessary - could be in step 1)
Step 3: Format output (unnecessary - agent can do this)
```

### 3. **Tool Response Context Not Properly Utilized**
Looking at the logs, tool responses are NOT being passed to subsequent agent calls. Each agent execution starts fresh without the context of previous tool results.

### 4. **Model Selection Issues**
- Using expensive gemini-2.5-pro for planning and final answer
- These tasks could use the cheaper gemini-2.5-flash

## Recommendations for Optimization

### 1. **Implement Prompt Compression**
- Create a minimal agent prompt for execution steps
- Only include relevant context for the specific task
- Move static documentation to a reference system

### 2. **Simplify Planning Logic**
For simple queries like "give me app IDs":
- Skip planning entirely OR
- Use a lightweight planner that outputs single-step plans

### 3. **Fix Tool Response Propagation**
Ensure tool responses are properly included in subsequent agent calls:
```python
# Current: Each step starts fresh
messages = [HumanMessage(full_system_prompt)]

# Better: Include previous context
messages = [
    HumanMessage(minimal_prompt),
    AIMessage(previous_tool_call),
    ToolMessage(tool_response)
]
```

### 4. **Implement Query Complexity Detection**
```python
def assess_query_complexity(query):
    simple_patterns = [
        "show me", "list", "give me", "what are",
        "recent", "latest", "few", "some"
    ]
    if any(pattern in query.lower() for pattern in simple_patterns):
        return "simple"
    return "complex"
```

### 5. **Optimize Model Selection**
- Use gemini-2.5-flash for ALL steps in simple queries
- Reserve gemini-2.5-pro only for complex analytical queries

## Proposed Optimized Workflow

For "Give me a few app ids":

1. **Query Classification** (200 tokens) - Simplified prompt
2. **Direct Execution** (500 tokens) - Skip planning, execute MongoDB query
3. **Format Response** (300 tokens) - Format and return

**Total**: ~1,000 tokens vs current 36,306 tokens (**97% reduction**)

## Implementation Priority

1. **High**: Fix tool response context propagation
2. **High**: Implement prompt compression
3. **Medium**: Add query complexity detection
4. **Medium**: Optimize model selection
5. **Low**: Refactor planning logic

## Conclusion

The current workflow is severely over-engineered for simple queries. By implementing these optimizations, we can reduce token usage by 97% and costs by similar margins while maintaining quality and actually improving response times.
