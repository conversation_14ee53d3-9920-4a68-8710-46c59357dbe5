# Multi-Run Support Implementation

## Overview

This implementation addresses the critical issue where **a single chat session can have multiple run-ids**, ensuring proper correlation between user messages, bot responses, and agent logs.

## Problem Statement

Previously, when a user had multiple interactions within the same chat session:

```
Session-123 starts
User: "What is 2+2?" → run_id_1
Bot: "4"
User: "What about 3+3?" → run_id_2
Bot: "6"
```

**Issues**:
- User messages weren't associated with their corresponding run_id
- No way to correlate which user message triggered which agent run
- Logs were separated but messages weren't properly linked
- Session retrieval didn't preserve run context

## Solution Architecture

### 1. Enhanced Message Storage

#### User Messages
- **Now**: `store_user_message(session_id, content, user_id, run_id)`
- **Storage**: User messages are stored with their triggering `run_id` in metadata
- **Timing**: Stored immediately when `agent.run()` starts (before processing)

#### Bot Messages
- **Existing**: Already stored `run_id` in metadata
- **Enhancement**: Now correlates properly with user messages from same run

### 2. Enhanced Data Retrieval

#### Basic Session Retrieval
```python
# Returns all messages with run_id information
chat_data = db_manager.get_chat_session(session_id)
# Each message now includes:
# - run_id: Associated run identifier
# - timestamp: When message was created
# - message_index: Sequence in conversation
```

#### Run-Specific Queries
```python
# Get all run_ids for a session
run_ids = db_manager.get_session_run_ids(session_id)

# Get messages and logs for specific run
run_data = db_manager.get_session_by_run_id(session_id, run_id)

# Get complete session organized by runs
detailed_session = db_manager.get_session_with_run_details(session_id)
```

### 3. New API Endpoints

#### `/session/<session_id>/runs`
Lists all run_ids for a session:
```json
{
  "session_id": "session-123",
  "run_ids": ["run-uuid-1", "run-uuid-2"],
  "total_runs": 2
}
```

#### `/session/<session_id>/run/<run_id>`
Gets detailed data for a specific run:
```json
{
  "session_id": "session-123",
  "run_id": "run-uuid-1",
  "messages": [...],
  "logs": [...],
  "message_count": 2,
  "log_count": 15
}
```

#### `/session/<session_id>/detailed`
Gets complete session organized by runs:
```json
{
  "title": "Session Title",
  "history": [...],
  "runs": {
    "run-uuid-1": {
      "messages": [...],
      "logs": [...]
    },
    "run-uuid-2": {
      "messages": [...],
      "logs": [...]
    }
  },
  "run_ids": ["run-uuid-1", "run-uuid-2"],
  "total_runs": 2
}
```

## Implementation Details

### 1. Database Schema Changes

#### `chat_messages` Collection
```javascript
{
  "session_id": "session-123",
  "message_index": 0,
  "type": "user|bot",
  "content": "What is 2+2?",
  "timestamp": ISODate("..."),
  "metadata": {
    "run_id": "run-uuid-1",        // ← NEW: Links to agent run
    "user_id": "user-456",
    "input_length": 12
  }
}
```

#### Enhanced Indexes
- `metadata.run_id` - For efficient run-specific queries
- `(session_id, run_id)` - For session-run combinations

### 2. Flow Changes

#### Before (Problematic)
```
1. User sends message → Routes stores to MongoDB (no run_id)
2. Agent.run() → Generates run_id internally
3. Agent logs → Stored with run_id
4. Bot response → Stored with run_id
❌ User message and agent processing not correlated
```

#### After (Fixed)
```
1. User sends message → Routes calls agent.run()
2. Agent.run() → Generates run_id immediately
3. Agent.run() → Stores user message with run_id
4. Agent processing → Logs stored with run_id
5. Bot response → Stored with run_id
✅ Complete correlation maintained
```

### 3. Backward Compatibility

- **Existing sessions**: Continue to work normally
- **Messages without run_id**: Still displayed in conversation flow
- **Mixed sessions**: Old messages (no run_id) + new messages (with run_id) work together
- **Graceful degradation**: If run_id is missing, operations continue normally

## Usage Examples

### Multiple Questions in Same Session

```python
# Session starts
session_id = "session-123"

# First question
answer1 = agent.run("What is 2+2?", session_id, "business")
# → Stored with run_id_1

# Second question
answer2 = agent.run("What about 3+3?", session_id, "business")
# → Stored with run_id_2

# Retrieve complete session
session = db_manager.get_chat_session(session_id)
# session["history"] now includes run_id for each message

# Get runs breakdown
runs = db_manager.get_session_run_ids(session_id)
# → ["run_id_1", "run_id_2"]

# Analyze specific run
run1_data = db_manager.get_session_by_run_id(session_id, "run_id_1")
# → Complete data for first question/answer cycle
```

### Debugging Specific Interactions

```python
# Find which run had issues
session_data = db_manager.get_session_with_run_details(session_id)

for run_id, run_data in session_data["runs"].items():
    if run_data["log_count"] > 50:  # High log volume
        print(f"Run {run_id} had {run_data['log_count']} logs")

        # Get detailed logs for this run
        logs = db_manager.get_session_logs(session_id)
        run_logs = [log for log in logs if log.get("run_id") == run_id]
```

## Benefits

1. **Complete Audit Trail**: Every user message now correlates with its processing run
2. **Debugging**: Can isolate issues to specific question/answer cycles
3. **Performance Analysis**: Compare processing times between different runs
4. **Context Preservation**: Maintain conversation flow while enabling run-specific analysis
5. **Scalability**: Efficiently query large sessions by run rather than processing all data

## Database Performance

With proper indexing on `metadata.run_id`, queries are efficient:
- **Session retrieval**: Same performance as before
- **Run-specific queries**: Fast lookups via run_id index
- **Cross-run analysis**: Efficient aggregation across runs

## Monitoring and Analytics

The enhanced structure enables:
- **Run success rates**: Track which runs complete successfully
- **Performance per run**: Measure processing time, token usage, etc.
- **Error correlation**: Link errors to specific user questions
- **Usage patterns**: Analyze multi-turn conversation flows

This implementation ensures that **every interaction within a session maintains perfect correlation** between user input, agent processing, and bot output, regardless of how many questions are asked within the same session.
