# Log Agent LangGraph Workflow Diagram

This diagram shows the LangGraph Plan-and-Execute workflow for the Log Intelligence Agent (`src/log_agent.py`).

```mermaid
graph TD
    START([START]) --> consult_memory[🗃️ Consult Memory]
    consult_memory --> plan[🤖 Plan]
    plan --> execute[Execute]
    execute --> agent[🤖 Agent]

    agent --> tools_condition{Tools Needed?}
    tools_condition -->|tools| tools[🗄️ Tools]
    tools_condition -->|continue_execution| process_result[Process Result]

    tools --> process_result

    process_result --> should_continue{More Steps?}
    should_continue -->|execute| execute
    should_continue -->|finish| finish[🤖 Finish]

    finish --> END([END])

    classDef startEnd fill:#ffffff,stroke:#2196f3,stroke-width:2px,color:#000000
    classDef process fill:#ffffff,stroke:#9c27b0,stroke-width:2px,color:#000000
    classDef decision fill:#fff8e1,stroke:#ff9800,stroke-width:2px,color:#000000
    classDef mongodb fill:#e8f5e8,stroke:#2e7d32,stroke-width:4px,stroke-dasharray: 8 4,color:#000000
    classDef llm fill:#ffebee,stroke:#c62828,stroke-width:4px,stroke-dasharray: 5 3,color:#000000

    class START,END startEnd
    class execute,process_result process
    class tools_condition,should_continue decision
    class consult_memory,tools mongodb
    class plan,agent,finish llm
```

## Workflow Description

The LangGraph workflow implements a Plan-and-Execute pattern with the following steps:

1. **🗃️ consult_memory**: Retrieves relevant context from memory manager *(MONGODB QUERY)*
2. **🤖 plan**: Creates execution plan steps based on user input *(LLM CALL)*
3. **execute**: Executes the current step from the plan
4. **🤖 agent**: LLM agent processes the step and determines next action *(LLM CALL)*
5. **🗄️ tools**: MongoDB query tools (executed if needed by agent) *(MONGODB QUERY)*
6. **process_result**: Processes step results and updates state
7. **🤖 finish**: Generates final response when all steps complete *(LLM CALL)*

The workflow loops between execute → agent → (optionally tools) → process_result → back to execute until all plan steps are completed.

**Legend**:
- 🤖 = Nodes that execute LLM calls (red dashed border)
- 🗃️🗄️ = Nodes that run MongoDB queries (green dashed border)
- Regular nodes = Non-LLM, non-database processing steps

## Convert to Image

<!-- To convert this mermaid diagram to an image, use one of these commands:

     Option 1 - Using mermaid-cli (install with: npm install -g @mermaid-js/mermaid-cli):
     mmdc -i docs/LOG_AGENT_LANGGRAPH_DIAGRAM.md -o docs/log_agent_workflow.png

     Option 2 - Using GitHub's mermaid rendering:
     View this file on GitHub and the diagram will render automatically

     Option 3 - Using VS Code with Mermaid Preview extension:
     Install "Mermaid Preview" extension and use Ctrl+Shift+P -> "Mermaid Preview"

     Option 4 - Online converter:
     Copy the mermaid code to https://mermaid.live/ and export as PNG/SVG
-->
