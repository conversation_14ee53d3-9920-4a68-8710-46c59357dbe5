#!/usr/bin/env python3
"""
Application entry point that handles both test execution and server startup.
Runs all unit tests before starting the Flask application server.
"""

import os
import sys
import unittest

from app import create_app


def run_tests() -> bool:
    """
    Run all unit tests in the 'tests/test_connections.py' file.

    Returns:
        bool: True if all tests passed, False otherwise
    """
    if os.getenv("SKIP_TESTS", "false").lower() == "true":
        return True

    test_loader = unittest.TestLoader()
    test_suite = test_loader.loadTestsFromName("tests.test_connections")
    test_runner = unittest.TextTestRunner(verbosity=2)
    test_result = test_runner.run(test_suite)
    return test_result.wasSuccessful()


def main() -> None:
    """
    Main entry point of the application.
    Runs tests and starts the Flask server if tests pass.
    """

    env_name = os.getenv("ENV", "local")
    env_file = f".env.{env_name}"
    print(f"Running in {env_name} environment. See {env_file} for configuration.")
    try:
        with open(env_file, "r") as f:
            print(f"\nContents of {env_file}:\n{'-'*40}")
            print(f.read())
            print("-" * 40)
    except FileNotFoundError:
        print(f"Warning: {env_file} not found.")
    # Run tests first
    if not run_tests():
        print("Unit tests failed. Exiting without starting the app.")
        sys.exit(1)

    # Create and configure the Flask app
    app = create_app()

    # Start the Flask application (DEVELOPMENT ONLY)
    port = int(os.getenv("PORT", 8080))
    print(f"\n{'='*60}")
    print("🚨 DEVELOPMENT SERVER - NOT FOR PRODUCTION USE")
    print("For production deployment, use: ./start_production.sh")
    print(f"{'='*60}\n")
    app.run(host="0.0.0.0", port=port, debug=True)


if __name__ == "__main__":
    main()
