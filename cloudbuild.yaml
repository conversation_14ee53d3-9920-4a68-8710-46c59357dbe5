# Cloud Build configuration to deploy multiple Alfred services from main branch
# Matches existing image naming convention

steps:
  # Build the Docker image once
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '--no-cache'
      - '-t'
      - '$_AR_HOSTNAME/$_AR_PROJECT_ID/$_AR_REPOSITORY/$REPO_NAME/$_SERVICE_NAME:$COMMIT_SHA'
      - '.'
      - '-f'
      - 'Dockerfile'
    id: 'Build'

  # Push the image to Artifact Registry
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - '$_AR_HOSTNAME/$_AR_PROJECT_ID/$_AR_REPOSITORY/$REPO_NAME/$_SERVICE_NAME:$COMMIT_SHA'
    id: 'Push'

  # Deploy alfred (production) service
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:slim'
    entrypoint: 'gcloud'
    args:
      - 'run'
      - 'services'
      - 'update'
      - 'alfred'
      - '--platform=managed'
      - '--image=$_AR_HOSTNAME/$_AR_PROJECT_ID/$_AR_REPOSITORY/$REPO_NAME/$_SERVICE_NAME:$COMMIT_SHA'
      - '--labels=managed-by=gcp-cloud-build-deploy-cloud-run,commit-sha=$COMMIT_SHA,gcb-build-id=$BUILD_ID,gcb-trigger-id=$_TRIGGER_ID'
      - '--region=$_DEPLOY_REGION'
      - '--quiet'
    id: 'Deploy-alfred'
    waitFor: ['Push']

  # Deploy alfred-homepro service
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:slim'
    entrypoint: 'gcloud'
    args:
      - 'run'
      - 'services'
      - 'update'
      - 'alfred-homepro'
      - '--platform=managed'
      - '--image=$_AR_HOSTNAME/$_AR_PROJECT_ID/$_AR_REPOSITORY/$REPO_NAME/$_SERVICE_NAME:$COMMIT_SHA'
      - '--labels=managed-by=gcp-cloud-build-deploy-cloud-run,commit-sha=$COMMIT_SHA,gcb-build-id=$BUILD_ID,gcb-trigger-id=$_TRIGGER_ID'
      - '--region=$_DEPLOY_REGION'
      - '--quiet'
    id: 'Deploy-alfred-homepro'
    waitFor: ['Push']


# Images to be pushed to the registry
images:
  - '$_AR_HOSTNAME/$_AR_PROJECT_ID/$_AR_REPOSITORY/$REPO_NAME/$_SERVICE_NAME:$COMMIT_SHA'

# Build options
options:
  logging: CLOUD_LOGGING_ONLY
  substitutionOption: ALLOW_LOOSE

# Substitutions matching existing trigger values
substitutions:
  REPO_NAME: 'alfred'
  _AR_HOSTNAME: 'us-central1-docker.pkg.dev'
  _AR_PROJECT_ID: 'llogic-skai'
  _AR_REPOSITORY: 'cloud-run-source-deploy'
  _DEPLOY_REGION: 'us-central1'
  _PLATFORM: 'managed'
  _SERVICE_NAME: 'alfred'

# Tags for the build
tags:
  - gcp-cloud-build-deploy-cloud-run
  - gcp-cloud-build-deploy-cloud-run-managed
  - alfred
  - alfred-homepro

# Timeout for the entire build
timeout: '1200s'
