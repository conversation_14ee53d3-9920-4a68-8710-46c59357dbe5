"""
REST API Plugin for Pipeline Management
Provides endpoints to create and manage data pipelines
"""

import json
from pathlib import Path
from typing import Dict, List

from airflow.plugins_manager import AirflowPlugin
from flask import Blueprint, jsonify, request

# Directory for pipeline specifications
PIPELINE_SPECS_DIR = Path("/opt/airflow/pipeline_specs")
PIPELINE_SPECS_DIR.mkdir(exist_ok=True)


def validate_spec(spec: Dict) -> List[str]:
    """Validate pipeline specification"""
    errors = []

    # Required fields
    required_fields = [
        "pipeline_name",
        "mongodb_query",
        "source_collection",
        "output_table",
    ]
    for field in required_fields:
        if field not in spec:
            errors.append(f"Missing required field: {field}")

    # Validate pipeline name
    if "pipeline_name" in spec:
        name = spec["pipeline_name"]
        if not name or not name.replace("_", "").replace("-", "").isalnum():
            errors.append(
                "Pipeline name must be alphanumeric with underscores/hyphens only"
            )

    # Validate MongoDB query
    if "mongodb_query" in spec:
        query = spec["mongodb_query"]
        if "type" not in query:
            errors.append("MongoDB query must specify 'type' (find or aggregate)")
        elif query["type"] == "find":
            if "filter" not in query:
                errors.append("Find query must have 'filter' field")
        elif query["type"] == "aggregate":
            if "pipeline" not in query:
                errors.append("Aggregate query must have 'pipeline' field")

    # Validate output table
    if "output_table" in spec:
        table = spec["output_table"]
        if "dataset" not in table:
            errors.append("Output table must specify 'dataset'")
        if "table" not in table:
            errors.append("Output table must specify 'table' name")
        if "columns" not in table or not table["columns"]:
            errors.append("Output table must specify 'columns' list")
        else:
            for idx, col in enumerate(table["columns"]):
                if "name" not in col:
                    errors.append(f"Column {idx} missing 'name'")
                if "type" not in col:
                    errors.append(f"Column {idx} missing 'type'")
                if "source" not in col:
                    errors.append(f"Column {idx} missing 'source'")

    # Validate schedule if present
    if "schedule" in spec:
        valid_presets = ["@once", "@hourly", "@daily", "@weekly", "@monthly", "@yearly"]
        schedule = spec["schedule"]
        if not (schedule in valid_presets or schedule.count(" ") == 4):
            errors.append(f"Invalid schedule format: {schedule}")

    return errors


# Create Blueprint for API endpoints
pipeline_bp = Blueprint("pipeline_api", __name__, url_prefix="/api/v1")


@pipeline_bp.route("/pipeline", methods=["POST"])
def create_pipeline():
    """Create a new pipeline from specification"""
    try:
        spec = request.json

        # Validate specification
        errors = validate_spec(spec)
        if errors:
            return jsonify({"status": "error", "errors": errors}), 400

        # Check if pipeline already exists
        spec_file = PIPELINE_SPECS_DIR / f"{spec['pipeline_name']}.json"
        if spec_file.exists():
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": f"Pipeline '{spec['pipeline_name']}' already exists",
                    }
                ),
                409,
            )

        # Save specification
        with open(spec_file, "w") as f:
            json.dump(spec, f, indent=2)

        return (
            jsonify(
                {
                    "status": "created",
                    "pipeline_name": spec["pipeline_name"],
                    "dag_id": spec["pipeline_name"],
                    "message": "Pipeline created successfully. DAG will be available within 30 seconds.",
                }
            ),
            201,
        )

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500


@pipeline_bp.route("/pipeline/<pipeline_name>", methods=["GET"])
def get_pipeline(pipeline_name: str):
    """Get pipeline specification"""
    try:
        spec_file = PIPELINE_SPECS_DIR / f"{pipeline_name}.json"

        if not spec_file.exists():
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": f"Pipeline '{pipeline_name}' not found",
                    }
                ),
                404,
            )

        with open(spec_file) as f:
            spec = json.load(f)

        return jsonify(spec), 200

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500


@pipeline_bp.route("/pipeline/<pipeline_name>", methods=["PUT"])
def update_pipeline(pipeline_name: str):
    """Update existing pipeline specification"""
    try:
        spec = request.json
        spec_file = PIPELINE_SPECS_DIR / f"{pipeline_name}.json"

        if not spec_file.exists():
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": f"Pipeline '{pipeline_name}' not found",
                    }
                ),
                404,
            )

        # Ensure pipeline name matches
        spec["pipeline_name"] = pipeline_name

        # Validate specification
        errors = validate_spec(spec)
        if errors:
            return jsonify({"status": "error", "errors": errors}), 400

        # Update specification
        with open(spec_file, "w") as f:
            json.dump(spec, f, indent=2)

        return (
            jsonify(
                {
                    "status": "updated",
                    "pipeline_name": pipeline_name,
                    "message": "Pipeline updated successfully",
                }
            ),
            200,
        )

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500


@pipeline_bp.route("/pipeline/<pipeline_name>", methods=["DELETE"])
def delete_pipeline(pipeline_name: str):
    """Delete pipeline specification"""
    try:
        spec_file = PIPELINE_SPECS_DIR / f"{pipeline_name}.json"

        if not spec_file.exists():
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": f"Pipeline '{pipeline_name}' not found",
                    }
                ),
                404,
            )

        # Remove specification file
        spec_file.unlink()

        return (
            jsonify(
                {
                    "status": "deleted",
                    "pipeline_name": pipeline_name,
                    "message": "Pipeline deleted successfully",
                }
            ),
            200,
        )

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500


@pipeline_bp.route("/pipelines", methods=["GET"])
def list_pipelines():
    """List all pipelines"""
    try:
        pipelines = []

        for spec_file in PIPELINE_SPECS_DIR.glob("*.json"):
            with open(spec_file) as f:
                spec = json.load(f)
                pipelines.append(
                    {
                        "pipeline_name": spec["pipeline_name"],
                        "schedule": spec.get("schedule", "@daily"),
                        "source_collection": spec["source_collection"],
                        "output_table": f"{spec['output_table']['dataset']}.{spec['output_table']['table']}",
                    }
                )

        return (
            jsonify(
                {"status": "success", "count": len(pipelines), "pipelines": pipelines}
            ),
            200,
        )

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500


# Define the plugin class
class PipelineApiPlugin(AirflowPlugin):
    name = "pipeline_api"
    flask_blueprints = [pipeline_bp]
