"""
Dynamic DAG Factory for MongoDB to BigQuery Pipelines
Automatically creates DAGs from JSON specifications in pipeline_specs directory
"""

import json
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict

from airflow import DAG
from airflow.operators.python import PythonOperator
# from airflow.providers.google.cloud.transfers.mongodb_to_gcs import MongoDBToGCSOperator  # This doesn't exist
from airflow.providers.google.cloud.transfers.gcs_to_bigquery import GCSToBigQueryOperator
from airflow.providers.mongo.hooks.mongo import MongoHook
from airflow.providers.google.cloud.hooks.bigquery import BigQueryHook


# Directory containing pipeline specifications
PIPELINE_SPECS_DIR = Path("/opt/airflow/pipeline_specs")


def extract_from_mongodb(**context):
    """Extract data from MongoDB for specific time window"""
    spec = context['params']['spec']
    
    # Get Airflow's data interval
    data_interval_start = context['data_interval_start']
    data_interval_end = context['data_interval_end']
    
    # Replace template variables in MongoDB query
    query = spec['mongodb_query'].copy()
    query_str = json.dumps(query)
    query_str = query_str.replace('{{data_interval_start}}', data_interval_start.isoformat())
    query_str = query_str.replace('{{data_interval_end}}', data_interval_end.isoformat())
    query_str = query_str.replace('{{ds}}', context['ds'])  # execution date
    query = json.loads(query_str)
    
    # Connect to MongoDB
    mongo_hook = MongoHook(conn_id='mongo_default')
    client = mongo_hook.get_conn()
    db = client[os.environ.get('MONGODB_DB', 'logs_db')]
    collection = db[spec['source_collection']]
    
    # Execute query based on type
    if query.get('type') == 'find':
        cursor = collection.find(
            filter=query.get('filter', {}),
            projection=query.get('projection')
        )
        if 'sort' in query:
            cursor = cursor.sort(query['sort'])
        if 'limit' in query:
            cursor = cursor.limit(query['limit'])
        results = list(cursor)
    elif query.get('type') == 'aggregate':
        results = list(collection.aggregate(query['pipeline']))
    else:
        raise ValueError(f"Unsupported query type: {query.get('type')}")
    
    # Transform results according to column mappings
    transformed_results = []
    for record in results:
        transformed_record = {}
        for column in spec['output_table']['columns']:
            source_field = column['source']
            
            # Handle special template variables
            if source_field == '{{ds}}':
                value = context['ds']
            elif source_field == '{{data_interval_start}}':
                value = data_interval_start.isoformat()
            elif source_field == '{{data_interval_end}}':
                value = data_interval_end.isoformat()
            elif source_field == 'NOW()':
                value = datetime.utcnow().isoformat()
            else:
                # Navigate nested fields (e.g., "response.body.data.status")
                value = record
                for field in source_field.split('.'):
                    value = value.get(field) if isinstance(value, dict) else None
                    if value is None:
                        break
            
            transformed_record[column['name']] = value
        
        transformed_results.append(transformed_record)
    
    # Store results in XCom for next task
    return transformed_results


def load_to_bigquery(**context):
    """Load transformed data to BigQuery"""
    spec = context['params']['spec']
    data = context['task_instance'].xcom_pull(task_ids='extract_mongodb')
    
    if not data:
        print("No data to load")
        return
    
    # Connect to BigQuery
    bq_hook = BigQueryHook(
        gcp_conn_id='bigquery_default',
        use_legacy_sql=False
    )
    
    # Prepare table reference
    project_id = os.environ.get('GCP_PROJECT_ID')
    dataset_id = spec['output_table']['dataset']
    table_id = spec['output_table']['table']
    table_ref = f"{project_id}.{dataset_id}.{table_id}"
    
    # Create table if it doesn't exist
    schema = []
    for column in spec['output_table']['columns']:
        schema.append({
            'name': column['name'],
            'type': column['type'],
            'mode': 'NULLABLE'
        })
    
    # Insert data based on write mode
    write_mode = spec.get('write_mode', 'append')
    
    if write_mode == 'replace':
        # Delete existing data first
        bq_hook.run_query(
            sql=f"DELETE FROM `{table_ref}` WHERE 1=1",
            use_legacy_sql=False,
            autocommit=True
        )
    
    # Insert new data
    bq_hook.insert_all(
        project_id=project_id,
        dataset_id=dataset_id,
        table_id=table_id,
        rows=data,
        ignore_unknown_values=True,
        skip_invalid_rows=False,
        fail_on_error=True
    )
    
    print(f"Loaded {len(data)} rows to {table_ref}")


def create_dag_from_spec(spec_file: Path) -> DAG:
    """Create a DAG from a pipeline specification file"""
    
    # Load specification
    with open(spec_file) as f:
        spec = json.load(f)
    
    # Default arguments for the DAG
    default_args = {
        'owner': spec.get('owner', 'data_pipeline'),
        'depends_on_past': False,
        'start_date': datetime(2024, 1, 1),
        'email_on_failure': spec.get('error_handling', {}).get('alert_on_failure', False),
        'email_on_retry': False,
        'retries': spec.get('error_handling', {}).get('retries', 2),
        'retry_delay': timedelta(
            minutes=spec.get('error_handling', {}).get('retry_delay_minutes', 5)
        ),
    }
    
    # Add email if specified
    if 'alert_email' in spec.get('error_handling', {}):
        default_args['email'] = [spec['error_handling']['alert_email']]
    
    # Create the DAG
    dag = DAG(
        spec['pipeline_name'],
        default_args=default_args,
        description=f"Pipeline: {spec['pipeline_name']}",
        schedule_interval=spec.get('schedule', '@daily'),
        catchup=False,
        tags=['mongodb', 'bigquery', 'etl'],
        params={'spec': spec}
    )
    
    with dag:
        # Task 1: Extract from MongoDB
        extract_task = PythonOperator(
            task_id='extract_mongodb',
            python_callable=extract_from_mongodb,
            params={'spec': spec},
            provide_context=True
        )
        
        # Task 2: Load to BigQuery
        load_task = PythonOperator(
            task_id='load_to_bigquery',
            python_callable=load_to_bigquery,
            params={'spec': spec},
            provide_context=True
        )
        
        # Set task dependencies
        extract_task >> load_task
    
    return dag


# Auto-discover and create DAGs from specification files
if PIPELINE_SPECS_DIR.exists():
    for spec_file in PIPELINE_SPECS_DIR.glob("*.json"):
        try:
            dag_id = spec_file.stem  # filename without extension
            dag = create_dag_from_spec(spec_file)
            # Register DAG in global namespace
            globals()[dag_id] = dag
        except Exception as e:
            print(f"Error creating DAG from {spec_file}: {e}")
            continue