# Alfred Deployment Guide

## Development vs Production

### Development Server (Current Warning)
- The Flask development server warning you're seeing is normal for development
- Use `python run.py` for development only
- Includes hot reloading and debug features

### Production Deployment

#### Option 1: Gunicorn (Recommended)
```bash
# Install production dependencies
pip install -r requirements.txt

# Start with production script
./start_production.sh
```

#### Option 2: Manual Gunicorn
```bash
# Basic production setup
gunicorn --bind 0.0.0.0:8080 --workers 4 --timeout 120 wsgi:app

# Advanced configuration
gunicorn \
    --bind 0.0.0.0:8080 \
    --workers 4 \
    --timeout 120 \
    --worker-class sync \
    --max-requests 1000 \
    --preload \
    wsgi:app
```

#### Option 3: Docker Production
```dockerfile
FROM python:3.10-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

# Production command
CMD ["gunicorn", "--bind", "0.0.0.0:8080", "--workers", "4", "--timeout", "120", "wsgi:app"]
```

## Configuration

### Environment Variables
- `PORT`: Server port (default: 8080)
- `WORKERS`: Number of Gunicorn workers (default: 4)
- `TIMEOUT`: Request timeout in seconds (default: 120)
- `FLASK_ENV`: Set to `production` for production deployments

### Worker Sizing
- **CPU-bound tasks**: workers = (2 × CPU cores) + 1
- **I/O-bound tasks**: workers = (4 × CPU cores) + 1
- **Your app**: I/O-bound (MongoDB, AI API calls) - use higher worker count

## Security Considerations

1. **Never use Flask dev server in production**
2. **Set `FLASK_ENV=production`**
3. **Use environment variables for secrets**
4. **Enable HTTPS in production**
5. **Use a reverse proxy (nginx) for static files**

## Monitoring

The production setup includes:
- Access logs to stdout
- Error logs to stderr
- Request timeouts (120s for AI operations)
- Worker process management
- Graceful shutdowns
