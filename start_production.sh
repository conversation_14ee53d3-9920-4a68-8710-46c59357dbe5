#!/bin/bash
"""
Production startup script using Gun<PERSON>.
Run this script instead of run.py for production deployments.
"""

# Set production environment
export FLASK_ENV=production

# Get port from environment or default to 8080
PORT=${PORT:-8080}

# Number of worker processes (adjust based on your server specs)
# Temporarily set to 1 to fix session task storage issue with multiple workers
WORKERS=${WORKERS:-1}

# Timeout for requests (increase for long-running operations)
TIMEOUT=${TIMEOUT:-120}

# Start application with Gun<PERSON>
echo "Starting Alfred application with Gun<PERSON>..."
echo "Workers: $WORKERS, Threads: 8, Port: $PORT, Timeout: ${TIMEOUT}s"

gunicorn \
    --bind 0.0.0.0:$PORT \
    --workers $WORKERS \
    --threads 8 \
    --timeout $TIMEOUT \
    --worker-class sync \
    --worker-connections 1000 \
    --max-requests 1000 \
    --max-requests-jitter 100 \
    --access-logfile - \
    --error-logfile - \
    --log-level info \
    wsgi:app
