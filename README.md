# Alfred - Intelligent Log Analysis Assistant

Alfred is an AI-powered assistant that helps analyze application logs using Google GenAI. It processes logs from microservices and uses a Google GenAI-powered agent to answer complex analytical questions with support for multiple tools.

## Features

- **Intelligent Analysis**: Uses Google GenAI to understand and answer questions about your logs
- **Multiple Tools Support**: Supports multiple different types of tools simultaneously (e.g., MongoDB queries + search tools)
- **Environment-Based Configuration**: Supports multiple environments (prod/uat/test) through environment files
- **Secure Credentials**: Integrates with GCP Secret Manager for secure credential management
- **Flexible Log Processing**: Handles logs from multiple services and applications

## Architecture

```mermaid
graph TD
    A[User Query] --> B[Alfred Agent]
    B --> C[Log Processing]
    C --> D[Google GenAI]
    D --> E[Response Generation]
    E --> F[User Response]
```

*The diagram above illustrates the agentic workflow, where a user query is broken down into a plan, executed by tools against the processed logs, and synthesized into a final answer.*

## Prerequisites

1. Python 3.7 or higher
2. Google Cloud Platform account
3. MongoDB instance
4. Required Python packages (see `requirements.txt`)

## Setup

1. Clone the repository:
```bash
git clone https://github.com/yourusername/alfred.git
cd alfred
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Set up environment files:
   - Create `.env.prod` for production
   - Create `.env.uat` for UAT
   - Create `.env.test` for testing

   Example `.env` file:
   ```
   GCP_PROJECT_ID=your-project-id
   GCP_LOCATION=your-location
   MONGODB_URI=your-mongodb-uri
   MONGODB_DB=your-db-name
   MONGODB_COLLECTION=your-collection-name
   MODEL_NAME=your-model-name
   LOG_LEVEL=INFO
   ```

4. Follow the GCP setup instructions in `gcp/gcp_setup_instructions.md`

## Usage

1. Set the environment:
```bash
export ENV=prod  # or uat/test
```

2. Run the agent:
```bash
python -m src "your question about the logs"
```

## Development

1. Create a new virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # or `venv\Scripts\activate` on Windows
```

2. Install development dependencies:
```bash
pip install -r requirements-dev.txt
```

3. Run tests:
```bash
python -m pytest tests/
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
