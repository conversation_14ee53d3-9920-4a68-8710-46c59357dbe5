# GCP Setup Instructions

This guide will help you set up the necessary Google Cloud Platform (GCP) resources for the Alfred project.

## Prerequisites

1. A Google Cloud Platform account with billing enabled
2. The `gcloud` CLI tool installed and configured
3. Python 3.7 or higher installed

## 1. Initial Setup

1. Set your project ID:
```bash
gcloud config set project YOUR_PROJECT_ID
```

2. Enable required APIs:
```bash
gcloud services enable \
  aiplatform.googleapis.com \
  secretmanager.googleapis.com
```

3. Set up authentication:
```bash
gcloud auth application-default login
```

## 2. Set Up Secret Manager

1. Create secrets for sensitive information:
```bash
echo -n "your-mongodb-uri" | gcloud secrets create mongodb-uri --data-file=-
```

2. <PERSON> access to the secrets:
```bash
gcloud secrets add-iam-policy-binding mongodb-uri \
  --member="serviceAccount:YOUR_SERVICE_ACCOUNT@YOUR_PROJECT.iam.gserviceaccount.com" \
  --role="roles/secretmanager.secretAccessor"
```

## 3. Environment Configuration

1. Create environment-specific `.env` files:
   - `.env.prod` for production
   - `.env.uat` for user acceptance testing
   - `.env.test` for testing

2. Add the necessary environment variables to each file:
```bash
GCP_PROJECT_ID=your-project-id
GCP_LOCATION=your-location
MONGODB_URI=your-mongodb-uri
MONGODB_DB=your-db-name
MONGODB_COLLECTION=your-collection-name
MODEL_NAME=your-model-name
LOG_LEVEL=INFO  # or DEBUG for non-prod environments
```

## Cleanup

If you want to start over or remove the created resources:

1. Delete secrets:
```bash
gcloud secrets delete mongodb-uri
```

## Next Steps

1. Update your application's environment files with the correct GCP project ID and other settings
2. Test the connection to GCP services using the provided test scripts
3. Deploy your application and verify all services are working as expected
