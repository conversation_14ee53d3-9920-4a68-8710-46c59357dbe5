#!/usr/bin/env python3
"""
Comprehensive environment setup script for Alfred infrastructure.
Sets up complete environment including:
- .env.<env> file
- alfred-<env> Cloud Run service
- alfred-log-ingestion-<env> Cloud Run service
- Pub/Sub topic and subscription with dead letter
- Cloud Storage buckets for logs and dead letter

Usage: python setup_complete_environment.py <env_name>
Example: python setup_complete_environment.py homepro
"""

import argparse
import json
import os
import re
import subprocess
import sys
import tempfile
from typing import Dict, Optional


class ComprehensiveEnvironmentSetup:
    def __init__(
        self,
        env_name: str,
        project_id: str,
        region: str,
        source_env: Optional[str] = None,
        topic_name: Optional[str] = None,
        subscription_name: Optional[str] = None,
        dead_letter_topic: Optional[str] = None,
        dead_letter_subscription: Optional[str] = None,
        auto_approve: bool = False,
        dry_run: bool = False,
    ):
        # Validate environment name
        self._validate_env_name(env_name)

        # Validate required parameters
        if not project_id or not isinstance(project_id, str):
            raise ValueError("Project ID must be a non-empty string")
        if not region or not isinstance(region, str):
            raise ValueError("Region must be a non-empty string")

        self.env_name = env_name
        self.project_id = project_id
        self.region = region
        self.source_env = source_env
        self.auto_approve = bool(auto_approve)
        self.dry_run = bool(dry_run)

        # Determine source services based on source_env
        if source_env and source_env.lower() != "prod":
            self.source_alfred_service = f"alfred-{source_env}"
            self.source_log_ingestion_service = f"alfred-log-ingestion-{source_env}"
        else:
            # Default to production services (no env suffix) - covers both None and "prod"
            self.source_alfred_service = "alfred"
            self.source_log_ingestion_service = "alfred-log-ingestion"

        # Target service names
        self.alfred_service = f"alfred-{env_name}"
        self.log_ingestion_service = f"alfred-log-ingestion-{env_name}"

        # Source Pub/Sub and Storage resources
        if source_env and source_env.lower() != "prod":
            self.source_topic_name = f"skeps-logs-{source_env}"
            self.source_logs_bucket = f"skeps-logs-{source_env}"
            self.source_dead_letter_bucket = f"skeps-logs-failed-{source_env}"
        else:
            # Production resources (no env suffix)
            self.source_topic_name = "skeps-logs"
            self.source_logs_bucket = "skeps-logs"
            self.source_dead_letter_bucket = "skeps-logs-failed"

        # Target Pub/Sub resources with defaults matching existing convention
        # Default pattern based on existing: skeps-logs, skeps-logs-uat, skeps-logs-otel
        self.topic_name = topic_name or f"skeps-logs-{env_name}"
        self.subscription_name = subscription_name or f"skeps-logs-{env_name}-sub"
        self.dead_letter_topic = dead_letter_topic or f"skeps-logs-failed-{env_name}"
        self.dead_letter_subscription = (
            dead_letter_subscription or f"skeps-logs-failed-{env_name}-sub-gcs"
        )

        # Target Cloud Storage buckets matching existing convention
        # Pattern based on existing: skeps-logs, skeps-logs-uat, skeps-logs-failed-uat
        self.logs_bucket = f"skeps-logs-{env_name}"
        self.dead_letter_bucket = f"skeps-logs-failed-{env_name}"

        # Track what was created for potential rollback
        self.created_resources = []
        self.alfred_service_url = None
        self.log_ingestion_service_url = None

    def _validate_env_name(self, env_name: str):
        """Validate environment name according to GCP naming requirements."""
        if not env_name:
            raise ValueError("Environment name cannot be empty")

        # Must be lowercase, alphanumeric, and hyphens only
        if not re.match(r"^[a-z0-9]+(-[a-z0-9]+)*$", env_name):
            raise ValueError(
                f"Environment name '{env_name}' is invalid. "
                "Must be lowercase, alphanumeric with hyphens, "
                "and cannot start or end with a hyphen."
            )

        if len(env_name) > 30:
            raise ValueError(
                f"Environment name '{env_name}' is too long (max 30 characters)"
            )

    def ask_approval(
        self, action: str, details: str = "", critical: bool = False
    ) -> bool:
        """Ask for user approval before proceeding with an action."""
        if self.auto_approve and not critical:
            return True

        if critical:
            print(f"\n⚠️  CRITICAL ACTION: {action}")
        else:
            print(f"\n🔔 Next Step: {action}")

        if details:
            print(f"📋 Details:\n{details}")

        if self.dry_run:
            print("🔍 DRY RUN MODE - No actual changes will be made")
            return True

        while True:
            prompt = (
                "\nProceed? (y/n): "
                if not critical
                else "\n⚠️  Are you SURE? (yes/no): "
            )
            response = input(prompt).lower().strip()

            if critical:
                if response == "yes":
                    return True
                elif response == "no":
                    print("❌ Operation cancelled by user.")
                    return False
                else:
                    print("Please type 'yes' or 'no' exactly.")
            else:
                if response == "y":
                    return True
                elif response == "n":
                    print("❌ Operation cancelled by user.")
                    return False
                else:
                    print("Please enter 'y' for yes or 'n' for no.")

    def run_command(
        self, cmd: list, capture_output: bool = True, check: bool = True
    ) -> subprocess.CompletedProcess:
        """Run a command and return the result."""
        if not cmd:
            raise ValueError("Command list cannot be empty")

        # Validate command components
        cmd_str = []
        for component in cmd:
            if component is None:
                raise ValueError(f"Command contains None value: {cmd}")
            cmd_str.append(str(component))

        print(f"🔧 Command: {' '.join(cmd_str)}")

        if self.dry_run:
            print("🔍 DRY RUN - Command not executed")
            # Return a fake successful result
            return subprocess.CompletedProcess(
                args=cmd, returncode=0, stdout="", stderr=""
            )

        try:
            result = subprocess.run(cmd_str, capture_output=capture_output, text=True)

            if check and result.returncode != 0:
                error_msg = (
                    result.stderr or result.stdout or "No error message available"
                )
                print(f"❌ Error: {error_msg}")
                raise subprocess.CalledProcessError(
                    result.returncode, cmd, result.stdout, result.stderr
                )

            return result
        except FileNotFoundError:
            raise FileNotFoundError(
                f"Command not found: {cmd_str[0]}. Please ensure gcloud SDK is installed."
            )
        except Exception as e:
            raise RuntimeError(
                f"Failed to execute command {' '.join(cmd_str)}: {str(e)}"
            )

    def get_project_number(self) -> str:
        """Get the project number for the current project."""
        try:
            cmd = [
                "gcloud",
                "projects",
                "describe",
                self.project_id,
                "--format=value(projectNumber)",
            ]
            result = self.run_command(cmd)
            return result.stdout.strip()
        except Exception as e:
            raise RuntimeError(f"Failed to get project number: {e}")

    def check_existing_resources(self) -> Dict[str, bool]:
        """Check which resources already exist to avoid conflicts."""
        print("\n🔍 Checking for existing resources...")

        existing = {
            "env_file": os.path.exists(f".env.{self.env_name}"),
            "alfred_service": False,
            "log_ingestion_service": False,
            "topic": False,
            "subscription": False,
            "dead_letter_topic": False,
            "dead_letter_subscription": False,
            "logs_bucket": False,
            "dead_letter_bucket": False,
        }

        # Check Cloud Run services
        try:
            cmd = [
                "gcloud",
                "run",
                "services",
                "describe",
                self.alfred_service,
                "--platform=managed",
                f"--region={self.region}",
                f"--project={self.project_id}",
            ]
            result = self.run_command(cmd, check=False)
            existing["alfred_service"] = result.returncode == 0
        except:
            pass

        try:
            cmd = [
                "gcloud",
                "run",
                "services",
                "describe",
                self.log_ingestion_service,
                "--platform=managed",
                f"--region={self.region}",
                f"--project={self.project_id}",
            ]
            result = self.run_command(cmd, check=False)
            existing["log_ingestion_service"] = result.returncode == 0
        except:
            pass

        # Check Pub/Sub topics
        try:
            cmd = [
                "gcloud",
                "pubsub",
                "topics",
                "describe",
                self.topic_name,
                f"--project={self.project_id}",
            ]
            result = self.run_command(cmd, check=False)
            existing["topic"] = result.returncode == 0
        except:
            pass

        try:
            cmd = [
                "gcloud",
                "pubsub",
                "topics",
                "describe",
                self.dead_letter_topic,
                f"--project={self.project_id}",
            ]
            result = self.run_command(cmd, check=False)
            existing["dead_letter_topic"] = result.returncode == 0
        except:
            pass

        # Check subscriptions
        try:
            cmd = [
                "gcloud",
                "pubsub",
                "subscriptions",
                "describe",
                self.subscription_name,
                f"--project={self.project_id}",
            ]
            result = self.run_command(cmd, check=False)
            existing["subscription"] = result.returncode == 0
        except:
            pass

        # Check buckets
        try:
            cmd = [
                "gcloud",
                "storage",
                "buckets",
                "describe",
                f"gs://{self.logs_bucket}",
                f"--project={self.project_id}",
            ]
            result = self.run_command(cmd, check=False)
            existing["logs_bucket"] = result.returncode == 0
        except:
            pass

        try:
            cmd = [
                "gcloud",
                "storage",
                "buckets",
                "describe",
                f"gs://{self.dead_letter_bucket}",
                f"--project={self.project_id}",
            ]
            result = self.run_command(cmd, check=False)
            existing["dead_letter_bucket"] = result.returncode == 0
        except:
            pass

        return existing

    def create_env_file(self) -> bool:
        """Create .env.<env> file based on .env.uat template."""
        details = f"""
Will create .env.{self.env_name} file based on .env.uat template.
This file will contain environment-specific configuration.
        """

        if not self.ask_approval(f"Create .env.{self.env_name} file", details):
            return False

        print(f"\n📝 Creating .env.{self.env_name} file...")

        # Check if template exists
        if not os.path.exists(".env.uat"):
            print("❌ Error: .env.uat template file not found")
            return False

        # Read template
        with open(".env.uat", "r") as f:
            content = f.read()

        # Replace uat with env_name
        content = content.replace("-uat", f"-{self.env_name}")
        content = content.replace("uat-", f"{self.env_name}-")

        # Write new file
        if not self.dry_run:
            with open(f".env.{self.env_name}", "w") as f:
                f.write(content)
            self.created_resources.append(("env_file", f".env.{self.env_name}"))

        print(f"✅ Created .env.{self.env_name}")
        return True

    def deploy_cloud_run_service(
        self, source_service: str, target_service: str
    ) -> Optional[str]:
        """Deploy a Cloud Run service by cloning an existing one."""
        if not source_service or not target_service:
            raise ValueError("Source and target service names cannot be empty")

        try:
            # Try different import methods
            try:
                from .deploy_cloud_run_clone import CloudRunCloner
            except ImportError:
                # Add current directory to path if needed
                import os
                import sys

                current_dir = os.path.dirname(os.path.abspath(__file__))
                if current_dir not in sys.path:
                    sys.path.insert(0, current_dir)
                from deploy_cloud_run_clone import CloudRunCloner
        except ImportError as e:
            raise ImportError(
                f"Failed to import required module: {e}. Ensure deploy_cloud_run_clone.py exists in the same directory."
            )

        print(f"\n🚀 Deploying {target_service}...")

        try:
            cloner = CloudRunCloner(
                source_service=source_service,
                new_service=target_service,
                env_value=self.env_name,
                project_id=self.project_id,
                region=self.region,
                auto_approve=self.auto_approve,
            )
        except Exception as e:
            raise RuntimeError(f"Failed to initialize CloudRunCloner: {e}")

        if self.dry_run:
            print(f"🔍 DRY RUN - Would deploy {target_service}")
            return f"https://{target_service}-dry-run.a.run.app"

        service_url = None
        try:
            # Export configuration
            config = cloner.export_service_config()
            if not config:
                raise ValueError(
                    f"Failed to export configuration from {source_service}"
                )

            # Modify configuration
            config = cloner.modify_service_config(config)
            if not config:
                raise ValueError(f"Failed to modify configuration for {target_service}")

            # Deploy service
            cloner.deploy_service(config)

            # Get service URL with retries
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    service_url = cloner.get_service_url(target_service)
                    if service_url:
                        break
                except Exception as e:
                    if attempt < max_retries - 1:
                        print(
                            f"⚠️  Retry {attempt + 1}/{max_retries}: Failed to get service URL: {e}"
                        )
                        import time

                        time.sleep(2)
                    else:
                        print(
                            f"⚠️  Failed to get service URL after {max_retries} attempts: {e}"
                        )

            if not service_url:
                print("⚠️  Warning: Service deployed but URL not available")
                service_url = (
                    f"https://{target_service}-{self.project_id}.{self.region}.run.app"
                )
                print(f"    Using estimated URL: {service_url}")

            self.created_resources.append(("cloud_run_service", target_service))
            print(f"✅ Deployed {target_service}")
            return service_url

        except subprocess.CalledProcessError as e:
            error_msg = e.stderr or e.stdout or str(e)
            raise RuntimeError(
                f"Failed to deploy {target_service}. Command error: {error_msg}"
            )
        except Exception as e:
            raise RuntimeError(f"Failed to deploy {target_service}: {str(e)}")

    def create_pubsub_topic(
        self, topic_name: str, source_topic_name: Optional[str] = None
    ) -> bool:
        """Create a Pub/Sub topic, optionally copying configuration from a source topic."""
        if source_topic_name:
            details = f"""
Will create Pub/Sub topic: {topic_name}
Copying configuration from: {source_topic_name}
This will preserve message retention, schema settings, etc.
        """
        else:
            details = f"""
Will create Pub/Sub topic: {topic_name}
Using default settings (no source topic to copy from)
        """

        if not self.ask_approval(f"Create Pub/Sub topic {topic_name}", details):
            return False

        print(f"\n📬 Creating Pub/Sub topic {topic_name}...")

        # Try to get configuration from source topic if specified
        topic_config = {}
        if source_topic_name:
            try:
                print(f"  📋 Getting configuration from {source_topic_name}...")
                cmd = [
                    "gcloud",
                    "pubsub",
                    "topics",
                    "describe",
                    source_topic_name,
                    f"--project={self.project_id}",
                    "--format=json",
                ]
                result = self.run_command(cmd, check=False)

                if result.returncode == 0:
                    import json

                    source_config = json.loads(result.stdout)

                    # Extract relevant configuration
                    if "messageRetentionDuration" in source_config:
                        topic_config["message-retention-duration"] = source_config[
                            "messageRetentionDuration"
                        ]

                    if "schemaSettings" in source_config:
                        print(
                            f"  ⚠️  Note: Schema settings found but will need manual setup"
                        )

                    print(f"  ✅ Retrieved configuration from {source_topic_name}")
                else:
                    print(
                        f"  ⚠️  Warning: Could not retrieve config from {source_topic_name}, using defaults"
                    )

            except Exception as e:
                print(f"  ⚠️  Warning: Failed to get source topic config: {e}")

        # Build create command
        cmd = [
            "gcloud",
            "pubsub",
            "topics",
            "create",
            topic_name,
            f"--project={self.project_id}",
        ]

        # Add configuration options if available
        if "message-retention-duration" in topic_config:
            cmd.extend(
                [
                    "--message-retention-duration",
                    topic_config["message-retention-duration"],
                ]
            )
        else:
            # Default to 7 days like existing topics
            cmd.extend(["--message-retention-duration", "604800s"])

        try:
            self.run_command(cmd)
            self.created_resources.append(("pubsub_topic", topic_name))
            print(f"✅ Created topic {topic_name}")
            return True
        except Exception as e:
            raise RuntimeError(f"Failed to create topic {topic_name}: {e}")

    def create_cloud_storage_bucket(
        self,
        bucket_name: str,
        source_bucket_name: Optional[str] = None,
        lifecycle_days: int = 30,
    ) -> bool:
        """Create a Cloud Storage bucket, optionally copying configuration from a source bucket."""
        # Validate bucket name
        if not bucket_name:
            raise ValueError("Bucket name cannot be empty")

        # GCS bucket naming rules
        if len(bucket_name) < 3 or len(bucket_name) > 63:
            raise ValueError(
                f"Bucket name '{bucket_name}' must be 3-63 characters long"
            )

        if not re.match(r"^[a-z0-9][a-z0-9\-_]*[a-z0-9]$", bucket_name):
            raise ValueError(
                f"Bucket name '{bucket_name}' must start and end with alphanumeric, "
                "and contain only lowercase letters, numbers, hyphens, and underscores"
            )

        if lifecycle_days < 1:
            raise ValueError("Lifecycle days must be at least 1")

        if source_bucket_name:
            details = f"""
Will create Cloud Storage bucket: {bucket_name}
Copying configuration from: {source_bucket_name}
Location: {self.region}
Lifecycle: Delete objects older than {lifecycle_days} days (may be overridden by source config)
        """
        else:
            details = f"""
Will create Cloud Storage bucket: {bucket_name}
Location: {self.region}
Lifecycle: Delete objects older than {lifecycle_days} days
        """

        if not self.ask_approval(f"Create Cloud Storage bucket {bucket_name}", details):
            return False

        print(f"\n🗄️  Creating Cloud Storage bucket {bucket_name}...")

        # Try to get configuration from source bucket if specified
        source_config = {}
        if source_bucket_name:
            try:
                print(f"  📋 Getting configuration from {source_bucket_name}...")
                cmd = [
                    "gcloud",
                    "storage",
                    "buckets",
                    "describe",
                    f"gs://{source_bucket_name}",
                    f"--project={self.project_id}",
                    "--format=json",
                ]
                result = self.run_command(cmd, check=False)

                if result.returncode == 0:
                    source_config = json.loads(result.stdout)
                    print(f"  ✅ Retrieved configuration from {source_bucket_name}")
                else:
                    print(
                        f"  ⚠️  Warning: Could not retrieve config from {source_bucket_name}, using defaults"
                    )

            except Exception as e:
                print(f"  ⚠️  Warning: Failed to get source bucket config: {e}")

        # Create bucket
        cmd = [
            "gcloud",
            "storage",
            "buckets",
            "create",
            f"gs://{bucket_name}",
            f"--project={self.project_id}",
            f"--location={self.region}",
            "--uniform-bucket-level-access",
        ]

        try:
            self.run_command(cmd)

            # Apply lifecycle configuration
            lifecycle_config = None

            # Use source bucket lifecycle if available, otherwise use default
            if source_config and "lifecycle" in source_config:
                lifecycle_config = {"lifecycle": source_config["lifecycle"]}
                print(f"  📋 Using lifecycle configuration from {source_bucket_name}")
            else:
                lifecycle_config = {
                    "lifecycle": {
                        "rule": [
                            {
                                "action": {"type": "Delete"},
                                "condition": {"age": lifecycle_days},
                            }
                        ]
                    }
                }
                print(
                    f"  📋 Using default lifecycle configuration ({lifecycle_days} days)"
                )

            if not self.dry_run and lifecycle_config:
                with tempfile.NamedTemporaryFile(
                    mode="w", suffix=".json", delete=False
                ) as f:
                    json.dump(lifecycle_config, f)
                    temp_file = f.name

                try:
                    cmd = [
                        "gcloud",
                        "storage",
                        "buckets",
                        "update",
                        f"gs://{bucket_name}",
                        f"--lifecycle-file={temp_file}",
                    ]
                    self.run_command(cmd)
                finally:
                    os.unlink(temp_file)

            self.created_resources.append(("storage_bucket", bucket_name))
            print(f"✅ Created bucket {bucket_name}")
            return True

        except Exception as e:
            raise RuntimeError(f"Failed to create bucket {bucket_name}: {e}")

    def create_pubsub_subscription(self) -> bool:
        """Create Pub/Sub subscription with push endpoint and dead letter topic."""
        if not self.alfred_service_url:
            raise ValueError(
                "Cannot create subscription: Alfred service URL not available. Deploy alfred service first."
            )

        if (
            not self.topic_name
            or not self.subscription_name
            or not self.dead_letter_topic
        ):
            raise ValueError(
                "Topic name, subscription name, and dead letter topic must all be specified"
            )

        # Validate URL format
        if not self.alfred_service_url.startswith(("http://", "https://")):
            raise ValueError(f"Invalid service URL format: {self.alfred_service_url}")

        push_endpoint = f"{self.alfred_service_url}/pubsub/api/log"

        details = f"""
Will create Pub/Sub subscription: {self.subscription_name}
Topic: {self.topic_name}
Push endpoint: {push_endpoint}
Dead letter topic: {self.dead_letter_topic}
Max delivery attempts: 5
Ack deadline: 600 seconds
        """

        if not self.ask_approval(
            "Create Pub/Sub subscription with dead letter", details, critical=True
        ):
            return False

        print(f"\n📮 Creating Pub/Sub subscription {self.subscription_name}...")

        # Verify dead letter topic exists first
        try:
            check_cmd = [
                "gcloud",
                "pubsub",
                "topics",
                "describe",
                self.dead_letter_topic,
                f"--project={self.project_id}",
            ]
            self.run_command(check_cmd, check=True)
        except subprocess.CalledProcessError:
            raise RuntimeError(
                f"Dead letter topic '{self.dead_letter_topic}' does not exist. Create it first."
            )

        cmd = [
            "gcloud",
            "pubsub",
            "subscriptions",
            "create",
            self.subscription_name,
            f"--topic={self.topic_name}",
            f"--push-endpoint={push_endpoint}",
            f"--ack-deadline=300",
            f"--max-delivery-attempts=5",
            f"--dead-letter-topic={self.dead_letter_topic}",
            "--max-retry-delay=600s",
            "--min-retry-delay=10s",
            "--expiration-period=31d",
            "--message-retention-duration=7d",
            f"--project={self.project_id}",
        ]

        try:
            self.run_command(cmd)

            # Verify subscription was created
            verify_cmd = [
                "gcloud",
                "pubsub",
                "subscriptions",
                "describe",
                self.subscription_name,
                f"--project={self.project_id}",
                "--format=value(name)",
            ]
            verify_result = self.run_command(verify_cmd)

            if not verify_result.stdout.strip():
                raise RuntimeError(
                    "Subscription creation succeeded but verification failed"
                )

            self.created_resources.append(
                ("pubsub_subscription", self.subscription_name)
            )
            print(f"✅ Created subscription {self.subscription_name}")
            return True

        except subprocess.CalledProcessError as e:
            error_msg = e.stderr or e.stdout or "No error details available"
            raise RuntimeError(
                f"Failed to create subscription '{self.subscription_name}': {error_msg}"
            )
        except Exception as e:
            raise RuntimeError(f"Failed to create subscription: {str(e)}")

    def create_pubsub_backup_subscriptions(self) -> bool:
        """Create Cloud Storage backup subscriptions for Pub/Sub topics."""
        details = f"""
Will create Cloud Storage backup subscriptions:
- {self.subscription_name}-gcs → backs up messages to {self.logs_bucket}
- {self.dead_letter_subscription} → backs up messages to {self.dead_letter_bucket}

These pull subscriptions automatically save all Pub/Sub messages to Cloud Storage.
Note: This will also set up required IAM permissions for the Pub/Sub service account.
        """

        if not self.ask_approval("Create Pub/Sub backup subscriptions", details):
            return False

        print("\n💾 Creating Pub/Sub backup subscriptions...")

        # Set up IAM permissions for Pub/Sub service account
        print("🔐 Setting up IAM permissions for Pub/Sub service account...")
        pubsub_service_account = (
            f"service-{self.get_project_number()}@gcp-sa-pubsub.iam.gserviceaccount.com"
        )

        # Grant permissions for main bucket
        for bucket in [self.logs_bucket, self.dead_letter_bucket]:
            try:
                # Grant Storage Legacy Bucket Reader role
                cmd = [
                    "gcloud",
                    "storage",
                    "buckets",
                    "add-iam-policy-binding",
                    f"gs://{bucket}",
                    f"--member=serviceAccount:{pubsub_service_account}",
                    "--role=roles/storage.legacyBucketReader",
                    f"--project={self.project_id}",
                ]
                self.run_command(cmd)

                # Grant Storage Object Creator role
                cmd = [
                    "gcloud",
                    "storage",
                    "buckets",
                    "add-iam-policy-binding",
                    f"gs://{bucket}",
                    f"--member=serviceAccount:{pubsub_service_account}",
                    "--role=roles/storage.objectCreator",
                    f"--project={self.project_id}",
                ]
                self.run_command(cmd)
                print(f"✅ Set IAM permissions for bucket {bucket}")
            except Exception as e:
                print(f"⚠️  Warning: Failed to set IAM permissions for {bucket}: {e}")

        # Create backup subscription for main topic (following existing pattern: topic-sub-gcs)
        backup_subscription_name = f"{self.subscription_name}-gcs"
        try:
            cmd = [
                "gcloud",
                "pubsub",
                "subscriptions",
                "create",
                backup_subscription_name,
                f"--topic={self.topic_name}",
                f"--cloud-storage-bucket={self.logs_bucket}",
                "--cloud-storage-max-duration=300s",
                "--ack-deadline=300",
                "--expiration-period=31d",
                "--message-retention-duration=7d",
                f"--project={self.project_id}",
            ]
            self.run_command(cmd)
            self.created_resources.append(
                ("pubsub_subscription", backup_subscription_name)
            )
            print(f"✅ Created backup subscription: {backup_subscription_name}")
        except Exception as e:
            print(f"❌ Failed to create backup subscription for main topic: {e}")
            return False

        # Create backup subscription for dead letter topic
        dead_letter_backup_sub = f"{self.dead_letter_topic}-gcs"
        try:
            cmd = [
                "gcloud",
                "pubsub",
                "subscriptions",
                "create",
                dead_letter_backup_sub,
                f"--topic={self.dead_letter_topic}",
                f"--cloud-storage-bucket={self.dead_letter_bucket}",
                "--cloud-storage-max-duration=300s",
                "--ack-deadline=300",
                "--expiration-period=31d",
                "--message-retention-duration=7d",
                f"--project={self.project_id}",
            ]
            self.run_command(cmd)
            self.created_resources.append(
                ("pubsub_subscription", dead_letter_backup_sub)
            )
            print(f"✅ Created backup subscription: {dead_letter_backup_sub}")
        except Exception as e:
            print(f"❌ Failed to create backup subscription for dead letter topic: {e}")
            return False

        return True

    def setup_pubsub_permissions(self) -> bool:
        """Set up necessary IAM permissions for Pub/Sub to write to dead letter topic."""
        details = """
Will grant Pub/Sub service account permission to:
- Publish to dead letter topic
- Acknowledge messages in dead letter subscription
        """

        if not self.ask_approval("Set up Pub/Sub IAM permissions", details):
            return False

        print("\n🔐 Setting up Pub/Sub permissions...")

        # Get project number
        cmd = [
            "gcloud",
            "projects",
            "describe",
            self.project_id,
            "--format=value(projectNumber)",
        ]
        try:
            result = self.run_command(cmd)
            project_number = result.stdout.strip()

            if not project_number:
                raise ValueError("Could not retrieve project number from gcloud")

            # Validate project number is numeric
            if not project_number.isdigit():
                raise ValueError(f"Invalid project number format: {project_number}")

        except subprocess.CalledProcessError as e:
            error_msg = e.stderr or e.stdout or "No error details"
            raise RuntimeError(
                f"Failed to get project number for {self.project_id}: {error_msg}"
            )
        except Exception as e:
            raise RuntimeError(f"Failed to get project number: {str(e)}")

        # Grant publisher role to the service account
        service_account = (
            f"service-{project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
        )

        cmd = [
            "gcloud",
            "pubsub",
            "topics",
            "add-iam-policy-binding",
            self.dead_letter_topic,
            f"--member=serviceAccount:{service_account}",
            "--role=roles/pubsub.publisher",
            f"--project={self.project_id}",
        ]

        try:
            self.run_command(cmd)
            print(f"✅ Granted publisher permission on dead letter topic")

            # Grant subscriber role on dead letter subscription
            cmd = [
                "gcloud",
                "pubsub",
                "subscriptions",
                "add-iam-policy-binding",
                self.dead_letter_subscription,
                f"--member=serviceAccount:{service_account}",
                "--role=roles/pubsub.subscriber",
                f"--project={self.project_id}",
            ]
            self.run_command(cmd)
            print(f"✅ Granted subscriber permission on dead letter subscription")

            return True
        except Exception as e:
            print(f"❌ Failed to set up permissions: {e}")
            return False

    def create_storage_notification(self) -> bool:
        """Create Cloud Storage notification to Pub/Sub for log backup."""
        details = f"""
Will create Cloud Storage notifications:
- From {self.logs_bucket} to {self.topic_name}
- From {self.dead_letter_bucket} to {self.dead_letter_topic}
        """

        if not self.ask_approval("Create Cloud Storage notifications", details):
            return False

        print("\n🔔 Creating Cloud Storage notifications...")

        # Notification for main logs bucket
        cmd = [
            "gcloud",
            "storage",
            "buckets",
            "notifications",
            "create",
            f"gs://{self.logs_bucket}",
            f"--topic={self.topic_name}",
            "--event-types=OBJECT_FINALIZE",
            f"--project={self.project_id}",
        ]

        try:
            self.run_command(cmd)
            print(f"✅ Created notification for {self.logs_bucket}")
        except Exception as e:
            print(f"⚠️  Warning: Failed to create notification for logs bucket: {e}")

        return True

    def show_summary(self):
        """Show a summary of what was created."""
        print("\n" + "=" * 80)
        print("🎉 ENVIRONMENT SETUP COMPLETE")
        print("=" * 80)
        print(f"Environment: {self.env_name}")
        print(f"Project: {self.project_id}")
        print(f"Region: {self.region}")

        print("\n📦 Created Resources:")

        # Group resources by type
        resources_by_type = {}
        for resource_type, resource_name in self.created_resources:
            if resource_type not in resources_by_type:
                resources_by_type[resource_type] = []
            resources_by_type[resource_type].append(resource_name)

        for resource_type, resources in resources_by_type.items():
            print(f"\n{resource_type.replace('_', ' ').title()}:")
            for resource in resources:
                print(f"  ✓ {resource}")

        print("\n🔗 Service URLs:")
        if self.alfred_service_url:
            print(f"  Alfred Service: {self.alfred_service_url}")
        if self.log_ingestion_service_url:
            print(f"  Log Ingestion Service: {self.log_ingestion_service_url}")

        print("\n📡 Pub/Sub Configuration:")
        print(f"  Topic: {self.topic_name}")
        print(f"  Subscription: {self.subscription_name}")
        print(f"  Dead Letter Topic: {self.dead_letter_topic}")
        print(f"  Dead Letter Subscription: {self.dead_letter_subscription}")

        print("\n📋 Next Steps:")
        print("1. Test the services are working correctly")
        print("2. Configure any service accounts and IAM permissions")
        print("3. Set up monitoring and alerting")
        print("4. Test the complete log ingestion pipeline")
        print("5. Configure continuous deployment if needed")
        print("=" * 80)

    def rollback(self):
        """Rollback created resources in case of failure."""
        if not self.created_resources:
            return

        print("\n⚠️  Rolling back created resources...")

        # Reverse order for rollback
        for resource_type, resource_name in reversed(self.created_resources):
            try:
                if resource_type == "env_file":
                    os.remove(resource_name)
                    print(f"✅ Removed {resource_name}")
                elif resource_type == "cloud_run_service":
                    cmd = [
                        "gcloud",
                        "run",
                        "services",
                        "delete",
                        resource_name,
                        "--platform=managed",
                        f"--region={self.region}",
                        f"--project={self.project_id}",
                        "--quiet",
                    ]
                    self.run_command(cmd)
                    print(f"✅ Deleted service {resource_name}")
                elif resource_type == "pubsub_topic":
                    cmd = [
                        "gcloud",
                        "pubsub",
                        "topics",
                        "delete",
                        resource_name,
                        f"--project={self.project_id}",
                        "--quiet",
                    ]
                    self.run_command(cmd)
                    print(f"✅ Deleted topic {resource_name}")
                elif resource_type == "pubsub_subscription":
                    cmd = [
                        "gcloud",
                        "pubsub",
                        "subscriptions",
                        "delete",
                        resource_name,
                        f"--project={self.project_id}",
                        "--quiet",
                    ]
                    self.run_command(cmd)
                    print(f"✅ Deleted subscription {resource_name}")
                elif resource_type == "storage_bucket":
                    cmd = [
                        "gcloud",
                        "storage",
                        "rm",
                        "-r",
                        f"gs://{resource_name}",
                        f"--project={self.project_id}",
                    ]
                    self.run_command(cmd)
                    print(f"✅ Deleted bucket {resource_name}")
            except Exception as e:
                print(f"⚠️  Failed to rollback {resource_type} {resource_name}: {e}")

    def preflight_checks(self) -> None:
        """Perform pre-flight checks to ensure environment is ready."""
        print("\n🔍 Performing pre-flight checks...")

        # Check gcloud is installed
        try:
            cmd = ["gcloud", "version"]
            result = self.run_command(cmd)
            if not result.stdout.strip() or "Google Cloud SDK" not in result.stdout:
                raise RuntimeError("gcloud CLI not properly installed")
            # Extract version from output
            version_line = result.stdout.split("\n")[0]
            print(f"✓ gcloud CLI found: {version_line}")
        except FileNotFoundError:
            raise RuntimeError("gcloud CLI not found. Please install Google Cloud SDK.")

        # Check authentication
        try:
            cmd = [
                "gcloud",
                "auth",
                "list",
                "--filter=status:ACTIVE",
                "--format=value(account)",
            ]
            result = self.run_command(cmd)
            account = result.stdout.strip()
            if not account:
                raise RuntimeError(
                    "No active gcloud authentication found. Run 'gcloud auth login'"
                )
            print(f"✓ Authenticated as: {account}")
        except Exception as e:
            raise RuntimeError(f"Authentication check failed: {e}")

        # Check project exists and is accessible
        try:
            cmd = [
                "gcloud",
                "projects",
                "describe",
                self.project_id,
                "--format=value(projectId)",
            ]
            result = self.run_command(cmd)
            if result.stdout.strip() != self.project_id:
                raise RuntimeError(f"Project {self.project_id} not accessible")
            print(f"✓ Project {self.project_id} accessible")
        except subprocess.CalledProcessError:
            raise RuntimeError(f"Project {self.project_id} not found or not accessible")

        # Check required APIs are enabled
        required_apis = [
            "run.googleapis.com",
            "pubsub.googleapis.com",
            "storage-api.googleapis.com",
        ]
        for api in required_apis:
            try:
                cmd = [
                    "gcloud",
                    "services",
                    "list",
                    "--enabled",
                    "--filter",
                    f"name:{api}",
                    "--format=value(name)",
                    f"--project={self.project_id}",
                ]
                result = self.run_command(cmd)
                if api not in result.stdout:
                    print(f"⚠️  Warning: {api} may not be enabled")
            except:
                pass

        print("✓ Pre-flight checks passed\n")

    def run(self):
        """Main execution flow."""
        print(f"🚀 Comprehensive Environment Setup")
        print(f"=" * 80)
        print(f"Target Environment: {self.env_name}")
        source_display = self.source_env or "prod"
        if source_display.lower() == "prod":
            source_display = "prod (no suffix)"
        print(f"Source Environment: {source_display}")
        print(f"Project: {self.project_id}")
        print(f"Region: {self.region}")
        print(f"Auto-approve: {'Yes' if self.auto_approve else 'No'}")
        print(f"Dry-run: {'Yes' if self.dry_run else 'No'}")
        print(f"\nSource Resources:")
        print(f"  Alfred Service: {self.source_alfred_service}")
        print(f"  Log Ingestion Service: {self.source_log_ingestion_service}")
        print(f"  Pub/Sub Topic: {self.source_topic_name}")
        print(f"  Logs Bucket: {self.source_logs_bucket}")
        print(f"  Dead Letter Bucket: {self.source_dead_letter_bucket}")
        print(f"\nTarget Resources:")
        print(f"  Alfred Service: {self.alfred_service}")
        print(f"  Log Ingestion Service: {self.log_ingestion_service}")
        print(f"  Pub/Sub Topic: {self.topic_name}")
        print(f"  Subscription: {self.subscription_name}")
        print(f"  Dead Letter Topic: {self.dead_letter_topic}")
        print(f"  Dead Letter Subscription: {self.dead_letter_subscription}")
        print(f"  Logs Bucket: {self.logs_bucket}")
        print(f"  Dead Letter Bucket: {self.dead_letter_bucket}")
        print(f"=" * 80)

        # Run pre-flight checks
        try:
            self.preflight_checks()
        except Exception as e:
            print(f"\n❌ Pre-flight check failed: {e}")
            sys.exit(1)

        try:
            # Check existing resources
            existing = self.check_existing_resources()

            # Show what already exists
            existing_items = [k.replace("_", " ") for k, v in existing.items() if v]
            if existing_items:
                print(f"\n⚠️  Found existing resources: {', '.join(existing_items)}")
                if not self.ask_approval(
                    "Continue with setup?",
                    "Existing resources will be skipped or updated as needed.",
                    critical=True,
                ):
                    return

            # Step 1: Create .env file
            if not existing["env_file"]:
                if not self.create_env_file():
                    raise Exception("Failed to create env file")
            else:
                print(f"✓ .env.{self.env_name} already exists")

            # Step 2: Deploy alfred-<env> service
            if not existing["alfred_service"]:
                try:
                    self.alfred_service_url = self.deploy_cloud_run_service(
                        self.source_alfred_service, self.alfred_service
                    )
                    if not self.alfred_service_url:
                        raise RuntimeError("Service deployed but URL not returned")
                except Exception as e:
                    raise RuntimeError(f"Failed to deploy alfred service: {str(e)}")
            else:
                print(f"✓ {self.alfred_service} already exists")
                # Get existing service URL
                try:
                    cmd = [
                        "gcloud",
                        "run",
                        "services",
                        "describe",
                        self.alfred_service,
                        "--platform=managed",
                        f"--region={self.region}",
                        f"--project={self.project_id}",
                        "--format=value(status.url)",
                    ]
                    result = self.run_command(cmd)
                    self.alfred_service_url = result.stdout.strip()

                    if not self.alfred_service_url:
                        raise ValueError("Service exists but URL is empty")

                    print(f"  URL: {self.alfred_service_url}")
                except Exception as e:
                    raise RuntimeError(
                        f"Failed to get URL for existing service {self.alfred_service}: {str(e)}"
                    )

            # Step 3: Deploy alfred-log-ingestion-<env> service
            if not existing["log_ingestion_service"]:
                try:
                    self.log_ingestion_service_url = self.deploy_cloud_run_service(
                        self.source_log_ingestion_service, self.log_ingestion_service
                    )
                    if not self.log_ingestion_service_url:
                        raise RuntimeError("Service deployed but URL not returned")
                except Exception as e:
                    raise RuntimeError(
                        f"Failed to deploy log ingestion service: {str(e)}"
                    )
            else:
                print(f"✓ {self.log_ingestion_service} already exists")

            # Step 4: Create dead letter topic first
            if not existing["dead_letter_topic"]:
                source_dead_letter_topic = (
                    f"skeps-logs-failed"
                    if not self.source_env or self.source_env.lower() == "prod"
                    else f"skeps-logs-failed-{self.source_env}"
                )
                if not self.create_pubsub_topic(
                    self.dead_letter_topic, source_dead_letter_topic
                ):
                    raise Exception("Failed to create dead letter topic")
            else:
                print(f"✓ Topic {self.dead_letter_topic} already exists")

            # Step 5: Create main topic
            if not existing["topic"]:
                if not self.create_pubsub_topic(
                    self.topic_name, self.source_topic_name
                ):
                    raise Exception("Failed to create main topic")
            else:
                print(f"✓ Topic {self.topic_name} already exists")

            # Step 6: Create Cloud Storage buckets
            if not existing["logs_bucket"]:
                if not self.create_cloud_storage_bucket(
                    self.logs_bucket, self.source_logs_bucket
                ):
                    raise Exception("Failed to create logs bucket")
            else:
                print(f"✓ Bucket {self.logs_bucket} already exists")

            if not existing["dead_letter_bucket"]:
                if not self.create_cloud_storage_bucket(
                    self.dead_letter_bucket,
                    self.source_dead_letter_bucket,
                    lifecycle_days=7,
                ):
                    raise Exception("Failed to create dead letter bucket")
            else:
                print(f"✓ Bucket {self.dead_letter_bucket} already exists")

            # Step 7: Create dead letter subscription
            if not existing["dead_letter_subscription"]:
                # Simple pull subscription for dead letter
                cmd = [
                    "gcloud",
                    "pubsub",
                    "subscriptions",
                    "create",
                    self.dead_letter_subscription,
                    f"--topic={self.dead_letter_topic}",
                    f"--project={self.project_id}",
                ]
                self.run_command(cmd)
                self.created_resources.append(
                    ("pubsub_subscription", self.dead_letter_subscription)
                )
                print(f"✅ Created dead letter subscription")
            else:
                print(f"✓ Subscription {self.dead_letter_subscription} already exists")

            # Step 8: Set up permissions before creating main subscription
            self.setup_pubsub_permissions()

            # Step 9: Create main subscription with dead letter
            if not existing["subscription"]:
                if not self.create_pubsub_subscription():
                    raise Exception("Failed to create subscription")
            else:
                print(f"✓ Subscription {self.subscription_name} already exists")

            # Step 10: Create Cloud Storage backup subscriptions
            if not self.create_pubsub_backup_subscriptions():
                print("⚠️  Warning: Failed to create backup subscriptions")

            # Step 11: Create storage notifications (optional)
            self.create_storage_notification()

            # Show summary
            self.show_summary()

        except KeyboardInterrupt:
            print("\n\n⚠️  Operation cancelled by user")
            if not self.dry_run:
                if self.ask_approval("Rollback created resources?"):
                    self.rollback()
            sys.exit(0)
        except Exception as e:
            print(f"\n❌ Error: {str(e)}")
            if not self.dry_run:
                if self.ask_approval("Rollback created resources?", critical=True):
                    self.rollback()
            sys.exit(1)


def main():
    parser = argparse.ArgumentParser(
        description="Set up complete Alfred environment infrastructure",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
This script sets up a complete Alfred environment including:
- Environment configuration file (.env.<env>)
- Alfred Cloud Run service (alfred-<env>)
- Log ingestion Cloud Run service (alfred-log-ingestion-<env>)
- Pub/Sub topic and subscription with dead letter support
- Cloud Storage buckets for logs and dead letter messages

Examples:
  # Create homepro environment by copying from production (default)
  python setup_complete_environment.py homepro

  # Create staging environment by copying from uat environment
  python setup_complete_environment.py staging --source-env=uat

  # Custom Pub/Sub names
  python setup_complete_environment.py staging \\
    --source-env=uat \\
    --topic-name=my-logs-staging \\
    --subscription-name=my-logs-staging-sub

  # Auto-approve mode (use with caution!)
  python setup_complete_environment.py prod --source-env=uat --yes

  # Dry run to see what would be created
  python setup_complete_environment.py test --source-env=uat --dry-run

Default naming conventions (based on existing patterns):
  Pub/Sub:
    - Topic: skeps-logs-<env>
    - Subscription: skeps-logs-<env>-sub
    - Dead letter topic: skeps-logs-failed-<env>
    - Dead letter subscription: skeps-logs-failed-<env>-sub-gcs

  Cloud Storage:
    - Logs bucket: skeps-logs-<env>
    - Dead letter bucket: skeps-logs-failed-<env>

Safety Features:
- Checks for existing resources before creating
- Asks for confirmation at each step
- Critical actions require explicit confirmation
- Rollback capability in case of failures
- Dry-run mode to preview changes
        """,
    )

    parser.add_argument(
        "env_name",
        help="Environment name for the new environment (e.g., homepro, staging, prod)",
    )
    parser.add_argument(
        "--source-env",
        help="Source environment to copy from (e.g., uat, homepro, prod). Use 'prod' or omit for production services (alfred, alfred-log-ingestion without suffix)",
    )
    parser.add_argument(
        "--topic-name", help="Pub/Sub topic name (default: skeps-logs-<env>)"
    )
    parser.add_argument(
        "--subscription-name",
        help="Pub/Sub subscription name (default: skeps-logs-<env>-sub)",
    )
    parser.add_argument(
        "--dead-letter-topic",
        help="Dead letter topic name (default: skeps-logs-failed-<env>)",
    )
    parser.add_argument(
        "--dead-letter-subscription",
        help="Dead letter subscription name (default: skeps-logs-failed-<env>-sub-gcs)",
    )
    parser.add_argument(
        "-y",
        "--yes",
        "--auto-approve",
        action="store_true",
        help="Auto-approve all non-critical steps",
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be created without making changes",
    )
    parser.add_argument(
        "--project-id",
        default="llogic-skai",
        help="GCP Project ID (default: llogic-skai)",
    )
    parser.add_argument(
        "--region", default="us-central1", help="GCP Region (default: us-central1)"
    )

    args = parser.parse_args()

    setup = ComprehensiveEnvironmentSetup(
        env_name=args.env_name,
        project_id=args.project_id,
        region=args.region,
        source_env=args.source_env,
        topic_name=args.topic_name,
        subscription_name=args.subscription_name,
        dead_letter_topic=args.dead_letter_topic,
        dead_letter_subscription=args.dead_letter_subscription,
        auto_approve=args.yes,
        dry_run=args.dry_run,
    )

    setup.run()


if __name__ == "__main__":
    main()
