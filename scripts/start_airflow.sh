#!/bin/bash

# Start Airflow Setup Script
set -e

echo "Starting Airflow setup..."

# Load environment based on ENV variable (default to test)
ENV=${ENV:-test}
echo "Using environment: $ENV"

# Load the appropriate environment file
if [ -f ".env.$ENV" ]; then
    set -a
    source .env.base
    source .env.$ENV
    set +a
    echo "Loaded configuration from .env.base and .env.$ENV"
else
    echo "Error: .env.$ENV file not found"
    exit 1
fi

# Export AIRFLOW_UID if not set
export AIRFLOW_UID=${AIRFLOW_UID:-$(id -u)}

# Start services with environment variables
echo "Starting Airflow services..."
export MONGODB_DB=${MONGODB_DB}
export GCP_PROJECT_ID=${GCP_PROJECT_ID}
export BIGQUERY_DATASET=${BIGQUERY_DATASET}
docker-compose -f docker-compose.airflow.yml up -d

# Wait for services to be healthy
echo "Waiting for services to be healthy..."
sleep 30

# Configure connections
echo "Configuring connections..."

# Add MongoDB connection using MONGODB_URI from environment
docker-compose -f docker-compose.airflow.yml exec -T airflow-webserver airflow connections add \
    'mongo_default' \
    --conn-type 'mongo' \
    --conn-uri "${MONGODB_URI}" || echo "MongoDB connection already exists"

# Add BigQuery connection (requires service account key)
if [ -f "airflow/gcp/service-account-key.json" ]; then
    docker-compose -f docker-compose.airflow.yml exec -T airflow-webserver airflow connections add \
        'bigquery_default' \
        --conn-type 'google_cloud_platform' \
        --conn-extra "{\"extra__google_cloud_platform__key_path\": \"/opt/airflow/gcp/service-account-key.json\", \"extra__google_cloud_platform__project\": \"${GCP_PROJECT_ID}\"}" || echo "BigQuery connection already exists"
else
    echo "Warning: No service account key found at airflow/gcp/service-account-key.json"
    echo "BigQuery connection not configured. Please add the service account key and run:"
    echo "  docker-compose -f docker-compose.airflow.yml exec airflow-webserver airflow connections add ..."
fi

echo ""
echo "Airflow setup complete!"
echo "Access Airflow UI at: http://localhost:8081"
echo "Username: admin"
echo "Password: admin"
echo ""
echo "API endpoints available at:"
echo "  POST   http://localhost:8081/api/v1/pipeline     - Create pipeline"
echo "  GET    http://localhost:8081/api/v1/pipelines    - List pipelines"
echo "  GET    http://localhost:8081/api/v1/pipeline/<name> - Get pipeline"
echo "  PUT    http://localhost:8081/api/v1/pipeline/<name> - Update pipeline"
echo "  DELETE http://localhost:8081/api/v1/pipeline/<name> - Delete pipeline"
echo ""
echo "Test pipeline specification created at: airflow/pipeline_specs/test_latest_decision_status.json"
