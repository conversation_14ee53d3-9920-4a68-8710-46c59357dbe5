# Build stage
FROM python:3.10.17-slim AS builder

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PIP_NO_CACHE_DIR=1

# Create and set working directory
WORKDIR /app

# Copy only requirements first
COPY requirements.txt .

# Create virtual environment and install dependencies
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"
RUN pip install --no-cache-dir -r requirements.txt

# Final stage
FROM python:3.10.17-slim

# Copy virtual environment from builder
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Set working directory
WORKDIR /app

# Copy only necessary files
COPY src/ src/
COPY app/ app/
COPY tests/ tests/
COPY run.py .
COPY start_production.sh .
COPY wsgi.py .
COPY .env* ./

# Make the production script executable
RUN chmod +x start_production.sh

# Set default environment
ENV ENV=local
ENV PORT=8080

# Expose port 8080
EXPOSE 8080

# Run the application - use Gunicorn for all environments except local
CMD ["sh", "-c", "if [ \"$ENV\" = \"local\" ]; then python run.py; else ./start_production.sh; fi"]
