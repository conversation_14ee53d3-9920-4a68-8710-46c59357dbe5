from .markdown_renderer import Mark<PERSON><PERSON><PERSON>er

# Create a singleton instance of the renderer
_renderer = MarkdownRenderer()


def render_markdown(text, include_css=False):
    """
    Render markdown text to HTML using our enhanced renderer.

    Args:
        text (str): The markdown text to render
        include_css (bool): Whether to include CSS in the output.
                          Set to False when rendering for web app where CSS is loaded separately.

    Returns:
        str: The rendered HTML
    """
    if not text:
        return ""

    # Replace escaped dollar signs for math rendering
    text = text.replace("\\$", "$")

    # Render markdown to HTML
    return _renderer.render(text, include_css=include_css)
