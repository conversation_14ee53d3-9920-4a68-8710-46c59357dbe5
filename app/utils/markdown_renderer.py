import markdown
from pygments.formatters import HtmlFormatter


class MarkdownRenderer:
    """A class to render markdown with beautiful styling."""

    def __init__(self):
        # Configure markdown extensions
        self.extensions = [
            "extra",  # Tables, fenced code, etc
            "codehilite",  # Syntax highlighting
            "toc",  # Table of contents
            "admonition",  # Callouts
            "meta",  # Metadata
            "attr_list",  # CSS classes
            "sane_lists",  # Better list handling
            "smarty",  # Smart typography
            "footnotes",  # Footnotes support
        ]

        # Configure extension settings
        self.extension_configs = {
            "codehilite": {
                "css_class": "highlight",
                "use_pygments": True,
                "noclasses": False,
                "linenums": True,
            },
            "toc": {"permalink": True, "toc_depth": 3, "title": "Table of Contents"},
        }

        # Initialize the markdown converter
        self.md = markdown.Markdown(
            extensions=self.extensions, extension_configs=self.extension_configs
        )

        # Get default CSS
        self.pygments_css = HtmlFormatter().get_style_defs(".highlight")
        self.markdown_css = self._get_default_css()

    def render(self, markdown_text, include_css=True):
        """Render markdown text to HTML with optional CSS inclusion."""
        # Convert markdown to HTML
        html_content = self.md.convert(markdown_text)

        if include_css:
            # Wrap the content in HTML with CSS
            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <style>
                    {self.markdown_css}
                    {self.pygments_css}
                </style>
            </head>
            <body class="markdown-body">
                {html_content}
            </body>
            </html>
            """
            return html

        return html_content

    def render_file(self, markdown_file, output_file=None, include_css=True):
        """Render a markdown file to HTML."""
        # Read markdown file
        with open(markdown_file, "r", encoding="utf-8") as f:
            markdown_text = f.read()

        # Render to HTML
        html = self.render(markdown_text, include_css)

        # If output file specified, write HTML
        if output_file:
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(html)
            return output_file

        return html

    def _get_default_css(self):
        """Get default GitHub-style markdown CSS."""
        return """
        .markdown-body {
            box-sizing: border-box;
            min-width: 200px;
            max-width: 980px;
            margin: 0 auto;
            padding: 45px;
            font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Helvetica,Arial,sans-serif;
            font-size: 16px;
            line-height: 1.5;
            word-wrap: break-word;
        }

        .markdown-body h1,
        .markdown-body h2,
        .markdown-body h3,
        .markdown-body h4,
        .markdown-body h5,
        .markdown-body h6 {
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: 600;
            line-height: 1.25;
        }

        .markdown-body h1 { font-size: 2em; border-bottom: 1px solid #eaecef; }
        .markdown-body h2 { font-size: 1.5em; border-bottom: 1px solid #eaecef; }

        .markdown-body code {
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            background-color: rgba(27,31,35,0.05);
            border-radius: 3px;
            font-family: SFMono-Regular,Consolas,Liberation Mono,Menlo,monospace;
        }

        .markdown-body pre {
            padding: 16px;
            overflow: auto;
            font-size: 85%;
            line-height: 1.45;
            background-color: #f6f8fa;
            border-radius: 3px;
        }

        .markdown-body pre code {
            display: inline;
            max-width: auto;
            padding: 0;
            margin: 0;
            overflow: visible;
            line-height: inherit;
            word-wrap: normal;
            background-color: transparent;
            border: 0;
        }

        .markdown-body blockquote {
            padding: 0 1em;
            color: #6a737d;
            border-left: 0.25em solid #dfe2e5;
            margin: 0;
        }

        .markdown-body table {
            border-spacing: 0;
            border-collapse: collapse;
            margin: 16px 0;
        }

        .markdown-body table th,
        .markdown-body table td {
            padding: 6px 13px;
            border: 1px solid #dfe2e5;
        }

        .markdown-body table tr:nth-child(2n) {
            background-color: #f6f8fa;
        }

        .markdown-body img {
            max-width: 100%;
            box-sizing: content-box;
        }

        .markdown-body .admonition {
            padding: 15px;
            margin-bottom: 16px;
            border-left: 4px solid #448aff;
            background-color: #e3f2fd;
            border-radius: 3px;
        }

        .markdown-body .admonition-title {
            font-weight: 600;
            margin: -15px -15px 15px;
            padding: 8px 15px;
            background-color: #448aff;
            color: white;
            border-radius: 3px 3px 0 0;
        }
        """
