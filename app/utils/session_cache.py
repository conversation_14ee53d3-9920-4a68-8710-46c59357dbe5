"""
Session Cache Utility
Provides in-memory caching with TTL for frequently accessed sessions.
MongoDB remains the single source of truth - this is purely for UI responsiveness.
"""

import logging
import threading
import time
from typing import Any, Callable, Dict, Optional


class SessionCache:
    """
    In-memory cache for session data with TTL (Time To Live).
    Designed to improve UI responsiveness by caching frequently accessed sessions.
    """

    def __init__(self, default_ttl: int = 300, max_size: int = 1000):
        """
        Initialize session cache.

        Args:
            default_ttl: Default time-to-live in seconds (5 minutes)
            max_size: Maximum number of sessions to cache
        """
        self.default_ttl = default_ttl
        self.max_size = max_size
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.stats = {"hits": 0, "misses": 0, "evictions": 0, "insertions": 0}
        self._lock = threading.RLock()

    def get(
        self,
        session_id: str,
        fetch_func: Callable[[str], Any],
        ttl: Optional[int] = None,
    ) -> Any:
        """
        Get session data from cache or fetch from source.

        Args:
            session_id: Session identifier
            fetch_func: Function to fetch data if not in cache
            ttl: Custom TTL for this entry

        Returns:
            Session data or None if not found
        """
        with self._lock:
            current_time = time.time()

            # Check if session is in cache and not expired
            if session_id in self.cache:
                cache_entry = self.cache[session_id]
                if cache_entry["expires_at"] > current_time:
                    # Cache hit
                    cache_entry["last_accessed"] = current_time
                    self.stats["hits"] += 1
                    return cache_entry["data"]
                else:
                    # Expired entry
                    del self.cache[session_id]

            # Cache miss - fetch from source
            self.stats["misses"] += 1

            try:
                data = fetch_func(session_id)
                if data is not None:
                    self.put(session_id, data, ttl)
                return data
            except Exception as e:
                logging.error(f"Error fetching session {session_id}: {e}")
                return None

    def put(self, session_id: str, data: Any, ttl: Optional[int] = None) -> None:
        """
        Store session data in cache.

        Args:
            session_id: Session identifier
            data: Session data to cache
            ttl: Custom TTL for this entry
        """
        with self._lock:
            current_time = time.time()
            effective_ttl = ttl or self.default_ttl

            # Check if we need to evict entries
            self._evict_if_needed()

            # Store the entry
            self.cache[session_id] = {
                "data": data,
                "created_at": current_time,
                "last_accessed": current_time,
                "expires_at": current_time + effective_ttl,
            }

            self.stats["insertions"] += 1

    def invalidate(self, session_id: str) -> bool:
        """
        Remove session from cache.

        Args:
            session_id: Session identifier

        Returns:
            True if session was in cache, False otherwise
        """
        with self._lock:
            if session_id in self.cache:
                del self.cache[session_id]
                return True
            return False

    def clear(self) -> int:
        """
        Clear all cached sessions.

        Returns:
            Number of sessions that were cached
        """
        with self._lock:
            count = len(self.cache)
            self.cache.clear()
            return count

    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._lock:
            current_time = time.time()

            # Count active (non-expired) entries
            active_entries = sum(
                1 for entry in self.cache.values() if entry["expires_at"] > current_time
            )

            total_requests = self.stats["hits"] + self.stats["misses"]
            hit_rate = (
                (self.stats["hits"] / total_requests * 100) if total_requests > 0 else 0
            )

            return {
                **self.stats,
                "total_entries": len(self.cache),
                "active_entries": active_entries,
                "expired_entries": len(self.cache) - active_entries,
                "hit_rate_percent": round(hit_rate, 2),
                "max_size": self.max_size,
                "default_ttl_seconds": self.default_ttl,
            }

    def _evict_if_needed(self) -> None:
        """Evict expired or oldest entries if cache is too large."""
        current_time = time.time()

        # First, remove expired entries
        expired_keys = [
            session_id
            for session_id, entry in self.cache.items()
            if entry["expires_at"] <= current_time
        ]

        for key in expired_keys:
            del self.cache[key]
            self.stats["evictions"] += 1

        # If still over capacity, remove oldest entries
        if len(self.cache) >= self.max_size:
            # Sort by last_accessed time (oldest first)
            sorted_entries = sorted(
                self.cache.items(), key=lambda x: x[1]["last_accessed"]
            )

            # Remove oldest entries until we're under capacity
            entries_to_remove = len(self.cache) - self.max_size + 1
            for session_id, _ in sorted_entries[:entries_to_remove]:
                del self.cache[session_id]
                self.stats["evictions"] += 1


# Global cache instance
_session_cache = SessionCache(
    default_ttl=300, max_size=1000
)  # 5 minutes, 1000 sessions


def cached_get_session(session_id: str, fetch_func: Callable[[str], Any]) -> Any:
    """
    Convenience function to get session data with caching.

    Args:
        session_id: Session identifier
        fetch_func: Function to fetch session data from MongoDB

    Returns:
        Session data or None if not found
    """
    return _session_cache.get(session_id, fetch_func)


def invalidate_session_cache(session_id: str) -> bool:
    """
    Convenience function to invalidate a specific session from cache.

    Args:
        session_id: Session identifier

    Returns:
        True if session was cached, False otherwise
    """
    return _session_cache.invalidate(session_id)


def get_cache_stats() -> Dict[str, Any]:
    """
    Convenience function to get cache statistics.

    Returns:
        Dictionary containing cache statistics
    """
    return _session_cache.get_stats()


def clear_session_cache() -> int:
    """
    Convenience function to clear all cached sessions.

    Returns:
        Number of sessions that were cached
    """
    return _session_cache.clear()
