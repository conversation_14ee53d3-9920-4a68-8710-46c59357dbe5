import atexit
import logging

from flask import Flask

from src.client_managers import cleanup_all_managers, get_mongo_manager
from src.database_manager import DatabaseManager
from src.log_agent import LogIntelligenceAgentLangGraph
from src.log_config import setup_logging

from .routes import init_routes


def create_app():
    """Create and configure the Flask application."""
    # Initialize logging for Flask app
    setup_logging()

    app = Flask(__name__)

    # Initialize client managers and application instances
    try:
        # Load config first for consistent configuration across all components
        from src.env_config import EnvConfig

        config = EnvConfig.load()

        # Initialize client managers with config (these will fail fast if credentials are missing)
        get_mongo_manager(config)

        # Create application instances that use the shared client managers
        db_manager = DatabaseManager(config)
        agent = LogIntelligenceAgentLangGraph(config)

        # Ensure MongoDB indexes are created
        db_manager.ensure_chat_indexes()

        # Store instances in app context so routes can access them
        app.db_manager = db_manager
        app.agent = agent

        logging.info(
            "Client managers and application instances initialized successfully"
        )

    except Exception as e:
        logging.error(
            f"Failed to initialize client managers and application instances: {e}"
        )
        logging.error("Application routes will return errors until this is resolved")
        # Set to None to make it clear they failed to initialize
        app.db_manager = None
        app.agent = None
        # Don't re-raise - let the app start but routes will fail gracefully

    # Initialize routes
    init_routes(app)

    # Register cleanup for application shutdown
    @app.teardown_appcontext
    def cleanup_on_teardown(exception):
        if exception:
            logging.error(f"Application context teardown due to exception: {exception}")

    # Register cleanup at exit
    atexit.register(cleanup_all_managers)

    return app
