# Environment files
.env

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
prompts/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
__pypackages__/

# Virtual Environment
venv/
env/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo
.ipynb_checkpoints
profile_default/
ipython_config.py

# Testing and Coverage
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Build and Documentation
target/
docs/_build/
instance/
.webassets-cache
.scrapy

# Logs, Databases and Data
*.log
*.sqlite
*.sqlite3
prompts/
log_data.pkl
log_index.faiss
*.csv
*.json
*.jsonl
processed_logs.jsonl
celerybeat-schedule
celerybeat.pid
pip-log.txt
pip-delete-this-directory.txt

# Translations
*.mo
*.pot

# Local Settings
local_settings.py
.python-version
poetry.lock

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
issues/
summarization_outputs/
CLAUDE.md
