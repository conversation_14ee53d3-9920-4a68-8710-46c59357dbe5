import os
import unittest
from datetime import datetime

import google.genai as genai
import requests
from google.cloud import secretmanager
from pymongo import MongoClient
from pymongo.server_api import ServerApi

from src.env_config import EnvConfig


class TestConnections(unittest.TestCase):
    def setUp(self):
        """Set up test environment"""
        # Load test environment configuration
        env = os.getenv("ENV", "test")
        env_file = f".env.{env}"
        if not os.path.exists(env_file):
            env_file = ".env"  # fallback to default .env
        self.config = EnvConfig.load(env_file)
        self.mongodb_client = self._get_mongodb_client()

    def tearDown(self):
        self.mongodb_client.close()

    def _get_mongodb_client(self):
        """Get MongoDB client"""
        secret_client = secretmanager.SecretManagerServiceClient()

        def access_secret(secret_id):
            name = (
                f"projects/{self.config.project_id}/secrets/{secret_id}/versions/latest"
            )
            response = secret_client.access_secret_version(request={"name": name})
            return response.payload.data.decode("UTF-8")

        mongodb_username = access_secret("MONGODB_USERNAME")
        mongodb_password = access_secret("MONGODB_PASSWORD")
        uri = self.config.mongodb_uri.format(
            mongodb_username=mongodb_username, mongodb_password=mongodb_password
        )
        return MongoClient(uri, server_api=ServerApi("1"))

    def test_mongodb_connection(self):
        """Test MongoDB connection (ping only)"""
        try:
            self.mongodb_client.admin.command("ping")
        except Exception as e:
            self.fail(f"MongoDB connection test failed: {str(e)}")

    def test_mongodb_write(self):
        """Test MongoDB write to collection"""
        try:
            db = self.mongodb_client.get_database(self.config.mongodb_db)
            collection_ref = db[self.config.mongodb_collection]
            result = collection_ref.insert_one(
                {
                    "dummy": True,
                    "timestamp": datetime.now(),
                }
            )
            self.assertIsNotNone(result.inserted_id)
        except Exception as e:
            self.fail(f"MongoDB write test failed: {str(e)}")

    def test_ping_google_dot_com(self):
        """Test pinging google.com"""
        try:
            response = requests.get("http://google.com")
            self.assertEqual(response.status_code, 200)
        except Exception as e:
            self.fail(f"Pinging google.com failed: {str(e)}")

    def test_google_genai_model_connection(self):
        """Test Google GenAI connection and model availability"""
        try:
            # Initialize Google GenAI client with Vertex AI
            client = genai.Client(
                vertexai=True,
                project=self.config.project_id,
                location=self.config.gcp_location,
            )

            # Try to initialize the model
            model = client.models.generate_content(
                model=self.config.model_name, contents="Hello, are you available?"
            )

            self.assertIsNotNone(model)
            self.assertTrue(hasattr(model, "text"))
            self.assertIsInstance(model.text, str)
        except Exception as e:
            self.fail(f"Google GenAI connection test failed: {str(e)}")


if __name__ == "__main__":
    unittest.main()
