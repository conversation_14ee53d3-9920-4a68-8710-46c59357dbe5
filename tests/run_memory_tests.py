"""
Test runner for memory system unit tests.
"""

import os
import sys
import unittest

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))

from tests.unit.test_embedding_service import TestEmbeddingResult, TestEmbeddingService
from tests.unit.test_memory_manager import TestMemoryManager

# Import test modules
from tests.unit.test_memory_models import (
    TestDeclarativeMemory,
    TestMemoryFactory,
    TestMemoryUnit,
    TestProceduralMemory,
    TestTranslationMemory,
)


def run_memory_tests():
    """Run all memory system tests."""

    # Create test suite
    test_suite = unittest.TestSuite()

    # Add memory model tests
    test_suite.addTest(unittest.makeSuite(TestMemoryUnit))
    test_suite.addTest(unittest.makeSuite(TestProceduralMemory))
    test_suite.addTest(unittest.makeSuite(TestDeclarativeMemory))
    test_suite.addTest(unittest.makeSuite(TestTranslationMemory))
    test_suite.addTest(unittest.makeSuite(TestMemoryFactory))

    # Add embedding service tests
    test_suite.addTest(unittest.makeSuite(TestEmbeddingService))
    test_suite.addTest(unittest.makeSuite(TestEmbeddingResult))

    # Add memory manager tests
    test_suite.addTest(unittest.makeSuite(TestMemoryManager))

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)

    # Return True if all tests passed
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_memory_tests()
    if not success:
        sys.exit(1)
    else:
        print("\nAll memory system tests passed!")
        sys.exit(0)
