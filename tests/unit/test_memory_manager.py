"""
Unit tests for memory manager.
"""

import unittest
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

from src.memory.memory_manager import MemoryManager
from src.memory.memory_models import (
    AgentGuidanceMemory,
    DomainWorkflowMemory,
    FieldKnowledgeMemory,
    MemoryUnit,
    TranslationMappingMemory,
)


class TestMemoryManager(unittest.TestCase):
    """Test MemoryManager class."""

    def setUp(self):
        """Set up test fixtures."""
        self.mock_config = Mock()

        # Mock database manager
        self.mock_db_manager = Mock()
        self.mock_database = MagicMock()
        self.mock_collection = Mock()
        self.mock_db_manager.database = self.mock_database
        self.mock_database.__getitem__.return_value = self.mock_collection

        # Mock embedding service
        self.mock_embedding_service = Mock()
        self.mock_embedding_result = Mock()
        self.mock_embedding_result.embedding = [0.1, 0.2, 0.3] * 256  # 768 dimensions
        self.mock_embedding_service.generate_embedding.return_value = (
            self.mock_embedding_result
        )

        with patch("src.memory.memory_manager.DatabaseManager") as mock_db_class, patch(
            "src.memory.memory_manager.EmbeddingService"
        ) as mock_embedding_class:

            mock_db_class.return_value = self.mock_db_manager
            mock_embedding_class.return_value = self.mock_embedding_service

            self.memory_manager = MemoryManager(self.mock_config)

    def test_memory_manager_initialization(self):
        """Test MemoryManager initialization."""
        self.assertEqual(self.memory_manager.config, self.mock_config)
        self.assertEqual(self.memory_manager.db_manager, self.mock_db_manager)
        self.assertEqual(
            self.memory_manager.embedding_service, self.mock_embedding_service
        )
        self.assertEqual(self.memory_manager.memory_collection, "memory")

    def test_create_memory_unit(self):
        """Test creating a memory unit."""
        memory = DomainWorkflowMemory(
            canonical_question="Test question",
            answer_plan={"collection": "test"},
            created_by="test_user",
            tags=["test"],
        )

        # Mock database insert
        self.mock_collection.insert_one.return_value = Mock(inserted_id="mock_id")

        result = self.memory_manager.create(memory)

        # Verify embedding was generated
        self.mock_embedding_service.generate_embedding.assert_called_once()

        # Verify database insert was called
        self.mock_collection.insert_one.assert_called_once()

        # Verify memory has embedding
        self.assertEqual(memory.embedding, self.mock_embedding_result.embedding)

        # Verify result is the MongoDB inserted_id
        self.assertEqual(result, "mock_id")

    def test_get_memory_unit(self):
        """Test retrieving a memory unit."""
        memory_id = "507f1f77bcf86cd799439011"  # Valid ObjectId format
        mock_memory_data = {
            "_id": memory_id,
            "type": "domain_workflow",
            "canonical_question": "Test question",
            "answer_plan": {"collection": "test"},
            "example_phrasings": [],
            "created_by": "test_user",
            "tags": ["test"],
        }

        # Mock database query
        self.mock_db_manager.query_mongodb.return_value = [mock_memory_data]

        result = self.memory_manager.get(memory_id)

        # Verify database query was called
        self.mock_db_manager.query_mongodb.assert_called_once()

        # Verify result is MemoryUnit
        self.assertIsInstance(result, MemoryUnit)
        # _id is filtered out during from_dict conversion

    def test_get_memory_unit_not_found(self):
        """Test retrieving non-existent memory unit."""
        memory_id = "nonexistent_id"

        # Mock database query returning no results
        self.mock_db_manager.query_mongodb.return_value = []

        result = self.memory_manager.get(memory_id)

        # Verify result is None
        self.assertIsNone(result)

    def test_search_memories(self):
        """Test searching memories."""
        query = "test query"

        # Mock database query returning memories
        mock_memories = [
            {
                "_id": "mem1",
                "embedding": [0.1, 0.2, 0.3] * 256,
                "type": "test",
                "canonical_question": "Test question 1",
                "title": "Test title 1",
            },
            {
                "_id": "mem2",
                "embedding": [0.4, 0.5, 0.6] * 256,
                "type": "test",
                "canonical_question": "Test question 2",
                "title": "Test title 2",
            },
        ]
        self.mock_db_manager.query_mongodb.return_value = mock_memories

        # Mock embedding service similarity search
        self.mock_embedding_service.find_similar_memories.return_value = [
            ("mem1", 0.9),
            ("mem2", 0.8),
        ]

        # Mock get method for retrieving full memory data
        mock_memory1 = Mock()
        mock_memory1.to_dict.return_value = {"type": "test"}
        mock_memory2 = Mock()
        mock_memory2.to_dict.return_value = {"type": "test"}

        with patch.object(
            self.memory_manager, "get", side_effect=[mock_memory1, mock_memory2]
        ):
            results = self.memory_manager.search(query)

        # Verify embedding was generated for query
        self.mock_embedding_service.generate_embedding.assert_called_with(query)

        # Verify database query was called
        self.mock_db_manager.query_mongodb.assert_called_once()

        # Verify similarity search was called
        self.mock_embedding_service.find_similar_memories.assert_called_once()

        # Verify results
        self.assertEqual(len(results), 2)
        self.assertEqual(results[0]["_id"], "mem1")
        self.assertEqual(results[0]["similarity_score"], 0.9)
        self.assertEqual(results[1]["_id"], "mem2")
        self.assertEqual(results[1]["similarity_score"], 0.8)

    def test_search_memories_with_type_filter(self):
        """Test searching memories with type filter."""
        query = "test query"
        type_filter = "domain_workflow"

        self.mock_db_manager.query_mongodb.return_value = []
        self.mock_embedding_service.find_similar_memories.return_value = []

        self.memory_manager.search(query, type_filter=type_filter)

        # Verify database query included type filter
        call_args = self.mock_db_manager.query_mongodb.call_args
        filter_arg = call_args[1]["filter"]
        self.assertEqual(filter_arg["type"], type_filter)
        self.assertTrue(filter_arg["is_latest_version"])

    def test_teach_domain_workflow(self):
        """Test teaching domain workflow memory."""
        question = "How to find errors?"
        logic = {"collection": "logs", "filter": {"level": "error"}}
        example_phrasings = ["Find errors", "Show errors"]
        created_by = "test_user"

        # Mock create method
        with patch.object(
            self.memory_manager, "create", return_value="test_id"
        ) as mock_create:
            result = self.memory_manager.teach_domain_workflow(
                question, logic, example_phrasings, created_by
            )

        # Verify create was called with DomainWorkflowMemory
        mock_create.assert_called_once()
        created_memory = mock_create.call_args[0][0]
        self.assertIsInstance(created_memory, DomainWorkflowMemory)
        self.assertEqual(created_memory.canonical_question, question)
        self.assertEqual(created_memory.answer_plan, logic)
        self.assertEqual(created_memory.example_phrasings, example_phrasings)
        self.assertEqual(created_memory.created_by, created_by)

        # Verify result
        self.assertEqual(result, "test_id")

    def test_teach_field_knowledge(self):
        """Test teaching field knowledge memory."""
        title = "Test Fact"
        body = "This is a test fact"
        field_type = "general"
        aliases = ["test", "fact"]
        created_by = "test_user"

        # Mock create method
        with patch.object(
            self.memory_manager, "create", return_value="test_id"
        ) as mock_create:
            result = self.memory_manager.teach_field_knowledge(
                title, body, field_type, aliases, created_by
            )

        # Verify create was called with FieldKnowledgeMemory
        mock_create.assert_called_once()
        created_memory = mock_create.call_args[0][0]
        self.assertIsInstance(created_memory, FieldKnowledgeMemory)
        self.assertEqual(created_memory.title, title)
        self.assertEqual(created_memory.body, body)
        self.assertEqual(created_memory.field_type, field_type)
        self.assertEqual(created_memory.aliases, aliases)
        self.assertEqual(created_memory.created_by, created_by)

        # Verify result
        self.assertEqual(result, "test_id")

    def test_teach_translation_mapping(self):
        """Test teaching translation mapping memory."""
        business_term = "revenue spike"
        technical_mapping = "daily_revenue > 10000"
        aliases = ["revenue jump", "sales spike"]
        created_by = "test_user"

        # Mock create method
        with patch.object(
            self.memory_manager, "create", return_value="test_id"
        ) as mock_create:
            result = self.memory_manager.teach_translation_mapping(
                business_term, technical_mapping, aliases, created_by
            )

        # Verify create was called with TranslationMappingMemory
        mock_create.assert_called_once()
        created_memory = mock_create.call_args[0][0]
        self.assertIsInstance(created_memory, TranslationMappingMemory)
        self.assertEqual(created_memory.created_by, created_by)

        # Verify result
        self.assertEqual(result, "test_id")

    def test_teach_agent_guidance(self):
        """Test teaching agent guidance memory."""
        title = "Query Strategy"
        body = "Use full logs for targeted queries"
        created_by = "test_user"

        # Mock create method
        with patch.object(
            self.memory_manager, "create", return_value="test_id"
        ) as mock_create:
            result = self.memory_manager.teach_agent_guidance(title, body, created_by)

        # Verify create was called with AgentGuidanceMemory
        mock_create.assert_called_once()
        created_memory = mock_create.call_args[0][0]
        self.assertIsInstance(created_memory, AgentGuidanceMemory)
        self.assertEqual(created_memory.title, title)
        self.assertEqual(created_memory.body, body)
        self.assertEqual(created_memory.created_by, created_by)

        # Verify result
        self.assertEqual(result, "test_id")

    def test_add_feedback(self):
        """Test adding feedback to memory."""
        memory_id = "test_memory_id"
        user_id = "test_user"
        rating = "thumbs_up"
        notes = "Great response!"

        # Mock existing memory
        mock_memory = Mock(spec=DomainWorkflowMemory)
        mock_memory.add_feedback = Mock()
        mock_memory.update_feedback_counts = Mock()
        mock_memory.confidence_score = 0.6
        mock_memory.calculate_acceptance_ratio.return_value = 0.8
        mock_memory.feedback = []

        # Mock update method
        with patch.object(
            self.memory_manager, "get", return_value=mock_memory
        ) as mock_get, patch.object(
            self.memory_manager, "update", return_value=True
        ) as mock_update:

            result = self.memory_manager.add_feedback(memory_id, user_id, rating, notes)

        # Verify memory was retrieved
        mock_get.assert_called_once_with(memory_id)

        # Verify feedback was added
        mock_memory.add_feedback.assert_called_once_with(user_id, rating, notes)

        # Verify feedback counts were updated (for domain workflow memory)
        mock_memory.update_feedback_counts.assert_called_once_with(rating)

        # Verify memory was updated
        mock_update.assert_called_once()

        # Verify result
        self.assertTrue(result)

    def test_add_feedback_memory_not_found(self):
        """Test adding feedback to non-existent memory."""
        memory_id = "nonexistent_id"

        # Mock get method returning None
        with patch.object(self.memory_manager, "get", return_value=None):
            result = self.memory_manager.add_feedback(memory_id, "user", "thumbs_up")

        # Verify result is False
        self.assertFalse(result)

    def test_get_memories_by_type(self):
        """Test getting memories by type."""
        memory_type = "domain_workflow"

        # Mock database query
        mock_memories = [
            {"_id": "mem1", "type": "domain_workflow"},
            {"_id": "mem2", "type": "domain_workflow"},
        ]
        self.mock_db_manager.query_mongodb.return_value = mock_memories

        results = self.memory_manager.get_memories_by_type(memory_type)

        # Verify database query was called with correct filter
        call_args = self.mock_db_manager.query_mongodb.call_args
        filter_arg = call_args[1]["filter"]
        self.assertEqual(filter_arg["type"], memory_type)
        self.assertTrue(filter_arg["is_latest_version"])

        # Verify results
        self.assertEqual(len(results), 2)

    def test_get_memory_stats(self):
        """Test getting memory statistics."""
        # Mock database aggregation results
        mock_type_counts = [
            {"_id": "domain_workflow", "count": 10},
            {"_id": "field_knowledge", "count": 5},
        ]
        mock_avg_confidence = [{"_id": None, "avg_confidence": 0.75}]

        self.mock_collection.aggregate.side_effect = [
            mock_type_counts,
            mock_avg_confidence,
        ]
        self.mock_collection.count_documents.return_value = 15

        # Mock embedding service stats
        self.mock_embedding_service.get_embedding_stats.return_value = {"model": "test"}

        stats = self.memory_manager.get_memory_stats()

        # Verify results
        self.assertEqual(stats["total_memories"], 15)
        self.assertEqual(stats["by_type"]["domain_workflow"], 10)
        self.assertEqual(stats["by_type"]["field_knowledge"], 5)
        self.assertEqual(stats["average_confidence"], 0.75)
        self.assertEqual(stats["embedding_stats"]["model"], "test")

    def test_extract_text_for_embedding(self):
        """Test extracting text for embedding."""
        # Test domain workflow memory
        domain_workflow_memory = DomainWorkflowMemory(
            canonical_question="Test question"
        )
        text = self.memory_manager._extract_text_for_embedding(domain_workflow_memory)
        self.assertEqual(text, "Test question")

        # Test field knowledge memory
        field_knowledge_memory = FieldKnowledgeMemory(
            title="Test title", body="Test body"
        )
        text = self.memory_manager._extract_text_for_embedding(field_knowledge_memory)
        self.assertEqual(text, "Test title Test body")

        # Test translation mapping memory
        translation_mapping_memory = TranslationMappingMemory(
            title="Test title", body="Test body"
        )
        text = self.memory_manager._extract_text_for_embedding(
            translation_mapping_memory
        )
        self.assertEqual(text, "Test title Test body")

        # Test agent guidance memory
        agent_guidance_memory = AgentGuidanceMemory(
            title="Test title", body="Test body"
        )
        text = self.memory_manager._extract_text_for_embedding(agent_guidance_memory)
        self.assertEqual(text, "Test title Test body")

        # Test base memory (should raise exception)
        base_memory = MemoryUnit(type="base", created_by="test_user")
        with self.assertRaises(ValueError):
            self.memory_manager._extract_text_for_embedding(base_memory)

    def test_calculate_confidence_score(self):
        """Test calculating confidence score."""
        memory = Mock()
        memory.confidence_score = 0.6
        memory.calculate_acceptance_ratio.return_value = 0.8

        # Test thumbs up
        new_score = self.memory_manager._calculate_confidence_score(memory, "thumbs_up")
        self.assertEqual(new_score, min(1.0, 0.6 + 0.05))

        # Test thumbs down
        new_score = self.memory_manager._calculate_confidence_score(
            memory, "thumbs_down"
        )
        self.assertEqual(new_score, max(0.0, 0.6 - 0.1))

        # Test wrong answer
        new_score = self.memory_manager._calculate_confidence_score(
            memory, "wrong_answer"
        )
        self.assertEqual(new_score, max(0.0, 0.6 - 0.2))

        # Test other rating (uses acceptance ratio)
        new_score = self.memory_manager._calculate_confidence_score(memory, "other")
        expected = (0.6 * 0.7) + (0.8 * 0.3)
        self.assertEqual(new_score, expected)


if __name__ == "__main__":
    unittest.main()
