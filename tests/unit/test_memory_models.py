"""
Unit tests for memory models.
"""

import unittest
from datetime import datetime

from src.memory.memory_models import (
    AgentGuidanceMemory,
    DomainWorkflowMemory,
    FieldKnowledgeMemory,
    MemoryUnit,
    TranslationMappingMemory,
    create_memory_from_dict,
)


class TestMemoryUnit(unittest.TestCase):
    """Test base MemoryUnit class."""

    def setUp(self):
        """Set up test fixtures."""
        self.memory_unit = MemoryUnit(
            type="test", created_by="test_user", tags=["test", "unit_test"]
        )

    def test_memory_unit_initialization(self):
        """Test MemoryUnit initialization."""
        self.assertEqual(self.memory_unit.type, "test")
        self.assertEqual(self.memory_unit.created_by, "test_user")
        self.assertEqual(self.memory_unit.tags, ["test", "unit_test"])
        self.assertEqual(self.memory_unit.version, 1)
        self.assertTrue(self.memory_unit.is_latest_version)
        self.assertEqual(self.memory_unit.confidence_score, 0.6)
        # memory_id was removed, using MongoDB _id instead

    def test_to_dict_conversion(self):
        """Test converting MemoryUnit to dictionary."""
        memory_dict = self.memory_unit.to_dict()

        self.assertEqual(memory_dict["type"], "test")
        self.assertEqual(memory_dict["created_by"], "test_user")
        self.assertEqual(memory_dict["tags"], ["test", "unit_test"])
        self.assertEqual(memory_dict["version"], 1)
        self.assertTrue(memory_dict["is_latest_version"])
        self.assertIn("created_at", memory_dict)
        self.assertIn("updated_at", memory_dict)

    def test_from_dict_conversion(self):
        """Test creating MemoryUnit from dictionary."""
        memory_dict = {
            "_id": "test_id",
            "type": "test",
            "version": 1,
            "created_by": "test_user",
            "tags": ["test"],
            "created_at": "2023-01-01T00:00:00",
            "updated_at": "2023-01-01T00:00:00",
            "history": [],
            "feedback": [],
        }

        memory = MemoryUnit.from_dict(memory_dict)

        # _id is filtered out during from_dict conversion
        self.assertEqual(memory.type, "test")
        self.assertEqual(memory.created_by, "test_user")
        self.assertEqual(memory.tags, ["test"])
        self.assertIsInstance(memory.created_at, datetime)
        self.assertIsInstance(memory.updated_at, datetime)

    def test_create_new_version(self):
        """Test creating a new version of memory."""
        new_version = self.memory_unit.create_new_version("test_updater", "parent_id")

        self.assertEqual(new_version.version, 2)
        self.assertEqual(new_version.parent_id, "parent_id")
        self.assertTrue(new_version.is_latest_version)
        self.assertEqual(new_version.confidence_score, 0.6)
        self.assertEqual(len(new_version.history), 1)

    def test_add_feedback(self):
        """Test adding feedback to memory."""
        self.memory_unit.add_feedback("user1", "thumbs_up", "Great response!")

        self.assertEqual(len(self.memory_unit.feedback), 1)
        feedback = self.memory_unit.feedback[0]
        self.assertEqual(feedback.user_id, "user1")
        self.assertEqual(feedback.rating, "thumbs_up")
        self.assertEqual(feedback.notes, "Great response!")

    def test_calculate_acceptance_ratio(self):
        """Test calculating acceptance ratio."""
        # No feedback
        self.assertEqual(self.memory_unit.calculate_acceptance_ratio(), 0.5)

        # Add mixed feedback
        self.memory_unit.add_feedback("user1", "thumbs_up")
        self.memory_unit.add_feedback("user2", "thumbs_down")
        self.memory_unit.add_feedback("user3", "thumbs_up")

        # 2 thumbs up, 1 thumbs down = 2/3 = 0.67
        self.assertAlmostEqual(
            self.memory_unit.calculate_acceptance_ratio(), 0.67, places=2
        )


class TestDomainWorkflowMemory(unittest.TestCase):
    """Test DomainWorkflowMemory class."""

    def setUp(self):
        """Set up test fixtures."""
        self.domain_workflow_memory = DomainWorkflowMemory(
            canonical_question="What is the error rate?",
            answer_plan={"collection": "logs", "filter": {"level": "error"}},
            example_phrasings=["How many errors?", "Error count?"],
            created_by="test_user",
        )

    def test_domain_workflow_memory_initialization(self):
        """Test DomainWorkflowMemory initialization."""
        self.assertEqual(self.domain_workflow_memory.type, "domain_workflow")
        self.assertEqual(
            self.domain_workflow_memory.canonical_question, "What is the error rate?"
        )
        self.assertEqual(self.domain_workflow_memory.usage_count, 0)
        self.assertEqual(self.domain_workflow_memory.acceptance_count, 0)
        self.assertEqual(self.domain_workflow_memory.rejection_count, 0)

    def test_increment_usage(self):
        """Test incrementing usage count."""
        original_count = self.domain_workflow_memory.usage_count
        self.domain_workflow_memory.increment_usage()
        self.assertEqual(self.domain_workflow_memory.usage_count, original_count + 1)

    def test_add_example_phrasing(self):
        """Test adding example phrasing."""
        new_phrasing = "Show me errors"
        self.domain_workflow_memory.add_example_phrasing(new_phrasing)

        self.assertIn(new_phrasing, self.domain_workflow_memory.example_phrasings)

        # Should not add duplicate
        original_length = len(self.domain_workflow_memory.example_phrasings)
        self.domain_workflow_memory.add_example_phrasing(new_phrasing)
        self.assertEqual(
            len(self.domain_workflow_memory.example_phrasings), original_length
        )

    def test_update_feedback_counts(self):
        """Test updating feedback counts."""
        self.domain_workflow_memory.update_feedback_counts("thumbs_up")
        self.assertEqual(self.domain_workflow_memory.acceptance_count, 1)

        self.domain_workflow_memory.update_feedback_counts("thumbs_down")
        self.assertEqual(self.domain_workflow_memory.rejection_count, 1)

        self.domain_workflow_memory.update_feedback_counts("wrong_answer")
        self.assertEqual(self.domain_workflow_memory.rejection_count, 2)


class TestFieldKnowledgeMemory(unittest.TestCase):
    """Test FieldKnowledgeMemory class."""

    def setUp(self):
        """Set up test fixtures."""
        self.field_knowledge_memory = FieldKnowledgeMemory(
            title="client_ip field",
            body="The client_ip field contains the user's IP address",
            field_type="log_schema",
            aliases=["ip", "user_ip"],
            created_by="test_user",
        )

    def test_field_knowledge_memory_initialization(self):
        """Test FieldKnowledgeMemory initialization."""
        self.assertEqual(self.field_knowledge_memory.type, "field_knowledge")
        self.assertEqual(self.field_knowledge_memory.title, "client_ip field")
        self.assertEqual(self.field_knowledge_memory.field_type, "log_schema")
        self.assertEqual(self.field_knowledge_memory.aliases, ["ip", "user_ip"])

    def test_add_alias(self):
        """Test adding alias."""
        new_alias = "client_address"
        self.field_knowledge_memory.add_alias(new_alias)

        self.assertIn(new_alias, self.field_knowledge_memory.aliases)

        # Should not add duplicate
        original_length = len(self.field_knowledge_memory.aliases)
        self.field_knowledge_memory.add_alias(new_alias)
        self.assertEqual(len(self.field_knowledge_memory.aliases), original_length)

    def test_update_content(self):
        """Test updating content."""
        new_title = "Updated title"
        new_body = "Updated body"

        self.field_knowledge_memory.update_content(title=new_title, body=new_body)

        self.assertEqual(self.field_knowledge_memory.title, new_title)
        self.assertEqual(self.field_knowledge_memory.body, new_body)


class TestTranslationMappingMemory(unittest.TestCase):
    """Test TranslationMappingMemory class."""

    def setUp(self):
        """Set up test fixtures."""
        self.translation_mapping_memory = TranslationMappingMemory(
            created_by="test_user"
        )

    def test_translation_mapping_memory_initialization(self):
        """Test TranslationMappingMemory initialization."""
        self.assertEqual(self.translation_mapping_memory.type, "translation_mapping")

    def test_set_mapping(self):
        """Test setting business term mapping."""
        business_term = "revenue spike"
        technical_mapping = "daily_revenue > 10000"
        aliases = ["revenue jump", "sales spike"]

        self.translation_mapping_memory.set_mapping(
            business_term, technical_mapping, aliases
        )

        self.assertEqual(
            self.translation_mapping_memory.title, f"Business term: {business_term}"
        )
        self.assertEqual(self.translation_mapping_memory.body, technical_mapping)
        self.assertEqual(self.translation_mapping_memory.aliases, aliases)


class TestAgentGuidanceMemory(unittest.TestCase):
    """Test AgentGuidanceMemory class."""

    def setUp(self):
        """Set up test fixtures."""
        self.agent_guidance_memory = AgentGuidanceMemory(
            title="Query optimization thresholds",
            body="Use full logs for <10 results, summary+key fields for 10-50 results",
            created_by="test_user",
        )

    def test_agent_guidance_memory_initialization(self):
        """Test AgentGuidanceMemory initialization."""
        self.assertEqual(self.agent_guidance_memory.type, "agent_guidance")
        self.assertEqual(
            self.agent_guidance_memory.title, "Query optimization thresholds"
        )
        self.assertEqual(
            self.agent_guidance_memory.body,
            "Use full logs for <10 results, summary+key fields for 10-50 results",
        )

    def test_update_content(self):
        """Test updating content."""
        new_title = "Updated guidance"
        new_body = "Updated guidance content"

        self.agent_guidance_memory.update_content(title=new_title, body=new_body)

        self.assertEqual(self.agent_guidance_memory.title, new_title)
        self.assertEqual(self.agent_guidance_memory.body, new_body)


class TestMemoryFactory(unittest.TestCase):
    """Test memory factory function."""

    def test_create_domain_workflow_memory(self):
        """Test creating domain workflow memory from dict."""
        data = {
            "type": "domain_workflow",
            "canonical_question": "Test question",
            "answer_plan": {},
            "example_phrasings": [],
            "created_by": "test_user",
        }

        memory = create_memory_from_dict(data)
        self.assertIsInstance(memory, DomainWorkflowMemory)
        self.assertEqual(memory.type, "domain_workflow")

    def test_create_field_knowledge_memory(self):
        """Test creating field knowledge memory from dict."""
        data = {
            "type": "field_knowledge",
            "title": "Test title",
            "body": "Test body",
            "field_type": "general",
            "created_by": "test_user",
        }

        memory = create_memory_from_dict(data)
        self.assertIsInstance(memory, FieldKnowledgeMemory)
        self.assertEqual(memory.type, "field_knowledge")

    def test_create_translation_mapping_memory(self):
        """Test creating translation mapping memory from dict."""
        data = {
            "type": "translation_mapping",
            "title": "Test translation",
            "body": "Test mapping",
            "created_by": "test_user",
        }

        memory = create_memory_from_dict(data)
        self.assertIsInstance(memory, TranslationMappingMemory)
        self.assertEqual(memory.type, "translation_mapping")

    def test_create_agent_guidance_memory(self):
        """Test creating agent guidance memory from dict."""
        data = {
            "type": "agent_guidance",
            "title": "Test guidance",
            "body": "Test guidance content",
            "created_by": "test_user",
        }

        memory = create_memory_from_dict(data)
        self.assertIsInstance(memory, AgentGuidanceMemory)
        self.assertEqual(memory.type, "agent_guidance")

    def test_create_base_memory(self):
        """Test creating base memory from dict."""
        data = {"type": "base", "created_by": "test_user"}

        memory = create_memory_from_dict(data)
        self.assertIsInstance(memory, MemoryUnit)
        self.assertEqual(memory.type, "base")

    def test_create_unknown_memory_type(self):
        """Test creating unknown memory type falls back to base."""
        data = {"type": "unknown", "created_by": "test_user"}

        memory = create_memory_from_dict(data)
        self.assertIsInstance(memory, MemoryUnit)
        self.assertEqual(memory.type, "unknown")


if __name__ == "__main__":
    unittest.main()
