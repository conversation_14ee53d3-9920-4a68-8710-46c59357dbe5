"""
Unit tests for embedding service.
"""

import unittest
from unittest.mock import Mock, patch

from src.memory.embedding_service import Embedding<PERSON><PERSON><PERSON>, EmbeddingService


class TestEmbeddingService(unittest.TestCase):
    """Test EmbeddingService class."""

    def setUp(self):
        """Set up test fixtures."""
        self.mock_config = Mock()
        self.mock_config.embedding_model = "text-embedding-004"
        self.mock_config.embedding_dimension = 768

        with patch("src.memory.embedding_service.get_genai_manager"):
            self.embedding_service = EmbeddingService(self.mock_config)

    def test_initialization(self):
        """Test EmbeddingService initialization."""
        self.assertEqual(self.embedding_service.model_name, "text-embedding-004")
        self.assertEqual(self.embedding_service.embedding_dimension, 768)
        self.assertEqual(self.embedding_service.similarity_threshold, 0.7)

    @patch("src.memory.embedding_service.get_genai_manager")
    @patch("src.memory.embedding_service.call_genai_with_retry")
    def test_generate_embedding_success(
        self, mock_call_genai_with_retry, mock_get_genai_manager
    ):
        """Test successful embedding generation."""
        # Mock the response
        mock_response = Mock()
        mock_response.embedding.values = [0.1, 0.2, 0.3] * 256  # 768 dimensions
        mock_call_genai_with_retry.return_value = mock_response

        mock_manager = Mock()
        mock_get_genai_manager.return_value = mock_manager

        # Test embedding generation
        result = self.embedding_service.generate_embedding("test text")

        self.assertIsInstance(result, EmbeddingResult)
        self.assertEqual(result.text, "test text")
        self.assertEqual(result.model, "text-embedding-004")
        self.assertEqual(len(result.embedding), 768)
        self.assertGreater(result.generation_time_ms, 0)

    def test_generate_embedding_empty_text(self):
        """Test embedding generation with empty text."""
        with self.assertRaises(ValueError):
            self.embedding_service.generate_embedding("")

        with self.assertRaises(ValueError):
            self.embedding_service.generate_embedding("   ")

    def test_calculate_cosine_similarity(self):
        """Test cosine similarity calculation."""
        # Test identical vectors
        vec1 = [1, 0, 0]
        vec2 = [1, 0, 0]
        similarity = self.embedding_service.calculate_cosine_similarity(vec1, vec2)
        self.assertAlmostEqual(similarity, 1.0, places=5)

        # Test orthogonal vectors
        vec1 = [1, 0, 0]
        vec2 = [0, 1, 0]
        similarity = self.embedding_service.calculate_cosine_similarity(vec1, vec2)
        self.assertAlmostEqual(similarity, 0.0, places=5)

        # Test opposite vectors
        vec1 = [1, 0, 0]
        vec2 = [-1, 0, 0]
        similarity = self.embedding_service.calculate_cosine_similarity(vec1, vec2)
        self.assertAlmostEqual(similarity, -1.0, places=5)

    def test_calculate_cosine_similarity_dimension_mismatch(self):
        """Test cosine similarity with mismatched dimensions."""
        vec1 = [1, 0, 0]
        vec2 = [1, 0]

        with self.assertRaises(ValueError):
            self.embedding_service.calculate_cosine_similarity(vec1, vec2)

    def test_calculate_cosine_similarity_zero_vectors(self):
        """Test cosine similarity with zero vectors."""
        vec1 = [0, 0, 0]
        vec2 = [1, 0, 0]
        similarity = self.embedding_service.calculate_cosine_similarity(vec1, vec2)
        self.assertEqual(similarity, 0.0)

    def test_find_similar_memories(self):
        """Test finding similar memories."""
        query_embedding = [1, 0, 0]
        memory_embeddings = [
            ("mem1", [1, 0, 0]),  # similarity = 1.0
            ("mem2", [0, 1, 0]),  # similarity = 0.0
            ("mem3", [0.8, 0.6, 0]),  # similarity = 0.8
            ("mem4", [-1, 0, 0]),  # similarity = -1.0
        ]

        similar_memories = self.embedding_service.find_similar_memories(
            query_embedding, memory_embeddings, threshold=0.5
        )

        # Should return memories with similarity >= 0.5, sorted by similarity
        self.assertEqual(len(similar_memories), 2)
        self.assertEqual(similar_memories[0][0], "mem1")  # highest similarity
        self.assertEqual(similar_memories[1][0], "mem3")  # second highest
        self.assertAlmostEqual(similar_memories[0][1], 1.0, places=5)
        self.assertAlmostEqual(similar_memories[1][1], 0.8, places=5)

    def test_find_similar_memories_default_threshold(self):
        """Test finding similar memories with default threshold."""
        query_embedding = [1, 0, 0]
        memory_embeddings = [
            ("mem1", [1, 0, 0]),  # similarity = 1.0
            ("mem2", [0.3, 0.9, 0]),  # similarity < 0.7
        ]

        similar_memories = self.embedding_service.find_similar_memories(
            query_embedding, memory_embeddings
        )

        # Should only return memories with similarity >= 0.7 (default threshold)
        self.assertEqual(len(similar_memories), 1)
        self.assertEqual(similar_memories[0][0], "mem1")

    @patch("src.memory.embedding_service.get_genai_manager")
    @patch("src.memory.embedding_service.call_genai_with_retry")
    def test_batch_generate_embeddings(
        self, mock_call_genai_with_retry, mock_get_genai_manager
    ):
        """Test batch embedding generation."""
        # Mock the response
        mock_response = Mock()
        mock_response.embedding.values = [0.1, 0.2, 0.3] * 256  # 768 dimensions
        mock_call_genai_with_retry.return_value = mock_response

        mock_manager = Mock()
        mock_get_genai_manager.return_value = mock_manager

        texts = ["text1", "text2", "text3"]
        results = self.embedding_service.batch_generate_embeddings(texts)

        self.assertEqual(len(results), 3)
        for i, result in enumerate(results):
            self.assertIsInstance(result, EmbeddingResult)
            self.assertEqual(result.text, texts[i])
            self.assertEqual(result.model, "text-embedding-004")

    def test_update_similarity_threshold(self):
        """Test updating similarity threshold."""
        new_threshold = 0.8
        self.embedding_service.update_similarity_threshold(new_threshold)
        self.assertEqual(self.embedding_service.similarity_threshold, new_threshold)

    def test_update_similarity_threshold_invalid(self):
        """Test updating similarity threshold with invalid values."""
        with self.assertRaises(ValueError):
            self.embedding_service.update_similarity_threshold(-0.1)

        with self.assertRaises(ValueError):
            self.embedding_service.update_similarity_threshold(1.1)

    def test_get_embedding_stats(self):
        """Test getting embedding statistics."""
        stats = self.embedding_service.get_embedding_stats()

        self.assertIn("model_name", stats)
        self.assertIn("embedding_dimension", stats)
        self.assertIn("similarity_threshold", stats)
        self.assertIn("client_available", stats)
        self.assertEqual(stats["model_name"], "text-embedding-004")
        self.assertEqual(stats["embedding_dimension"], 768)
        self.assertEqual(stats["similarity_threshold"], 0.7)

    def test_validate_embedding_valid(self):
        """Test validating valid embedding."""
        valid_embedding = [0.1, 0.2, 0.3] * 256  # 768 dimensions
        self.assertTrue(self.embedding_service.validate_embedding(valid_embedding))

    def test_validate_embedding_invalid_type(self):
        """Test validating embedding with invalid type."""
        self.assertFalse(self.embedding_service.validate_embedding("not a list"))
        self.assertFalse(self.embedding_service.validate_embedding(None))

    def test_validate_embedding_invalid_dimension(self):
        """Test validating embedding with invalid dimension."""
        invalid_embedding = [0.1, 0.2, 0.3]  # Wrong dimension
        self.assertFalse(self.embedding_service.validate_embedding(invalid_embedding))

    def test_validate_embedding_invalid_values(self):
        """Test validating embedding with invalid values."""
        # Test non-numeric values
        invalid_embedding = ["a", "b", "c"] * 256
        self.assertFalse(self.embedding_service.validate_embedding(invalid_embedding))

        # Test NaN values
        invalid_embedding = [float("nan")] * 768
        self.assertFalse(self.embedding_service.validate_embedding(invalid_embedding))

        # Test infinity values
        invalid_embedding = [float("inf")] * 768
        self.assertFalse(self.embedding_service.validate_embedding(invalid_embedding))


class TestEmbeddingResult(unittest.TestCase):
    """Test EmbeddingResult dataclass."""

    def test_embedding_result_creation(self):
        """Test EmbeddingResult creation."""
        embedding = [0.1, 0.2, 0.3]
        result = EmbeddingResult(
            embedding=embedding,
            text="test text",
            model="text-embedding-004",
            generation_time_ms=100.5,
        )

        self.assertEqual(result.embedding, embedding)
        self.assertEqual(result.text, "test text")
        self.assertEqual(result.model, "text-embedding-004")
        self.assertEqual(result.generation_time_ms, 100.5)
        self.assertIsNone(result.token_count)

    def test_embedding_result_with_token_count(self):
        """Test EmbeddingResult with token count."""
        result = EmbeddingResult(
            embedding=[0.1, 0.2, 0.3],
            text="test text",
            model="text-embedding-004",
            generation_time_ms=100.5,
            token_count=5,
        )

        self.assertEqual(result.token_count, 5)


if __name__ == "__main__":
    unittest.main()
