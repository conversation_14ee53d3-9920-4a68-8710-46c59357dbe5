#!/usr/bin/env python3
"""
Test all three log ingestion endpoints for immediate response and correctness.
"""
import base64
import datetime
import json
import time
import uuid

import requests

BASE_URL = "http://localhost:8080"
ENDPOINTS = {
    "api": f"{BASE_URL}/api/log",
    "prod": f"{BASE_URL}/api/log/prod",
    "pubsub": f"{BASE_URL}/pubsub/api/log",
}


def create_pubsub_payload(message_id, data):
    encoded_data = base64.b64encode(json.dumps(data).encode()).decode()
    return {"message": {"messageId": message_id, "data": encoded_data}}


def test_api_log():
    print("\n🧪 Testing /api/log endpoint...")
    payload = {
        "logs": [
            {
                "timestamp": datetime.datetime.now().isoformat(),
                "level": "INFO",
                "message": "Test API log message",
                "source": "test-script",
                "dummy": True,
                "uuid": str(uuid.uuid4()),
            }
        ]
    }
    print(f"Payload: {payload}")
    start = time.time()
    resp = requests.post(ENDPOINTS["api"], json=payload, timeout=10)
    elapsed = time.time() - start
    print(f"Status: {resp.status_code}, Time: {elapsed:.2f}s, Body: {resp.json()}")
    assert resp.status_code == 200
    assert resp.json().get("summarization_status") == "background_processing"
    print("✅ /api/log passed.")


def test_api_log_prod():
    print("\n🧪 Testing /api/log/prod endpoint...")
    payload = {
        "logs": [
            {
                "timestamp": datetime.datetime.now().isoformat(),
                "level": "INFO",
                "message": "Test PROD log message",
                "source": "test-script",
                "dummy": True,
                "uuid": str(uuid.uuid4()),
            }
        ]
    }
    print(f"Payload: {payload}")
    start = time.time()
    resp = requests.post(ENDPOINTS["prod"], json=payload, timeout=10)
    elapsed = time.time() - start
    print(f"Status: {resp.status_code}, Time: {elapsed:.2f}s, Body: {resp.json()}")
    assert resp.status_code == 200
    assert resp.json().get("summarization_status") == "background_processing"
    print("✅ /api/log/prod passed.")


def test_pubsub_log():
    print("\n🧪 Testing /pubsub/api/log endpoint...")
    message_id = f"test-pubsub-{str(uuid.uuid4())}"
    payload = create_pubsub_payload(
        message_id,
        {
            "logs": [
                {
                    "timestamp": datetime.datetime.now().isoformat(),
                    "level": "INFO",
                    "message": "Test PubSub log message",
                    "source": "test-script",
                    "dummy": True,
                    "uuid": str(uuid.uuid4()),
                }
            ]
        },
    )
    print(f"Payload: {payload}")
    start = time.time()
    resp = requests.post(ENDPOINTS["pubsub"], json=payload, timeout=10)
    elapsed = time.time() - start
    print(f"Status: {resp.status_code}, Time: {elapsed:.2f}s, Body: {resp.json()}")
    assert resp.status_code == 200
    assert resp.json().get("summarization_status") == "background_processing"
    print("✅ /pubsub/api/log (first) passed.")

    # Test duplicate
    print("\n🧪 Testing /pubsub/api/log duplicate handling...")
    resp2 = requests.post(ENDPOINTS["pubsub"], json=payload, timeout=10)
    print(f"Status: {resp2.status_code}, Body: {resp2.json()}")
    assert resp2.status_code == 200
    assert resp2.json().get("status") == "duplicate"
    print("✅ /pubsub/api/log duplicate handling passed.")

    # Test idempotency
    print("\n🧪 Testing /pubsub/api/log idempotency...")
    resp3 = requests.post(ENDPOINTS["pubsub"], json=payload, timeout=10)
    print(f"Status: {resp3.status_code}, Body: {resp3.json()}")
    assert resp3.status_code == 200
    assert resp3.json().get("status") == "duplicate"
    print("✅ /pubsub/api/log idempotency passed.")


if __name__ == "__main__":
    test_api_log()
    test_api_log_prod()
    test_pubsub_log()
    print("\n🎉 All endpoint tests passed!")
