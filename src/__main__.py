"""Main entry point for the application."""

import argparse
import os
import sys
import uuid

from src.log_agent import LogIntelligenceAgentLangGraph
from src.log_config import setup_logging


def main():
    """Main entry point."""
    setup_logging()
    parser = argparse.ArgumentParser(description="Log Intelligence Agent")
    parser.add_argument("question", type=str, help="Your question for the agent")
    parser.add_argument(
        "--persona",
        type=str,
        choices=["business", "developer", "support"],
        default="business",
        help="Persona for the agent's response",
    )
    parser.add_argument(
        "--user-id",
        type=str,
        help="Optional: User ID for session tracking. If not provided, a CLI-specific user ID will be used.",
    )
    parser.add_argument(
        "--env-file",
        type=str,
        help="Optional: Path to environment file. If not provided, will use .env in current directory.",
    )
    parser.add_argument(
        "--session-id",
        type=str,
        help="Optional: Session ID for conversation continuity. If not provided, a new session will be created.",
    )
    args = parser.parse_args()

    # Determine environment file
    env_file = args.env_file
    if not env_file:
        env = os.getenv("ENV", "prod")
        env_file = f".env.{env}"
        if not os.path.exists(env_file):
            env_file = ".env"  # fallback to default .env

    user_question = args.question
    persona = args.persona
    args.user_id or "cli-user"

    # Use provided session ID or generate a unique one
    session_id = args.session_id or f"session-{uuid.uuid4()}"

    try:
        agent = LogIntelligenceAgentLangGraph()
        final_answer = agent.run(
            question=user_question, session_id=session_id, persona=persona
        )
        print("--- LangGraph Agent's Final Answer ---")
        print(final_answer)
    except ValueError as e:
        print(f"Configuration Error: {e}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
