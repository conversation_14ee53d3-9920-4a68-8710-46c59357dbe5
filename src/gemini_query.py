#!/usr/bin/env python3
import json
import os
from datetime import datetime
from typing import Dict, List, Optional

from src.client_managers import get_genai_manager
from src.env_config import EnvConfig
from src.retry_utils import call_google_genai_with_retry


class GeminiQuerier:
    # Class constant for output directory
    OUTPUT_DIR = "prompts"

    def __init__(self, config: EnvConfig):
        print("Initializing Google Generative AI and Gemini model...")

        # Use the provided config for consistency
        self.config = config
        self.genai_manager = get_genai_manager(self.config)
        self.client = self.genai_manager.get_client()
        self.model_name = self.config.model_name
        print("GeminiQuerier initialized.")

        # Create output directory if it doesn't exist
        os.makedirs(self.OUTPUT_DIR, exist_ok=True)

    def save_query_data(
        self, prompt: str, file_configs: List[Dict[str, any]], response: str
    ) -> str:
        """Save the query data to a JSON file with timestamp and response to a markdown file.

        Args:
            prompt: The prompt sent to Gemini
            file_configs: List of file configurations used
            response: Gemini's response

        Returns:
            Path to the saved JSON file
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save response to markdown file
        md_filename = f"{self.OUTPUT_DIR}/response_{timestamp}.md"
        with open(md_filename, "w") as f:
            f.write(response)
        print(f"Response saved to markdown file: {md_filename}")

        # Save all data to JSON file
        data = {
            "timestamp": timestamp,
            "prompt": prompt,
            "file_configs": file_configs,
            "response": response,
            "response_markdown_file": md_filename,
        }

        json_filename = f"{self.OUTPUT_DIR}/query_{timestamp}.json"
        with open(json_filename, "w") as f:
            json.dump(data, f, indent=2)

        print(f"Query data saved to {json_filename}")
        return json_filename

    def read_file_content(
        self, file_path: str, start_line: int = 1, num_lines: Optional[int] = None
    ) -> str:
        """Read content from a file with line control.

        Args:
            file_path: Path to the file to read
            start_line: Starting line number (1-based indexing)
            num_lines: Number of lines to read (None means read till end)

        Returns:
            String containing the requested lines from the file
        """
        print(
            f"Reading file: {file_path} (start_line={start_line}, num_lines={num_lines})"
        )
        try:
            with open(file_path, "r") as f:
                lines = f.readlines()
            print(f"Read {len(lines)} lines from {file_path}")

            if start_line < 1:
                print(f"start_line < 1 for {file_path}, resetting to 1")
                start_line = 1

            start_idx = start_line - 1
            if num_lines is not None:
                end_idx = start_idx + num_lines
                content = "".join(lines[start_idx:end_idx])
                print(
                    f"Returning lines {start_line} to {start_line + num_lines - 1} from {file_path}"
                )
            else:
                content = "".join(lines[start_idx:])
                print(f"Returning lines {start_line} to end from {file_path}")

            return content
        except Exception as e:
            print(f"Error reading file {file_path}: {str(e)}")
            return ""

    def query_with_files(self, prompt: str, file_configs: List[Dict[str, any]]) -> str:
        """Query Gemini with a prompt and attached file contents.

        Args:
            prompt: The main prompt to send to Gemini
            file_configs: List of dictionaries containing file configurations:
                        [{"path": "path/to/file",
                          "start_line": 1,  # optional
                          "num_lines": 100}] # optional
            temperature: Temperature for generation (0.0 to 1.0)

        Returns:
            Gemini's response as a string
        """
        print("Building complete prompt with file contents...")
        complete_prompt = [prompt + "\n\nHere are the file contents you requested:\n\n"]

        for config in file_configs:
            file_path = config["path"]
            start_line = config.get("start_line", 1)
            num_lines = config.get("num_lines", None)
            print(
                f"Processing file config: {file_path} (start_line={start_line}, num_lines={num_lines})"
            )

            content = self.read_file_content(file_path, start_line, num_lines)
            if content:
                file_section = f"\nContent from {file_path}"
                if num_lines:
                    file_section += (
                        f" (lines {start_line} to {start_line + num_lines - 1}):"
                    )
                else:
                    file_section += f" (from line {start_line}):"
                file_section += f"\n```\n{content}\n```\n"
                complete_prompt.append(file_section)
                print(f"Added content from {file_path} to prompt.")
            else:
                print(f"No content read from {file_path}, skipping.")

        print("Starting Gemini chat session...")
        response = call_google_genai_with_retry(
            client=self.client,
            model=self.model_name,
            contents=" ".join(complete_prompt),
        )
        response_text = response.text
        print("Received response from Gemini model.")

        # Save the query data
        saved_file = self.save_query_data(prompt, file_configs, response_text)
        print(f"Query data saved to {saved_file}")

        return response_text


def main():
    print("Starting GeminiQuerier main example usage...")
    env = os.getenv("ENV", "prod")
    env_file = f".env.{env}"
    if not os.path.exists(env_file):
        env_file = ".env"  # fallback to default .env

    # Load config and pass it to GeminiQuerier
    config = EnvConfig.load(env_file)
    querier = GeminiQuerier(config=config)

    # Example configuration
    files_to_read = [
        {
            "path": "downloaded/event-logs.service_events.jsonl",
            "start_line": 1,
            "num_lines": 120,
        }
    ]

    prompt = (
        "Please analyze these log files and tell me what instructions you will give"
        " an LLM to be able to effectively query these logs from a MongoDB database. These"
        " logs are stored as timeseries data in MongoDB."
        " When analyzing application outcomes, ensure that each unique application is counted"
        " only once, using its most recent event that includes a decision status. Later events"
        " should override earlier ones for the same application. Ignore any logs that do not"
        " contain a decision status. If you are giving examples of mongodb queries, use python."
        " Timestamp is BSON compatible, so take that into account when generating queries."
        "\n\n"
        " Example queries: "
        " 1. What happened to application <application_id>? (This is a very common query)"
        " 2. Give me 10 unique application ids that have been declined"
        " 3. What is the status of application <application_id>? (User wants the final status of the application)"
        " 4. Trend analysis over the past one year of the total loan amount approved each month"
        " 5. What is the distribution between approved and declined applications in the past 24 hours?"
        " 6. Give the url of the contract sent over to the applicant."
        " 7. Tell me about the applications from applicant <applicant_name>."
        " 8. Give all the applications belonging to the customer with phone number (*************."
        " 9. Give all the applications belonging to the customer with phone number **********."
        " 10. Give me the errors that happened this month, rank them by frequency. (When user asks for errors, do not confuse it with the application decline. Errors are HTTP status codes, tracebacks, etc.)"
        " 11. What is the impact of the error <error_name> on business (loans that could have been approved but didn't materialize or other ways the business could have been impacted)?"
        "\n\n**The actions of model directly determine the quality and accuracy of the user's answer. It must follow these steps with precision:**"
        "1.  **Formulate a Plan:** Carefully analyze the user's question and devise a clear, logical plan to retrieve and analyze the necessary logs."
        "2.  **Generate the Mongodb Query:** Write a Mongodb query to fetch the most relevant logs from the `{mongodb_db}` database, specifically using the `{collection_name}` collection. Your query must be as precise and efficient as possible."
        "3.  **Execute the Query:** Use the `query_mongodb` tool to run your query. This step is critical—ensure your query is correct and will return the data needed to answer the user's question."
        "4.  **Analyze the Results:** Examine the query results thoroughly. Assess whether you have enough information to answer the user's question with confidence."
        "5.  **Iterate if Needed:** If the initial results are insufficient, do not stop. Formulate a new plan, generate a refined query, and execute it. Repeat this process as many times as necessary until you have all the information required."
        "6.  **Summarize and Answer With Citations:** Once you have gathered enough information, present your answer in three parts:"
        "    a. Write a clear, easy-to-understand summary of your findings."
        "    b. Provide a step-by-step explanation of how you reached your conclusions. **Do not include table names or full SQL queries in the text output.**"
        "Start by understanding the user's question and formulating your first query."
        "NOTE:"
        "- Avoid unneccesary UUIDs, API endpoints, service names in text outputs. Timestamps should be human readable and in EST."
        "- Text output should be in markdown format."
        "- Errors mean when the api_status is not 200."
        "- For the same type of event, the one that occurs later in time takes precedence over earlier ones."
        "- Partially redact SSNs from the text output."
    ).format(
        mongodb_db=querier.config.mongodb_db,
        collection_name=querier.config.mongodb_collection,
    )
    print("Querying Gemini with files...")
    response = querier.query_with_files(prompt, files_to_read)
    print("Gemini's Response:\n" + response)
    print("\nGemini's Response:")
    print(response)


if __name__ == "__main__":
    main()
