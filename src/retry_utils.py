"""
Retry utilities for GenAI API calls with exponential backoff.
Provides shared retry logic to handle network errors across the application.
"""

import logging
import random
import time
from typing import Any, Callable, Optional

import httpcore
import httpx


def call_genai_with_retry(
    api_call: Callable,
    max_retries: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    logger: Optional[logging.Logger] = None,
) -> Any:
    """
    Call GenAI API with exponential backoff retry logic.

    Args:
        api_call: Function that makes the API call (should return response)
        max_retries: Maximum number of retry attempts
        base_delay: Base delay in seconds for exponential backoff
        max_delay: Maximum delay between retries
        logger: Optional logger instance for custom logging

    Returns:
        API response object

    Raises:
        Exception: If all retry attempts fail

    Example:
        def make_api_call():
            return client.models.generate_content(
                model=model_name,
                contents=contents
            )

        response = call_genai_with_retry(make_api_call)
    """
    if logger is None:
        logger = logging.getLogger(__name__)

    last_exception = None

    for attempt in range(max_retries + 1):
        try:
            return api_call()

        except (
            httpcore.ConnectError,
            httpcore.TimeoutException,
            httpcore.RemoteProtocolError,
            httpx.ConnectError,
            httpx.TimeoutException,
            httpx.RemoteProtocolError,
        ) as e:
            last_exception = e

            if attempt == max_retries:
                logger.error(
                    f"Failed to connect to GenAI after {max_retries + 1} attempts: {e}"
                )
                raise

            # Exponential backoff with jitter
            delay = min(base_delay * (2**attempt) + random.uniform(0, 1), max_delay)
            logger.warning(
                f"Network error on attempt {attempt + 1}: {e}. Retrying in {delay:.2f}s..."
            )
            time.sleep(delay)

        except Exception as e:
            # For non-network errors, don't retry
            logger.error(f"Non-retryable error calling GenAI: {e}")
            raise

    # Should not reach here, but just in case
    raise last_exception or Exception("Unexpected error in retry logic")


def call_vertex_ai_with_retry(
    model,
    prompt: str,
    max_retries: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    logger: Optional[logging.Logger] = None,
) -> Any:
    """
    Call Vertex AI API with exponential backoff retry logic.
    Convenience wrapper for Vertex AI GenerativeModel.generate_content().

    Args:
        model: The Vertex AI GenerativeModel instance
        prompt: Prompt to send to the model
        max_retries: Maximum number of retry attempts
        base_delay: Base delay in seconds for exponential backoff
        max_delay: Maximum delay between retries
        logger: Optional logger instance for custom logging

    Returns:
        Model response object

    Raises:
        Exception: If all retry attempts fail
    """

    def api_call():
        return model.generate_content(prompt)

    return call_genai_with_retry(
        api_call=api_call,
        max_retries=max_retries,
        base_delay=base_delay,
        max_delay=max_delay,
        logger=logger,
    )


def call_google_genai_with_retry(
    client,
    model: str,
    contents,
    config=None,
    max_retries: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    logger: Optional[logging.Logger] = None,
) -> Any:
    """
    Call Google GenAI API with exponential backoff retry logic.
    Convenience wrapper for google.genai client.models.generate_content().

    Args:
        client: The Google GenAI client instance
        model: Model name to use
        contents: Content to send to the model
        config: Optional generation config
        max_retries: Maximum number of retry attempts
        base_delay: Base delay in seconds for exponential backoff
        max_delay: Maximum delay between retries
        logger: Optional logger instance for custom logging

    Returns:
        GenAI response object

    Raises:
        Exception: If all retry attempts fail
    """

    def api_call():
        if config:
            return client.models.generate_content(
                model=model, contents=contents, config=config
            )
        else:
            return client.models.generate_content(model=model, contents=contents)

    return call_genai_with_retry(
        api_call=api_call,
        max_retries=max_retries,
        base_delay=base_delay,
        max_delay=max_delay,
        logger=logger,
    )
