class ResponseProcessor:
    """Utility class for processing model responses and tool calls."""

    @staticmethod
    def get_texts_and_tool_calls_from_response(
        response,
    ) -> tuple[list[str], list[dict]]:
        """Extract text content and tool calls from a model response."""
        texts = []
        tool_calls = []
        if not response.candidates:
            return texts, tool_calls

        candidate = response.candidates[0]
        if not hasattr(candidate, "content") or not candidate.content:
            return texts, tool_calls

        if not hasattr(candidate.content, "parts") or not candidate.content.parts:
            return texts, tool_calls

        for part in candidate.content.parts:
            if hasattr(part, "text") and part.text:
                texts.append(part.text)
            if hasattr(part, "function_call") and part.function_call:
                tool_calls.append(part.function_call)
        return texts, tool_calls

    @staticmethod
    def is_function_call(response) -> bool:
        """Check if the response contains function calls."""
        if not response.candidates:
            return False
        candidate = response.candidates[0]
        if not hasattr(candidate, "content") or not candidate.content:
            return False

        if not hasattr(candidate.content, "parts") or not candidate.content.parts:
            return False

        return any(
            hasattr(part, "function_call") and part.function_call is not None
            for part in candidate.content.parts
        )
