# Log Summarization Package

This package provides a comprehensive pipeline for generating AI-powered summaries of microservices logs using Google Cloud Vertex AI. The pipeline fetches only unprocessed logs, adds AI-generated summaries, and stores complete log documents with summaries in a separate MongoDB collection.

## Overview

The package provides a single unified script `batch_process_logs.py` that handles the entire pipeline:

1. **Fetch logs to process** - Either process logs newer than latest timestamp, or find missing log IDs
2. **Generate summaries** - Use Vertex AI batch processing or online inference
3. **Store complete logs** - Save full log documents with added summary fields

## Processing Modes

The script supports two modes for finding logs to process:

- **Default mode**: Processes logs newer than the latest timestamp in the summarized collection
- **Missing ID mode** (`--find-missing`): Finds all log IDs present in main collection but missing from summarized collection, regardless of timestamp

## Key Features

- **Incremental processing**: Only processes new logs not already summarized
- **Resume capability**: Can resume from any stage of batch processing
- **Complete log preservation**: Stores full original logs with summary additions
- **Flexible processing**: Supports both batch and online inference modes
- **Safety confirmation**: Shows preview of logs to be processed and requires user confirmation
- **Missing log detection**: Find and process logs missing from summarized collection

## Usage

### Full Pipeline (Recommended)

Process all unprocessed logs in one command:

```bash
ENV=local python -m src.summarize_logs.batch_process_logs \
  --stage full \
  --filter context.application_id=01JVSX94H307RYBGB3GS2VWZVD \
  --model gemini-2.5-flash
```

### Online Processing (Faster for small batches)

```bash
ENV=local python -m src.summarize_logs.batch_process_logs \
  --online \
  --filter level=error \
  --model gemini-2.5-flash
```

### Find and Process Missing Logs

Find all log IDs that exist in the main collection but are missing from the summarized collection (ignores timestamp ordering):

```bash
ENV=local python -m src.summarize_logs.batch_process_logs \
  --find-missing \
  --filter context.application_id=01JVSX94H307RYBGB3GS2VWZVD \
  --model gemini-2.5-flash
```

This is useful for:
- **Backfilling missed logs** that were skipped due to processing errors
- **Fixing gaps** in summarized collection from failed batch jobs
- **Reprocessing logs** that may have been missed due to timestamp issues

### Automatic Processing Confirmation

By default, the script will show a preview of logs to be processed and ask for user confirmation before starting:

```bash
# Shows preview and asks for confirmation
ENV=local python -m src.summarize_logs.batch_process_logs --find-missing

# Skip confirmation prompt (auto-confirm)
ENV=local python -m src.summarize_logs.batch_process_logs --find-missing --yes
```

The preview shows:
- **Total number of logs** to be processed
- **Sample log IDs** (up to 10) with timestamps and context
- **Processing mode** and model information
- **Timestamp range** of logs to be processed

### Resume from Specific Stages

**Submit job only:**
```bash
ENV=local python -m src.summarize_logs.batch_process_logs \
  --stage submit \
  --filter context.application_id=01JVSX94H307RYBGB3GS2VWZVD
```

**Check status and download results:**
```bash
ENV=local python -m src.summarize_logs.batch_process_logs \
  --stage check \
  --job-resource-name projects/.../batchPredictionJobs/123456789 \
  --job-info-file log_summarization_20250629_024123_job_info.json
```

**Upload results to MongoDB:**
```bash
ENV=local python -m src.summarize_logs.batch_process_logs \
  --stage upload \
  --results-file complete_logs_with_summaries_20250629_024123.jsonl
```

## Command Line Options

- `--stage`: Processing stage (`submit`, `check`, `upload`, `full`) - default: `full`
- `--filter`: MongoDB filter criteria (e.g., `level=error`, `context.application_id=123`)
- `--model`: Gemini model to use (default: `gemini-2.5-flash`)
- `--prompt-file`: Path to custom prompt file (optional)
- `--online`: Use online inference instead of batch processing
- `--find-missing`: Find log IDs missing from summarized collection (ignores timestamp ordering)
- `--limit`: Limit number of logs to process (useful for testing)
- `--yes`: Automatically confirm processing without showing preview (skip confirmation prompt)
- `--job-resource-name`: Job resource name for resuming from check/upload stages
- `--job-info-file`: Job info file for resuming from check/upload stages
- `--results-file`: Results file for upload stage

## Supported Models

- `gemini-2.5-pro`
- `gemini-2.5-flash` (default)
- `gemini-2.0-flash`
- `gemini-2.0-flash-001`
- `gemini-2.0-flash-lite`

## Requirements

- Google Cloud Project with Vertex AI enabled
- GCS bucket for batch processing files
- MongoDB database with two collections:
  - Source collection (`MONGODB_COLLECTION`) - original logs
  - Summarized collection (`MONGODB_SUMMARIZED_COLLECTION`) - logs with summaries
- Appropriate environment configuration (`.env.local`, etc.)

## Configuration

Ensure your environment files contain:
- `GCP_PROJECT_ID`: Google Cloud Project ID
- `GCP_LOCATION`: Vertex AI region (e.g., `us-central1`)
- `GCP_SUMMARY_BUCKET_NAME`: GCS bucket for batch processing files
- `MONGODB_URI`: MongoDB connection string
- `MONGODB_DB`: MongoDB database name
- `MONGODB_COLLECTION`: Source collection name (original logs)
- `MONGODB_SUMMARIZED_COLLECTION`: Target collection name (logs with summaries)
- `MODEL_NAME`: Default model name

## Output Format

The pipeline stores complete log documents with added summary fields in the summarized collection:

```json
{
  "_id": "original_log_id",
  "timestamp": "2025-01-XX...",
  "context": { "application_id": "...", ... },
  "request": { ... },
  "response": { ... },
  "summary": "**POS_FINANCING EXTERNAL POST /applications**...",
  "summary_generated_at": "2025-01-XX...",
  "summary_model": "gemini-2.5-flash",
  "summary_batch_job_id": "1234567890123456789"
}
```

**Added Summary Fields:**
- `summary`: AI-generated summary text
- `summary_generated_at`: ISO timestamp when summary was created
- `summary_model`: Model used for generation (e.g., "gemini-2.5-flash")
- `summary_batch_job_id`: Batch job ID for traceability
  - For batch processing: Vertex AI batch job ID (e.g., "1234567890123456789")
  - For online processing: Session ID (e.g., "online_20250629_143025")

**Summary Content Format:**
```
**[SERVICE] [REQUEST_TYPE] [METHOD] [ENDPOINT]**
**Status:** [HTTP_CODE] - [BUSINESS_STATUS]
**Context:** [Key business identifiers]
**Summary:** [1-2 sentence description]
**Key Details:** [Important specifics]
```

**Example Summary:**
```
**POS_FINANCING EXTERNAL POST /applications**
**Status:** 200 - APPROVED
**Context:** App ID abc123, Transaction txn456
**Summary:** Jason Hamilton applied for $15,000 loan and was approved with 2 offers.
**Key Details:** Best offer: $15,000 at 12.5% APR from TGUC
```
