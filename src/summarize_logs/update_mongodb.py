import argparse
import json
import os

from bson import ObjectId

from ..database_manager import DatabaseManager
from ..env_config import EnvConfig


def main():
    """
    LEGACY SCRIPT: Main function to update MongoDB documents with predictions from a results file.

    Note: This functionality is now integrated into batch_process_logs.py --stage upload.
    This script is kept for backward compatibility and manual operations.
    """
    parser = argparse.ArgumentParser(
        description="Update MongoDB documents with predictions from a results file."
    )
    parser.add_argument(
        "--results-file",
        required=True,
        help="Path to the JSONL file containing the results ('_id', 'prediction', and metadata).",
    )
    args = parser.parse_args()

    # 1. Configuration and Connection
    print("Loading configuration...")
    env_name = os.getenv("ENV", "local")
    config = EnvConfig.load(f".env.{env_name}")
    db_manager = DatabaseManager(config)

    # 2. Read Results File
    try:
        with open(args.results_file, "r") as f:
            results = [json.loads(line) for line in f]
    except FileNotFoundError:
        print(f"Error: Results file not found at '{args.results_file}'. Exiting.")
        return
    except json.JSONDecodeError:
        print(f"Error: Could not decode JSON from '{args.results_file}'. Exiting.")
        return

    if not results:
        print("No results found in the file. Exiting.")
        return

    # 3. Insert into MongoDB
    print(f"Starting insertion of {len(results)} documents into MongoDB...")
    collection_name = config.mongodb_summarized_collection
    updated_count = 0
    for i, result in enumerate(results):
        doc_id = result.get("_id")
        prediction = result.get("prediction")

        if not doc_id or not prediction:
            print(f"Skipping invalid record at line {i+1}: {result}")
            continue

        try:
            # Try to insert a new document with the provided _id and metadata
            new_doc = {
                "_id": ObjectId(doc_id),
                "summary": prediction,
                "source": "batch_prediction",
            }

            # Include original log metadata
            if result.get("timestamp"):
                new_doc["timestamp"] = result["timestamp"]
            if result.get("application_id"):
                new_doc["application_id"] = result["application_id"]

            insert_result = db_manager.database[collection_name].insert_one(new_doc)
            if insert_result.inserted_id:
                updated_count += 1
        except Exception as e:
            print(f"Error inserting document {doc_id}: {e}")

        # Print progress
        if (i + 1) % 10 == 0 or (i + 1) == len(results):
            print(f"  Processed {i + 1}/{len(results)} documents...")

    print("\n" + "=" * 50)
    if updated_count == len(results):
        print("MongoDB insertion completed successfully!")
    elif updated_count > 0:
        print("MongoDB insertion completed with some failures!")
    else:
        print("MongoDB insertion failed!")
    print(f"  Successfully inserted {updated_count} of {len(results)} documents.")
    print("=" * 50)


if __name__ == "__main__":
    main()
