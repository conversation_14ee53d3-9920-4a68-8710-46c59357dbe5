import argparse
import json
import os
import time
from datetime import datetime, timedelta, timezone

import vertexai
from bson import ObjectId
from google.cloud import aiplatform
from google.cloud.storage import Client as GCSClient
from pymongo.errors import BulkWriteError
from vertexai.generative_models import GenerativeModel

from ..database_manager import DatabaseManager
from ..env_config import EnvConfig
from ..prompt_manager import PromptManager
from ..retry_utils import call_vertex_ai_with_retry


def json_converter(o):
    if isinstance(o, datetime):
        return o.isoformat()
    if isinstance(o, ObjectId):
        return str(o)


def get_unprocessed_logs(db_manager, config, log_filter=None, limit=None):
    """
    Fetch logs from mongodb_collection that are newer than the latest processed log in mongodb_summarized_collection.

    Args:
        db_manager: DatabaseManager instance
        config: Environment configuration
        log_filter: Optional filter to apply to source logs
        limit: Optional limit on number of logs to fetch

    Returns:
        List of unprocessed log documents
    """
    print("Checking latest processed timestamp from summarized collection...")

    # Get the latest timestamp from summarized collection
    latest_timestamp = None
    try:
        latest_doc = db_manager.database[config.mongodb_summarized_collection].find_one(
            {}, sort=[("timestamp", -1)]
        )
        if latest_doc and "timestamp" in latest_doc:
            latest_timestamp = latest_doc["timestamp"]
            print(f"Found latest processed timestamp: {latest_timestamp}")
        else:
            print("No processed logs found or no timestamp field")
    except Exception as e:
        print(
            f"Warning: Could not fetch latest timestamp (collection may not exist): {e}"
        )

    # Build filter for unprocessed logs (newer than latest processed)
    unprocessed_filter = {}
    if latest_timestamp:
        unprocessed_filter["timestamp"] = {"$gt": latest_timestamp}
        print(f"Filtering for logs newer than: {latest_timestamp}")
    else:
        print("No timestamp filter applied - processing all logs")

    # Add any additional filters
    if log_filter:
        unprocessed_filter.update(log_filter)

    print(f"Fetching unprocessed logs with filter: {unprocessed_filter}")
    if limit:
        print(f"Limiting to {limit} logs for testing")

    # Fetch unprocessed logs with ascending timestamp order and optional limit
    if limit:
        unprocessed_logs = list(
            db_manager.database[config.mongodb_collection]
            .find(unprocessed_filter)
            .sort([("timestamp", 1)])  # 1 = ascending order
            .limit(limit)
        )
    else:
        unprocessed_logs = list(
            db_manager.database[config.mongodb_collection]
            .find(unprocessed_filter)
            .sort([("timestamp", 1)])
        )  # 1 = ascending order

    print(f"Found {len(unprocessed_logs)} unprocessed logs")
    return unprocessed_logs


def get_orphaned_logs_in_summarized(db_manager, config):
    """
    Find all log-ids that exist in the summarized collection but are missing from the main collection.

    This function identifies "orphaned" logs in the summarized collection that no longer have
    corresponding entries in the main collection, which can happen due to data cleanup or corruption.

    Args:
        db_manager: DatabaseManager instance
        config: Environment configuration

    Returns:
        List of log documents from summarized collection that don't exist in main collection
    """
    print("Finding orphaned logs in summarized collection...")

    try:
        # Get all log IDs from the main collection
        print("Fetching all log IDs from main collection...")
        main_collection = db_manager.database[config.mongodb_collection]

        main_ids = set()
        cursor = main_collection.find({}, {"_id": 1})
        for doc in cursor:
            main_ids.add(doc["_id"])

        print(f"Found {len(main_ids)} log IDs in main collection")

        # Get all log IDs from the summarized collection
        print("Fetching all log IDs from summarized collection...")
        summarized_collection = db_manager.database[
            config.mongodb_summarized_collection
        ]

        summarized_logs = []
        summarized_ids = set()

        try:
            cursor = summarized_collection.find({})
            for doc in cursor:
                doc_id = doc["_id"]
                summarized_ids.add(doc_id)
                summarized_logs.append(doc)
        except Exception as e:
            print(f"Warning: Error reading from summarized collection: {e}")
            return []

        print(f"Found {len(summarized_ids)} log IDs in summarized collection")

        # Find orphaned IDs (in summarized but not in main)
        orphaned_ids = summarized_ids - main_ids
        print(f"Found {len(orphaned_ids)} orphaned log IDs in summarized collection")

        if not orphaned_ids:
            print(
                "✅ No orphaned logs found. All summarized logs have corresponding originals!"
            )
            return []

        # Return the full documents for orphaned IDs
        orphaned_logs = [log for log in summarized_logs if log["_id"] in orphaned_ids]

        print(f"Successfully identified {len(orphaned_logs)} orphaned log documents")

        # Log some statistics
        if orphaned_logs:
            # Sort by timestamp if available
            timestamped_logs = [log for log in orphaned_logs if log.get("timestamp")]
            if timestamped_logs:
                timestamped_logs.sort(key=lambda x: x["timestamp"])
                first_timestamp = timestamped_logs[0].get("timestamp", "unknown")
                last_timestamp = timestamped_logs[-1].get("timestamp", "unknown")
                print(
                    f"Orphaned logs timestamp range: {first_timestamp} to {last_timestamp}"
                )

        return orphaned_logs

    except Exception as e:
        print(f"❌ Error finding orphaned logs: {e}")
        import traceback

        traceback.print_exc()
        return []


def get_missing_logs_by_id(
    db_manager, config, log_filter=None, limit=None, ignore_recent=None
):
    """
    Find all log-ids that exist in the main collection but are missing from the summarized collection.

    This function compares log IDs between the two collections and returns documents for missing IDs,
    regardless of timestamp, which is useful for backfilling missed logs.

    Args:
        db_manager: DatabaseManager instance
        config: Environment configuration
        log_filter: Optional filter to apply to source logs
        limit: Optional limit on number of logs to fetch
        ignore_recent: Optional timedelta to ignore logs from the last n minutes/hours/days

    Returns:
        List of log documents that exist in main collection but missing from summarized collection
    """
    print("Finding log-ids missing from summarized collection...")

    try:
        # Get all log IDs from the main collection
        print("Fetching all log IDs from main collection...")
        main_collection = db_manager.database[config.mongodb_collection]

        # Apply log_filter if provided
        main_filter = log_filter.copy() if log_filter else {}

        # Filter out dummy logs
        main_filter["dummy"] = {"$ne": True}

        # Filter out recent logs if ignore_recent is provided
        if ignore_recent:
            cutoff_time = datetime.utcnow() - ignore_recent
            main_filter["timestamp"] = {"$lt": cutoff_time}
            print(f"Ignoring logs newer than: {cutoff_time}")
        else:
            print("No time-based filtering applied")

        # Get all _id values from main collection
        main_ids = set()
        cursor = main_collection.find(main_filter, {"_id": 1})
        for doc in cursor:
            main_ids.add(doc["_id"])

        print(f"Found {len(main_ids)} total log IDs in main collection")

        # Get all log IDs from the summarized collection
        print("Fetching all log IDs from summarized collection...")
        summarized_collection = db_manager.database[
            config.mongodb_summarized_collection
        ]

        summarized_ids = set()
        try:
            cursor = summarized_collection.find({}, {"original_log_id": 1, "_id": 1})
            for doc in cursor:
                # Try both original_log_id and _id fields as some implementations may vary
                if "original_log_id" in doc:
                    summarized_ids.add(
                        ObjectId(doc["original_log_id"])
                        if isinstance(doc["original_log_id"], str)
                        else doc["original_log_id"]
                    )
                else:
                    summarized_ids.add(doc["_id"])
        except Exception as e:
            print(f"Warning: Error reading from summarized collection: {e}")
            print("Assuming summarized collection is empty or doesn't exist")

        print(f"Found {len(summarized_ids)} log IDs in summarized collection")

        # Find missing IDs (in main but not in summarized)
        missing_ids = main_ids - summarized_ids
        print(f"Found {len(missing_ids)} log IDs missing from summarized collection")

        if not missing_ids:
            print("✅ No missing logs found. All logs are already summarized!")
            return []

        # Fetch full documents for missing IDs
        print("Fetching full documents for missing log IDs...")

        # Convert missing_ids to list and apply limit if specified
        missing_ids_list = list(missing_ids)
        if limit:
            missing_ids_list = missing_ids_list[:limit]
            print(f"Limiting to {limit} missing logs for processing")

        # Build query for missing documents
        missing_filter = {"_id": {"$in": missing_ids_list}}

        # Filter out dummy logs
        missing_filter["dummy"] = {"$ne": True}

        if log_filter:
            # Combine with additional filters
            for key, value in log_filter.items():
                if key != "_id":  # Don't override our _id filter
                    missing_filter[key] = value

        # Fetch the actual documents
        missing_logs = list(
            main_collection.find(missing_filter).sort([("timestamp", 1)])
        )

        print(f"Successfully retrieved {len(missing_logs)} missing log documents")

        # Log some statistics
        if missing_logs:
            first_timestamp = missing_logs[0].get("timestamp", "unknown")
            last_timestamp = missing_logs[-1].get("timestamp", "unknown")
            print(
                f"Missing logs timestamp range: {first_timestamp} to {last_timestamp}"
            )

        return missing_logs

    except Exception as e:
        print(f"❌ Error finding missing logs: {e}")
        import traceback

        traceback.print_exc()
        return []


def submit_batch_job(logs, prompt_content, model_name, config):
    """Submit a batch prediction job to Vertex AI."""
    print(f"Submitting batch job for {len(logs)} logs...")

    # Ensure output directory exists
    output_dir = ensure_output_directory()

    # Validate model support for batch processing
    batch_supported_models = [
        "gemini-2.5-pro",
        "gemini-2.5-flash",
        "gemini-2.0-flash",
        "gemini-2.0-flash-001",
        "gemini-2.0-flash-lite",
    ]

    if model_name not in batch_supported_models:
        print(f"⚠️  Warning: Model '{model_name}' may not support batch processing.")
        print(f"Supported models: {', '.join(batch_supported_models)}")
        print("Continuing anyway, but the job may fail...")

    # Prepare files
    job_name_prefix = f"log_summarization_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    input_filename = f"{job_name_prefix}_input.jsonl"
    job_info_filename = os.path.join(output_dir, f"{job_name_prefix}_job_info.json")
    gcs_client = GCSClient(project=config.project_id)
    gcs_bucket_name = config.gcp_summary_bucket_name

    # Store job information and full logs
    job_info = {
        "job_name_prefix": job_name_prefix,
        "model_name": model_name,
        "submitted_at": datetime.utcnow().isoformat(),
        "log_count": len(logs),
        "log_id_range": {
            "first_log_id": str(logs[0]["_id"]) if logs else None,
            "last_log_id": str(logs[-1]["_id"]) if logs else None,
            "first_timestamp": (
                logs[0].get("timestamp").isoformat()
                if logs and logs[0].get("timestamp")
                else None
            ),
            "last_timestamp": (
                logs[-1].get("timestamp").isoformat()
                if logs and logs[-1].get("timestamp")
                else None
            ),
        },
        "full_logs": [
            json.loads(json.dumps(log, default=json_converter)) for log in logs
        ],
    }

    # Create batch input file
    with open(input_filename, "w") as f:
        for i, log in enumerate(logs):
            full_prompt = f"{prompt_content}\n\nLog Details:\n{json.dumps(log, default=json_converter)}"
            instance = {
                "_id": str(log["_id"]),
                "request": {
                    "contents": [{"role": "user", "parts": [{"text": full_prompt}]}]
                },
            }
            f.write(json.dumps(instance) + "\n")

    # Upload to GCS
    print(f"Uploading input file to GCS...")
    bucket = gcs_client.bucket(gcs_bucket_name)
    blob = bucket.blob(f"batch_inputs/{input_filename}")
    blob.upload_from_filename(input_filename)
    gcs_input_uri = f"gs://{gcs_bucket_name}/batch_inputs/{input_filename}"
    print(f"Upload complete. GCS URI: {gcs_input_uri}")

    # Submit job
    print("Submitting batch prediction job...")
    gcs_output_uri = f"gs://{gcs_bucket_name}/batch_outputs/{job_name_prefix}/"

    try:
        batch_model_name = f"publishers/google/models/{model_name}"
        print("Creating BatchPredictionJob...")
        batch_prediction_job = aiplatform.BatchPredictionJob.create(
            job_display_name=f"log-summarization-{datetime.now().strftime('%Y%m%d-%H%M%S')}",
            model_name=batch_model_name,
            gcs_source=gcs_input_uri,
            gcs_destination_prefix=gcs_output_uri,
            sync=False,
        )
        print("✅ BatchPredictionJob.create() completed successfully")

    except Exception as e:
        print(f"❌ Error during job creation: {e}")
        if hasattr(e, "details"):
            print(f"Error details: {e.details()}")

        # Cleanup
        try:
            os.remove(input_filename)
        except OSError:
            pass
        return None, None

    try:
        # Wait a moment for the job resource to be fully created
        import time

        # Retry accessing job properties with backoff
        max_retries = 5
        retry_delay = 2

        for attempt in range(max_retries):
            try:
                # Try to access basic job properties
                resource_name = batch_prediction_job.resource_name
                display_name = batch_prediction_job.display_name

                print(f"✅ Job properties accessible on attempt {attempt + 1}")
                break

            except Exception as access_error:
                if attempt < max_retries - 1:
                    print(
                        f"⏳ Attempt {attempt + 1}/{max_retries}: Job properties not yet accessible - {access_error}"
                    )
                    print(f"   Waiting {retry_delay} seconds before retry...")
                    time.sleep(retry_delay)
                    retry_delay *= 1.5  # Exponential backoff
                else:
                    print(
                        f"❌ Failed to access job properties after {max_retries} attempts: {access_error}"
                    )
                    raise access_error

        # Update job info with job details
        job_info["job_resource_name"] = resource_name
        job_info["job_display_name"] = display_name
        job_info["job_id"] = resource_name.split("/")[-1]
        job_info["gcs_output_uri"] = gcs_output_uri

        # Save job info
        with open(job_info_filename, "w") as f:
            json.dump(job_info, f, indent=2)

        abs_job_info_path = os.path.abspath(job_info_filename)
        print(f"📁 Job info saved to: {abs_job_info_path}")
        print(
            f"📊 Processing {len(logs)} logs from {job_info['log_id_range']['first_timestamp']} to {job_info['log_id_range']['last_timestamp']}"
        )
        print("BatchPredictionJob created. Resource name:", resource_name)
        print("To use this BatchPredictionJob in another session:")
        print(f"bpj = aiplatform.BatchPredictionJob('{resource_name}')")
        print(f"Job info file: {abs_job_info_path}")
        print("\nTo resume this job later:")
        print(
            f"python -m src.summarize_logs.batch_process_logs --stage check --job-resource-name {resource_name} --job-info-file {abs_job_info_path}"
        )

        # Extract project and location for console URL
        resource_parts = resource_name.split("/")
        project_id = resource_parts[1]
        location = resource_parts[3]
        job_id = resource_parts[5]
        console_url = f"https://console.cloud.google.com/ai/platform/locations/{location}/batch-predictions/{job_id}?project={project_id}"
        print(f"View Batch Prediction Job:\n{console_url}")

        # Show initial job state (with retry for this property too)
        try:
            job_state = batch_prediction_job.state
            print(f"BatchPredictionJob {resource_name} current state:")
            print(job_state)
        except Exception as state_error:
            print(f"⚠️ Could not access initial job state: {state_error}")
            print(
                "This is normal for newly created jobs - state will be available shortly"
            )

        # Cleanup temporary files
        os.remove(input_filename)

        return resource_name, job_info_filename

    except Exception as e:
        print(f"❌ Error during job info processing: {e}")
        if hasattr(e, "details"):
            print(f"Error details: {e.details()}")

        # Even if job info processing fails, the job was created successfully
        # Try to return the job resource name if we have it
        if "resource_name" in locals():
            print(
                "⚠️ Job was created successfully but there was an error processing job info"
            )
            print(f"Job resource name: {resource_name}")
            print("You can check job status manually or retry with --stage check")
            return resource_name, None
        elif "batch_prediction_job" in locals():
            try:
                job_resource_name = batch_prediction_job.resource_name
                print(
                    "⚠️ Job was created successfully but there was an error processing job info"
                )
                print(f"Job resource name: {job_resource_name}")
                print("You can check job status manually or retry with --stage check")
                return job_resource_name, None
            except:
                pass

        # Cleanup
        try:
            os.remove(input_filename)
        except OSError:
            pass
        return None, None


def get_ny_time():
    """Get current time in NY timezone (handles EST/EDT automatically)."""
    try:
        # Try to use zoneinfo for proper timezone handling (Python 3.9+)
        from zoneinfo import ZoneInfo

        ny_tz = ZoneInfo("America/New_York")
        return datetime.now(ny_tz).strftime("%Y-%m-%d %H:%M:%S %Z")
    except ImportError:
        # Fallback for older Python versions - approximate EST/EDT
        is_dst = time.daylight and time.localtime().tm_isdst
        offset_hours = -4 if is_dst else -5  # EDT vs EST
        tz_name = "EDT" if is_dst else "EST"
        ny_tz = timezone(timedelta(hours=offset_hours))
        return datetime.now(ny_tz).strftime(f"%Y-%m-%d %H:%M:%S {tz_name}")


def check_job_status_and_download(job_resource_name, job_info_filename, config):
    """Check job status and download results when complete."""
    print(f"Checking status of job: {job_resource_name}")

    # Ensure output directory exists
    output_dir = ensure_output_directory()

    # Load job info
    with open(job_info_filename, "r") as f:
        job_info = json.load(f)

    gcs_client = GCSClient(project=config.project_id)

    # Get the batch prediction job
    job = aiplatform.BatchPredictionJob(job_resource_name)

    # Check job state
    completed_states = [
        "JOB_STATE_SUCCEEDED",
        "JOB_STATE_FAILED",
        "JOB_STATE_CANCELLED",
    ]

    print("🔄 Monitoring job status (Ctrl+C to stop and resume later)...")

    state_name = "UNKNOWN"  # Initialize state_name for KeyboardInterrupt handler
    try:
        while str(job.state).split(".")[-1] not in completed_states:
            state_name = (
                str(job.state).split(".")[-1]
                if "." in str(job.state)
                else str(job.state)
            ).replace("JOB_STATE_", "")

            current_time = get_ny_time()
            # Use \r to overwrite the same line for cleaner output
            print(
                f"\r📊 Status: {state_name} | Last checked: {current_time}",
                end="",
                flush=True,
            )

            time.sleep(30)
            try:
                job = aiplatform.BatchPredictionJob(job_resource_name)
            except Exception as e:
                print(f"\n❌ Error refreshing job status: {e}")
                break

    except KeyboardInterrupt:
        print(f"\n⏸️  Monitoring stopped. Current status: {state_name}")
        print(
            f"To resume: python -m src.summarize_logs.batch_process_logs --stage check --job-resource-name {job_resource_name} --job-info-file {job_info_filename}"
        )
        return None

    # Final status on new line
    final_state = (
        str(job.state).split(".")[-1] if "." in str(job.state) else str(job.state)
    ).replace("JOB_STATE_", "")

    print(f"\n✅ Final status: {final_state} | Completed at: {get_ny_time()}")

    if final_state != "SUCCEEDED":
        print("❌ Job did not succeed.")
        if hasattr(job, "error") and job.error:
            print(f"Error details: {job.error}")
        return None

    # Download results
    print("✅ Job succeeded. Downloading results...")
    output_uri = job.output_info.gcs_output_directory
    bucket_name, prefix = output_uri.replace("gs://", "").split("/", 1)
    bucket = gcs_client.bucket(bucket_name)

    # Download prediction files
    prediction_files = []
    for blob in bucket.list_blobs(prefix=prefix):
        if (
            blob.name.endswith(".jsonl") and blob.size > 0
        ):  # Only download non-empty jsonl files
            # Get just the filename for local storage
            filename_only = blob.name.split("/")[-1]
            if filename_only == "predictions.jsonl":  # Main predictions file
                local_filename = os.path.join(
                    output_dir, f"predictions_{job_info['job_name_prefix']}.jsonl"
                )
                blob.download_to_filename(local_filename)
                prediction_files.append(local_filename)
                print(f"Downloaded: {local_filename} ({blob.size} bytes)")
                break  # We found the main predictions file

    if not prediction_files:
        print("❌ No prediction files found.")
        return None

    # Process predictions and merge with original logs
    results_filename = os.path.join(
        output_dir, f"complete_logs_with_summaries_{job_info['job_name_prefix']}.jsonl"
    )

    with open(prediction_files[0], "r") as pred_file:
        predictions = [json.loads(line) for line in pred_file]

    # Create a mapping from _id to original log for proper matching
    log_mapping = {}
    for i, log in enumerate(job_info["full_logs"]):
        log_id = str(log["_id"])  # Use _id directly
        log_mapping[log_id] = log

    complete_logs = []
    unmatched_predictions = 0

    for prediction in predictions:
        # Extract _id and summary from prediction
        log_id = prediction.get("_id")
        if not log_id:
            print(f"⚠️ Warning: Prediction missing _id, skipping...")
            unmatched_predictions += 1
            continue

        original_log = log_mapping.get(log_id)
        if not original_log:
            print(f"⚠️ Warning: No matching log found for _id {log_id}, skipping...")
            unmatched_predictions += 1
            continue

        summary = ""
        if "response" in prediction and prediction["response"]:
            response_data = prediction["response"]
            if "candidates" in response_data and response_data["candidates"]:
                candidate = response_data["candidates"][0]
                if "content" in candidate and candidate["content"]:
                    content_data = candidate["content"]
                    if "parts" in content_data and content_data["parts"]:
                        if "text" in content_data["parts"][0]:
                            summary = content_data["parts"][0]["text"]

        if summary:
            # Create complete log with summary
            complete_log = original_log.copy()
            complete_log["summary"] = summary.strip()
            complete_log["summary_generated_at"] = datetime.utcnow().isoformat()
            complete_log["summary_model"] = job_info["model_name"]
            complete_log["summary_batch_job_id"] = job_info.get("job_id", "unknown")
            complete_logs.append(complete_log)

    if unmatched_predictions > 0:
        print(
            f"⚠️ Warning: {unmatched_predictions} predictions could not be matched to original logs"
        )

    # Save complete logs
    with open(results_filename, "w") as f:
        for complete_log in complete_logs:
            f.write(json.dumps(complete_log, default=json_converter) + "\n")

    print(f"✅ Results processed and saved to: {results_filename}")
    print(f"📊 Successfully processed {len(complete_logs)} logs")
    print(f"🔍 Batch job ID: {job_info.get('job_id', 'unknown')}")

    # Cleanup prediction files
    for pred_file in prediction_files:
        try:
            os.remove(pred_file)
        except OSError:
            pass

    return results_filename


def upload_to_mongodb(results_filename, config):
    """Upload complete logs with summaries to MongoDB using bulk operations."""
    print(f"Uploading logs from {results_filename} to MongoDB...")

    # Load complete logs
    with open(results_filename, "r") as f:
        complete_logs = [json.loads(line) for line in f]

    if not complete_logs:
        print("❌ No logs to upload.")
        return False

    # Upload to MongoDB
    db_manager = DatabaseManager(config)
    collection = db_manager.database[config.mongodb_summarized_collection]

    print(f"Preprocessing {len(complete_logs)} logs for bulk upload...")

    # Pre-process all logs - convert types and handle potential issues
    processed_logs = []
    processing_errors = 0

    for i, log in enumerate(complete_logs, 1):
        try:
            # Convert _id string back to ObjectId if needed
            if isinstance(log["_id"], str):
                log["_id"] = ObjectId(log["_id"])

            # Convert timestamp string to datetime object if needed
            if "timestamp" in log and isinstance(log["timestamp"], str):
                try:
                    # Parse ISO format timestamp string to datetime
                    log["timestamp"] = datetime.fromisoformat(
                        log["timestamp"].replace("Z", "+00:00")
                    )
                except (ValueError, AttributeError):
                    # If parsing fails, remove the timestamp field to avoid MongoDB errors
                    log.pop("timestamp", None)

            processed_logs.append(log)
        except Exception as e:
            print(f"Error preprocessing log {i}: {e}")
            processing_errors += 1

        # Progress update for preprocessing
        if i % 100 == 0 or i == len(complete_logs):
            print(f"  Preprocessed {i}/{len(complete_logs)} logs...")

    if processing_errors > 0:
        print(f"⚠️ {processing_errors} logs failed preprocessing and will be skipped")

    if not processed_logs:
        print("❌ No valid logs to upload after preprocessing.")
        return False

    # Check for existing documents in bulk
    print("Checking for existing documents...")
    log_ids = [log["_id"] for log in processed_logs]
    existing_ids = set()

    try:
        existing_docs = collection.find({"_id": {"$in": log_ids}}, {"_id": 1})
        existing_ids = {doc["_id"] for doc in existing_docs}

        if existing_ids:
            print(
                f"⚠️ Found {len(existing_ids)} existing documents - these will be skipped"
            )
    except Exception as e:
        print(f"Warning: Could not check for existing documents: {e}")

    # Filter out existing documents
    new_logs = [log for log in processed_logs if log["_id"] not in existing_ids]

    if not new_logs:
        print("❌ No new logs to upload - all documents already exist.")
        return False

    print(f"Proceeding with batched bulk upload of {len(new_logs)} new logs...")

    # Perform bulk insert in batches
    batch_size = 500
    inserted_count = 0
    failed_count = len(existing_ids) + processing_errors
    total_batches = (len(new_logs) + batch_size - 1) // batch_size

    timeseries_fix_attempted = False

    for batch_num in range(total_batches):
        start_idx = batch_num * batch_size
        end_idx = min(start_idx + batch_size, len(new_logs))
        batch = new_logs[start_idx:end_idx]

        print(
            f"  📦 Processing batch {batch_num + 1}/{total_batches} ({len(batch)} documents)..."
        )

        try:
            # Use insert_many with ordered=False to continue even if some documents fail
            result = collection.insert_many(batch, ordered=False)
            batch_inserted = len(result.inserted_ids)
            inserted_count += batch_inserted

            print(
                f"    ✅ Batch {batch_num + 1} completed: {batch_inserted}/{len(batch)} inserted"
            )

        except BulkWriteError as bwe:
            # Handle partial success/failure
            batch_inserted = bwe.details.get("nInserted", 0)
            inserted_count += batch_inserted
            write_errors = bwe.details.get("writeErrors", [])

            # Check if this is a time-series mixed schema error
            timeseries_schema_errors = [
                error
                for error in write_errors
                if "timeseriesBucketsMayHaveMixedSchemaData" in error.get("errmsg", "")
            ]

            if timeseries_schema_errors and not timeseries_fix_attempted:
                print(
                    f"    🔧 Detected time-series collection mixed schema issue in batch {batch_num + 1}!"
                )
                print(f"       Attempting to fix automatically...")
                timeseries_fix_attempted = True

                try:
                    # Try to enable mixed schema data for time-series collection
                    fix_result = db_manager.database.command(
                        {
                            "collMod": config.mongodb_summarized_collection,
                            "timeseriesBucketsMayHaveMixedSchemaData": True,
                        }
                    )

                    if fix_result.get("ok") == 1:
                        print(
                            f"    ✅ Successfully enabled mixed schema data for time-series collection"
                        )
                        print(f"    🔄 Retrying batch {batch_num + 1}...")

                        # Retry the batch
                        try:
                            retry_result = collection.insert_many(batch, ordered=False)
                            retry_inserted = len(retry_result.inserted_ids)
                            # Adjust counts (subtract the partial success, add the retry success)
                            inserted_count = (
                                inserted_count - batch_inserted + retry_inserted
                            )
                            print(
                                f"    ✅ Retry successful! Inserted {retry_inserted}/{len(batch)} documents"
                            )

                        except Exception as retry_error:
                            print(f"    ❌ Retry failed: {retry_error}")
                            failed_count += len(write_errors)
                    else:
                        print(f"    ❌ Failed to modify collection settings")
                        failed_count += len(write_errors)

                except Exception as fix_error:
                    print(
                        f"    ❌ Could not automatically fix time-series collection: {fix_error}"
                    )
                    print(f"")
                    print(f"📋 Manual fix required:")
                    print(f"   Run this command in MongoDB shell:")
                    print(f"   db.runCommand({{")
                    print(f'     collMod: "{config.mongodb_summarized_collection}",')
                    print(f"     timeseriesBucketsMayHaveMixedSchemaData: true")
                    print(f"   }});")
                    print(f"")
                    failed_count += len(write_errors)
            else:
                print(f"    ⚠️ Batch {batch_num + 1} completed with errors:")
                print(f"      Successfully inserted: {batch_inserted}/{len(batch)}")
                print(f"      Failed inserts: {len(write_errors)}")

                # Show first few errors for debugging (only for first batch with errors)
                if batch_num == 0 or len(write_errors) <= 3:
                    for i, error in enumerate(write_errors[:3]):
                        print(
                            f"        Error {i+1}: {error.get('errmsg', 'Unknown error')}"
                        )

                    if len(write_errors) > 3:
                        print(f"        ... and {len(write_errors) - 3} more errors")
                else:
                    print(
                        f"        (Error details suppressed - similar to previous batches)"
                    )

                failed_count += len(write_errors)

        except Exception as e:
            print(f"    ❌ Batch {batch_num + 1} failed completely: {e}")
            failed_count += len(batch)

    print(f"\n✅ MongoDB upload complete!")
    print(f"  Successfully inserted: {inserted_count}")
    print(f"  Failed/skipped insertions: {failed_count}")
    print(f"  Total processed: {len(complete_logs)}")

    return inserted_count > 0


def show_orphaned_logs_preview_and_confirm(orphaned_logs, auto_confirm=False):
    """
    Show a preview of orphaned logs to be deleted and ask for user confirmation.

    Args:
        orphaned_logs: List of orphaned log documents from summarized collection
        auto_confirm: If True, skip confirmation and proceed automatically

    Returns:
        bool: True if user confirms or auto_confirm is True, False if user cancels
    """
    print(f"\n{'='*60}")
    print(f"🗑️  ORPHANED LOGS CLEANUP PREVIEW")
    print(f"{'='*60}")
    print(f"Total orphaned logs to delete: {len(orphaned_logs)}")

    if not orphaned_logs:
        print("✅ No orphaned logs found. Nothing to delete!")
        return False

    # Show timestamp range if available
    timestamps = []
    for log in orphaned_logs:
        if log.get("timestamp"):
            timestamps.append(log["timestamp"])

    if timestamps:
        timestamps.sort()
        print(f"Timestamp range: {timestamps[0]} to {timestamps[-1]}")

    # Show sample log IDs and details
    print(f"\nSample orphaned logs (these will be DELETED):")
    sample_size = min(10, len(orphaned_logs))  # Show up to 10 sample logs

    for i in range(sample_size):
        log = orphaned_logs[i]
        log_id = str(log["_id"])
        timestamp = log.get("timestamp", "No timestamp")

        # Show summary info if available
        summary_info = ""
        if "summary" in log:
            summary_preview = (
                log["summary"][:50] + "..."
                if len(log["summary"]) > 50
                else log["summary"]
            )
            summary_info = f" | Summary: {summary_preview}"

        # Show model info if available
        model_info = ""
        if "summary_model" in log:
            model_info = f" | Model: {log['summary_model']}"

        print(f"  {i+1:2d}. ID: {log_id}")
        print(f"      Timestamp: {timestamp}{model_info}{summary_info}")

    if len(orphaned_logs) > sample_size:
        print(f"  ... and {len(orphaned_logs) - sample_size} more orphaned logs")

    print(f"\n{'='*60}")
    print(
        f"⚠️  WARNING: This will PERMANENTLY DELETE {len(orphaned_logs)} logs from the summarized collection!"
    )
    print(
        f"   These logs exist in summarized collection but have no corresponding entry in main collection."
    )
    print(f"   This operation cannot be undone.")

    # Auto-confirm if requested
    if auto_confirm:
        print(f"✅ Auto-confirming deletion (--yes flag provided)")
        return True

    # Ask for user confirmation
    while True:
        try:
            response = (
                input(
                    f"\nProceed with DELETION of {len(orphaned_logs)} orphaned logs? [y/N]: "
                )
                .strip()
                .lower()
            )
            if response in ["y", "yes"]:
                print(f"✅ Confirmed. Starting deletion...")
                return True
            elif response in ["n", "no", ""]:
                print(f"❌ Deletion cancelled by user.")
                return False
            else:
                print(f"Please enter 'y' for yes or 'n' for no.")
        except KeyboardInterrupt:
            print(f"\n❌ Deletion cancelled by user (Ctrl+C).")
            return False
        except EOFError:
            print(f"\n❌ Deletion cancelled.")
            return False


def delete_orphaned_logs(orphaned_logs, db_manager, config):
    """
    Delete orphaned logs from the summarized collection.

    Args:
        orphaned_logs: List of orphaned log documents to delete
        db_manager: DatabaseManager instance
        config: Environment configuration

    Returns:
        bool: True if deletion was successful, False otherwise
    """
    if not orphaned_logs:
        print("✅ No orphaned logs to delete.")
        return True

    print(
        f"🗑️  Deleting {len(orphaned_logs)} orphaned logs from summarized collection..."
    )

    try:
        # Extract IDs to delete
        ids_to_delete = [log["_id"] for log in orphaned_logs]

        # Perform bulk deletion
        summarized_collection = db_manager.database[
            config.mongodb_summarized_collection
        ]

        # Delete in batches to avoid memory issues with very large deletions
        batch_size = 1000
        total_deleted = 0
        total_batches = (len(ids_to_delete) + batch_size - 1) // batch_size

        for batch_num in range(total_batches):
            start_idx = batch_num * batch_size
            end_idx = min(start_idx + batch_size, len(ids_to_delete))
            batch_ids = ids_to_delete[start_idx:end_idx]

            print(
                f"  📦 Deleting batch {batch_num + 1}/{total_batches} ({len(batch_ids)} documents)..."
            )

            # Delete batch
            result = summarized_collection.delete_many({"_id": {"$in": batch_ids}})
            batch_deleted = result.deleted_count
            total_deleted += batch_deleted

            print(
                f"    ✅ Batch {batch_num + 1} completed: {batch_deleted}/{len(batch_ids)} deleted"
            )

        print(f"\n✅ Orphaned logs deletion complete!")
        print(f"  Successfully deleted: {total_deleted}")
        print(f"  Expected to delete: {len(orphaned_logs)}")

        if total_deleted == len(orphaned_logs):
            print(f"✅ All orphaned logs were successfully deleted!")
            return True
        else:
            print(
                f"⚠️  Warning: Expected to delete {len(orphaned_logs)} but deleted {total_deleted}"
            )
            return total_deleted > 0

    except Exception as e:
        print(f"❌ Error deleting orphaned logs: {e}")
        import traceback

        traceback.print_exc()
        return False


def process_online_inference(logs, prompt_content, model_name, config):
    """Process logs using online inference instead of batch."""
    print(f"\nProcessing {len(logs)} logs using online inference...")
    print(f"Model: {model_name}")
    print("=" * 50)

    # Initialize the model
    model = GenerativeModel(model_name)
    complete_logs_with_summaries = []

    # Create a unique identifier for this online processing session
    online_session_id = f"online_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

    for i, log in enumerate(logs, 1):
        print(f"Processing log {i}/{len(logs)}...")

        try:
            # Create the full prompt
            full_prompt = f"{prompt_content}\n\nLog Details:\n{json.dumps(log, default=json_converter)}"

            # Generate response
            response = call_vertex_ai_with_retry(model, full_prompt)

            # Extract the text response
            if response.text:
                # Create complete log with summary
                complete_log = log.copy()
                complete_log["summary"] = response.text.strip()
                complete_log["summary_generated_at"] = datetime.utcnow().isoformat()
                complete_log["summary_model"] = model_name
                complete_log["summary_batch_job_id"] = online_session_id
                complete_logs_with_summaries.append(complete_log)

                print(f"✅ Processed log {i}: {len(response.text)} characters")
            else:
                print(f"❌ No response for log {i}")

        except Exception as e:
            print(f"❌ Error processing log {i}: {e}")

        # Small delay to avoid rate limiting
        if i < len(logs):
            time.sleep(1)

    # Save and upload results
    if complete_logs_with_summaries:
        # Ensure output directory exists
        output_dir = ensure_output_directory()

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_filename = os.path.join(
            output_dir, f"complete_logs_with_summaries_online_{timestamp}.jsonl"
        )

        # Save complete logs with summaries
        with open(results_filename, "w") as f:
            for log in complete_logs_with_summaries:
                f.write(json.dumps(log, default=json_converter) + "\n")

        print("\n" + "=" * 50)
        print("Online inference completed!")
        print(f"  Processed: {len(complete_logs_with_summaries)}/{len(logs)} logs")
        print(f"  Results saved to: {results_filename}")
        print(f"  Session ID: {online_session_id}")
        print("=" * 50)

        # Upload to MongoDB
        success = upload_to_mongodb(results_filename, config)
        if success:
            print("✅ Successfully uploaded to MongoDB!")
        else:
            print(f"❌ Failed to upload to MongoDB. Manual upload needed.")

    else:
        print("\n❌ No results generated.")


def ensure_output_directory():
    """Ensure the summarization_outputs directory exists."""
    output_dir = "summarization_outputs"
    os.makedirs(output_dir, exist_ok=True)
    return output_dir


def parse_ignore_recent(ignore_recent_str):
    """
    Parse ignore_recent string into a timedelta object.

    Args:
        ignore_recent_str: String like '30m', '2h', '1d'

    Returns:
        timedelta object or None if invalid
    """
    if not ignore_recent_str:
        return None

    import re

    # Match pattern like '30m', '2h', '1d'
    match = re.match(r"^(\d+)([mhd])$", ignore_recent_str.lower())
    if not match:
        raise ValueError(
            f"Invalid ignore_recent format: {ignore_recent_str}. Use format like '30m', '2h', '1d'"
        )

    value = int(match.group(1))
    unit = match.group(2)

    if unit == "m":
        return timedelta(minutes=value)
    elif unit == "h":
        return timedelta(hours=value)
    elif unit == "d":
        return timedelta(days=value)
    else:
        raise ValueError(f"Invalid time unit: {unit}. Use 'm', 'h', or 'd'")


def show_preview_and_confirm(logs, processing_mode, model_name, auto_confirm=False):
    """
    Show a preview of logs to be processed and ask for user confirmation.

    Args:
        logs: List of log documents to be processed
        processing_mode: String describing the processing mode (e.g., "missing logs", "unprocessed logs")
        model_name: Name of the AI model to be used
        auto_confirm: If True, skip confirmation and proceed automatically

    Returns:
        bool: True if user confirms or auto_confirm is True, False if user cancels
    """
    print(f"\n{'='*60}")
    print(f"📋 PROCESSING PREVIEW")
    print(f"{'='*60}")
    print(f"Processing mode: {processing_mode}")
    print(f"Model: {model_name}")
    print(f"Total logs to process: {len(logs)}")

    if logs:
        # Show timestamp range if available
        timestamps = []
        for log in logs:
            if log.get("timestamp"):
                timestamps.append(log["timestamp"])

        if timestamps:
            timestamps.sort()
            print(f"Timestamp range: {timestamps[0]} to {timestamps[-1]}")

    # Show sample log IDs
    print(f"\nSample log IDs to be processed:")
    sample_size = min(10, len(logs))  # Show up to 10 sample IDs

    for i in range(sample_size):
        log = logs[i]
        log_id = str(log["_id"])
        timestamp = log.get("timestamp", "No timestamp")

        # Show some context about the log
        context_info = ""
        if "context" in log:
            app_id = log["context"].get("application_id", "")
            if app_id:
                context_info = (
                    f" (app: {app_id[:12]}...)"
                    if len(app_id) > 12
                    else f" (app: {app_id})"
                )

        level = log.get("level", "")
        level_info = f" [{level}]" if level else ""

        print(f"  {i+1:2d}. {log_id}{level_info}{context_info}")
        print(f"      Timestamp: {timestamp}")

    if len(logs) > sample_size:
        print(f"  ... and {len(logs) - sample_size} more logs")

    print(f"\n{'='*60}")

    # Auto-confirm if requested
    if auto_confirm:
        print(f"✅ Auto-confirming processing (--yes flag provided)")
        return True

    # Ask for user confirmation
    print(f"⚠️  This will process {len(logs)} logs using the {model_name} model.")
    print(f"   Processing mode: {processing_mode}")

    while True:
        try:
            response = input(f"\nProceed with processing? [y/N]: ").strip().lower()
            if response in ["y", "yes"]:
                print(f"✅ Confirmed. Starting processing...")
                return True
            elif response in ["n", "no", ""]:
                print(f"❌ Processing cancelled by user.")
                return False
            else:
                print(f"Please enter 'y' for yes or 'n' for no.")
        except KeyboardInterrupt:
            print(f"\n❌ Processing cancelled by user (Ctrl+C).")
            return False
        except EOFError:
            print(f"\n❌ Processing cancelled.")
            return False


def main():
    """
    Main function for comprehensive log summarization pipeline.

    Supports multiple modes:
    1. Default mode: Find logs newer than the latest processed timestamp
    2. Missing ID mode (--find-missing): Find all log IDs present in main collection
       but missing from summarized collection, regardless of timestamp
    3. Cleanup mode (--cleanup-orphaned): Find and delete orphaned logs in summarized
       collection that don't have corresponding entries in main collection
    """
    parser = argparse.ArgumentParser(
        description="Comprehensive log summarization pipeline with resume capability and cleanup tools."
    )
    parser.add_argument(
        "--stage",
        choices=["submit", "check", "upload", "full"],
        default="full",
        help="Stage to execute: submit (job only), check (status and download), upload (to MongoDB), or full (all stages).",
    )
    parser.add_argument(
        "--job-resource-name",
        help="Job resource name for resuming from check or upload stage.",
    )
    parser.add_argument(
        "--job-info-file", help="Job info file for resuming from check or upload stage."
    )
    parser.add_argument("--results-file", help="Results file for upload stage.")
    parser.add_argument("--prompt-file", help="Path to custom prompt file (optional).")
    parser.add_argument(
        "--model",
        help="Model name (defaults to SUMMARIZATION_MODEL from config).",
    )
    parser.add_argument(
        "--filter",
        nargs="*",
        help="Key-value pairs for filtering logs (e.g., level=error).",
        default=[],
    )
    parser.add_argument(
        "--online",
        action="store_true",
        help="Use online inference instead of batch processing.",
    )
    parser.add_argument(
        "--limit",
        type=int,
        help="Limit the number of logs to process (useful for testing).",
    )
    parser.add_argument(
        "--find-missing",
        action="store_true",
        help="Find and process log-ids that exist in main collection but are missing from summarized collection (ignores timestamp ordering).",
    )
    parser.add_argument(
        "--yes",
        action="store_true",
        help="Automatically confirm processing without showing preview and asking for confirmation.",
    )
    parser.add_argument(
        "--cleanup-orphaned",
        action="store_true",
        help="Find and delete orphaned logs in summarized collection (logs that don't exist in main collection).",
    )
    parser.add_argument(
        "--ignore-recent",
        help="Ignore logs from the last N minutes/hours/days when finding missing logs (e.g., '30m', '2h', '1d').",
    )
    args = parser.parse_args()

    # Configuration
    print("Loading configuration...")
    env_name = os.getenv("ENV", "local")
    config = EnvConfig.load(f".env.{env_name}")

    if args.stage in ["check", "upload"] and not args.online:
        aiplatform.init(project=config.project_id, location=config.gcp_location)

    if args.stage != "upload":
        vertexai.init(project=config.project_id, location=config.gcp_location)

    # Handle different stages
    if args.stage == "upload":
        # Upload stage only
        if not args.results_file:
            print("❌ --results-file required for upload stage")
            return

        if not os.path.exists(args.results_file):
            print(f"❌ Results file not found: {args.results_file}")
            return

        success = upload_to_mongodb(args.results_file, config)
        if success:
            print("✅ Upload completed successfully!")
        else:
            print("❌ Upload failed!")
        return

    elif args.stage == "check":
        # Check and download stage only
        if not args.job_resource_name or not args.job_info_file:
            print("❌ --job-resource-name and --job-info-file required for check stage")
            return

        if not os.path.exists(args.job_info_file):
            print(f"❌ Job info file not found: {args.job_info_file}")
            return

        results_file = check_job_status_and_download(
            args.job_resource_name, args.job_info_file, config
        )
        if results_file:
            print(f"✅ Results ready in: {results_file}")
            print(
                f"Next: python -m src.summarize_logs.batch_process_logs --stage upload --results-file {results_file}"
            )
        else:
            print("❌ Failed to download results!")
        return

    # Handle orphaned logs cleanup mode
    if args.cleanup_orphaned:
        db_manager = DatabaseManager(config)

        print("\n🗑️  ORPHANED LOGS CLEANUP MODE")
        print(
            "Finding logs in summarized collection that don't exist in main collection..."
        )

        # Find orphaned logs
        orphaned_logs = get_orphaned_logs_in_summarized(db_manager, config)

        if not orphaned_logs:
            print("✅ No orphaned logs found. Summarized collection is clean!")
            return

        # Show preview and confirm deletion
        confirmed = show_orphaned_logs_preview_and_confirm(
            orphaned_logs, auto_confirm=args.yes
        )

        if not confirmed:
            print("🛑 Cleanup aborted by user.")
            return

        # Perform deletion
        success = delete_orphaned_logs(orphaned_logs, db_manager, config)

        if success:
            print("🎉 Orphaned logs cleanup completed successfully!")
        else:
            print("❌ Orphaned logs cleanup failed!")

        return

    # For submit and full stages, we need to fetch logs and set up processing
    db_manager = DatabaseManager(config)

    print(
        f"\n🔍 Log processing mode: {'Find missing log IDs' if args.find_missing else 'Process logs newer than latest timestamp'}"
    )
    if args.find_missing:
        print(
            "   This will find all log IDs in main collection that are missing from summarized collection"
        )
    else:
        print("   This will process logs newer than the latest processed timestamp")

    # Parse filters
    log_filter = {}
    for f in args.filter:
        key, value = f.split("=", 1)
        if key == "_id":
            log_filter[key] = ObjectId(value)
        else:
            try:
                value = int(value)
            except ValueError:
                try:
                    value = float(value)
                except ValueError:
                    pass
            log_filter[key] = value

    # Parse ignore_recent argument
    ignore_recent_timedelta = None
    if args.ignore_recent:
        if not args.find_missing:
            print("❌ --ignore-recent can only be used with --find-missing")
            return
        try:
            ignore_recent_timedelta = parse_ignore_recent(args.ignore_recent)
            print(f"Will ignore logs from the last {args.ignore_recent}")
        except ValueError as e:
            print(f"❌ Error parsing --ignore-recent: {e}")
            return

    # Fetch logs based on mode
    if args.find_missing:
        print("Fetching missing logs by ID comparison...")
        logs = get_missing_logs_by_id(
            db_manager, config, log_filter, args.limit, ignore_recent_timedelta
        )
    else:
        print("Fetching unprocessed logs by timestamp...")
        logs = get_unprocessed_logs(db_manager, config, log_filter, args.limit)

    if not logs:
        if args.find_missing:
            print(
                "✅ No missing logs found. All log IDs are present in the summarized collection!"
            )
        else:
            print("✅ No unprocessed logs found. All logs are up to date!")
        return

    # Load prompt
    prompt_manager = PromptManager()
    if args.prompt_file:
        print(f"Loading prompt from: {args.prompt_file}")
        try:
            with open(args.prompt_file, "r") as f:
                prompt_content = f.read()
        except FileNotFoundError:
            print(f"❌ Prompt file not found: {args.prompt_file}")
            return
    else:
        prompt_content = prompt_manager.get_log_summarization_prompt()

    # Use configurable model name
    model_name = args.model or config.summarization_model
    print(f"Using model: {model_name}")

    # Show preview and get user confirmation (unless auto-confirmed)
    processing_mode = "missing logs" if args.find_missing else "unprocessed logs"
    processing_type = "online" if args.online else "batch"

    confirmed = show_preview_and_confirm(
        logs,
        f"{processing_mode} ({processing_type} processing)",
        model_name,
        auto_confirm=args.yes,
    )

    if not confirmed:
        print("🛑 Processing aborted by user.")
        return

    print(
        f"\n📊 Processing {len(logs)} {processing_mode} using {processing_type} mode..."
    )

    if args.online:
        # Online processing - does everything in one go
        process_online_inference(logs, prompt_content, model_name, config)

    elif args.stage == "submit":
        # Submit job only
        job_resource_name, job_info_file = submit_batch_job(
            logs, prompt_content, model_name, config
        )
        if job_resource_name:
            print(f"\n🚀 Job submitted successfully!")
            print(
                f"Next: python -m src.summarize_logs.batch_process_logs --stage check --job-resource-name {job_resource_name} --job-info-file {job_info_file}"
            )
        else:
            print("❌ Job submission failed!")

    elif args.stage == "full":
        # Full pipeline
        print("\n🚀 Starting full batch processing pipeline...")

        # Submit job
        job_resource_name, job_info_file = submit_batch_job(
            logs, prompt_content, model_name, config
        )
        if not job_resource_name:
            print("❌ Job submission failed!")
            return

        # Check status and download
        results_file = check_job_status_and_download(
            job_resource_name, job_info_file, config
        )
        if not results_file:
            print("❌ Failed to download results!")
            return

        # Upload to MongoDB
        success = upload_to_mongodb(results_file, config)
        if success:
            print("\n🎉 Full pipeline completed successfully!")

            # Cleanup
            try:
                os.remove(job_info_file)
                os.remove(results_file)
                print("✅ Temporary files cleaned up")
            except OSError:
                pass
        else:
            print("❌ Upload to MongoDB failed!")


if __name__ == "__main__":
    main()
