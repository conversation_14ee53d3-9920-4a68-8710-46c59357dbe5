"""
MongoDB logging handler for session-specific log storage.
Separated from logging_utils to avoid circular imports.
"""

import logging
import sys
import traceback


class MongoDBLogHandler(logging.Handler):
    """Custom logging handler that stores session logs in MongoDB."""

    def __init__(self, session_id=None, run_id=None):
        super().__init__()
        self.session_id = session_id
        self.run_id = run_id
        self.db_manager = None

    def set_session_context(self, session_id, run_id=None):
        """Update the session context for this handler."""
        self.session_id = session_id
        self.run_id = run_id

    def emit(self, record):
        """Emit a log record to MongoDB."""
        if not self.session_id:
            return  # No session context, skip MongoDB storage

        try:
            # Initialize database manager if needed (lazy import to avoid circular imports)
            if not self.db_manager:
                from .database_manager import DatabaseManager
                from .env_config import EnvConfig

                config = EnvConfig.load()
                self.db_manager = DatabaseManager(config)

            # Format the record
            log_entry = {
                "levelname": record.levelname,
                "message": record.getMessage(),
                "timestamp": record.created,
                "module": record.module,
                "funcName": record.funcName,
                "lineno": record.lineno,
            }

            # Add extra details if available
            if hasattr(record, "details"):
                # Convert protobuf objects to MongoDB-compatible format
                from .logging_utils import LoggingUtils

                log_entry["details"] = LoggingUtils.convert_protobuf_to_dict(
                    record.details
                )

            # Add traceback if it's an error
            if record.exc_info:
                log_entry["traceback"] = traceback.format_exception(*record.exc_info)

            # Store in MongoDB
            self.db_manager.store_session_log(
                session_id=self.session_id, log_entry=log_entry, run_id=self.run_id
            )

        except Exception as e:
            # Don't let logging errors break the application
            # Just log to stderr if possible
            try:
                print(f"MongoDB logging handler error: {e}", file=sys.stderr)
            except:
                pass

    @staticmethod
    def get_session_handler(session_id, run_id=None):
        """Get or create a MongoDB log handler for a specific session."""
        handler = MongoDBLogHandler(session_id, run_id)
        handler.setLevel(logging.INFO)  # Capture INFO and above
        return handler
