"""
Setup script for MongoDB indexes required by the memory system.
Based on the implementation plan specifications.
"""

import logging

from ..database_manager import DatabaseManager
from ..env_config import EnvConfig


def setup_memory_indexes(config: EnvConfig):
    """Setup all required MongoDB indexes for the memory system."""
    try:
        db_manager = DatabaseManager(config)
        database = db_manager.database
        memory_collection = database["memory"]

        logging.info("Setting up memory system indexes...")

        # Core indexes for memory collection
        memory_collection.create_index("memory_id", unique=True)
        memory_collection.create_index("type")
        memory_collection.create_index("tags")
        memory_collection.create_index("created_by")
        memory_collection.create_index("updated_at")
        memory_collection.create_index("version")
        memory_collection.create_index("is_latest_version")
        memory_collection.create_index("parent_memory_id")
        memory_collection.create_index("confidence_score")

        # Text search indexes
        memory_collection.create_index([("canonical_question", "text")])
        memory_collection.create_index([("title", "text")])
        memory_collection.create_index([("body", "text")])

        # Field-specific indexes
        memory_collection.create_index("field_type")
        memory_collection.create_index("aliases")

        # Compound indexes for common query patterns
        memory_collection.create_index([("type", 1), ("is_latest_version", 1)])
        memory_collection.create_index([("type", 1), ("tags", 1)])
        memory_collection.create_index([("field_type", 1), ("is_latest_version", 1)])
        memory_collection.create_index([("created_by", 1), ("updated_at", -1)])
        memory_collection.create_index(
            [("confidence_score", -1), ("is_latest_version", 1)]
        )

        # Procedural memory specific indexes
        memory_collection.create_index([("type", 1), ("usage_count", -1)])
        memory_collection.create_index([("type", 1), ("acceptance_count", -1)])

        # Version control indexes
        memory_collection.create_index([("parent_memory_id", 1), ("version", -1)])

        # Performance indexes for similarity search
        memory_collection.create_index([("type", 1), ("embedding", 1)])

        logging.info("Memory system indexes created successfully")

        # Setup feedback collection indexes (for future Phase 3)
        feedback_collection = database["memory_feedback"]
        feedback_collection.create_index("memory_id")
        feedback_collection.create_index("user_id")
        feedback_collection.create_index("feedback_type")
        feedback_collection.create_index("timestamp")
        feedback_collection.create_index([("memory_id", 1), ("timestamp", -1)])

        logging.info("Memory feedback indexes created successfully")

        return True

    except Exception as e:
        logging.exception(f"Failed to setup memory indexes: {e}")
        return False


def verify_indexes(config: EnvConfig):
    """Verify that all required indexes exist."""
    try:
        db_manager = DatabaseManager(config)
        database = db_manager.database
        memory_collection = database["memory"]

        indexes = memory_collection.list_indexes()
        index_names = [idx["name"] for idx in indexes]

        required_indexes = [
            "memory_id_1",
            "type_1",
            "tags_1",
            "created_by_1",
            "updated_at_1",
            "version_1",
            "is_latest_version_1",
            "field_type_1",
            "aliases_1",
        ]

        missing_indexes = [idx for idx in required_indexes if idx not in index_names]

        if missing_indexes:
            logging.warning(f"Missing indexes: {missing_indexes}")
            return False

        logging.info("All required memory indexes verified")
        return True

    except Exception as e:
        logging.exception(f"Failed to verify memory indexes: {e}")
        return False


if __name__ == "__main__":
    # Setup logging
    logging.basicConfig(level=logging.INFO)

    # Load config
    config = EnvConfig.load()

    # Setup indexes
    success = setup_memory_indexes(config)

    if success:
        # Verify indexes
        verify_indexes(config)
        print("Memory system indexes setup completed successfully")
    else:
        print("Failed to setup memory system indexes")
        exit(1)
