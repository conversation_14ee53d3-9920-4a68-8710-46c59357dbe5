"""
Embedding service for the Alfred memory system.
Provides semantic search capabilities using Google's embedding model.
"""

import logging
import math
import time
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Tuple

from ..client_managers import get_genai_manager
from ..retry_utils import call_genai_with_retry


@dataclass
class EmbeddingResult:
    """Result of embedding generation."""

    embedding: List[float]
    text: str
    model: str
    generation_time_ms: float
    token_count: Optional[int] = None


class EmbeddingService:
    """Service for generating and comparing text embeddings."""

    def __init__(self, config):
        """Initialize embedding service with configuration."""
        self.config = config
        self.genai_manager = get_genai_manager(config)
        self.model_name = config.embedding_model
        self.embedding_dimension = config.embedding_dimension
        self.similarity_threshold = 0.7

        logging.info(
            f"Initialized embedding service with model: {self.model_name}, "
            f"dimension: {self.embedding_dimension}"
        )

    def generate_embedding(self, text: str) -> EmbeddingResult:
        """Generate embedding for given text using configured embedding model."""
        if not text or not text.strip():
            raise ValueError("Text cannot be empty")

        start_time = time.time()

        try:
            # Get GenAI client
            client = self.genai_manager.get_client()

            # Generate embedding using retry wrapper
            result = self._generate_embedding_with_retry(client, text)

            generation_time_ms = (time.time() - start_time) * 1000

            # Extract embedding values - response is a list of embeddings
            embedding_values = result.embeddings[0].values

            # Log embedding generation
            logging.info(
                "Generated embedding for text",
                extra={
                    "details": {
                        "text_length": len(text),
                        "embedding_dimension": len(embedding_values),
                        "model": self.model_name,
                        "generation_time_ms": generation_time_ms,
                        "text_preview": text[:100] + "..." if len(text) > 100 else text,
                    }
                },
            )

            return EmbeddingResult(
                embedding=embedding_values,
                text=text,
                model=self.model_name,
                generation_time_ms=generation_time_ms,
            )

        except Exception as e:
            logging.exception(
                f"Failed to generate embedding for text: {e}",
                extra={
                    "details": {
                        "text_length": len(text),
                        "model": self.model_name,
                        "error": str(e),
                    }
                },
            )
            raise

    def _generate_embedding_with_retry(self, client, text: str):
        """Generate embedding with retry logic."""

        def api_call():
            # Use the embed_content method with the configured model
            return client.models.embed_content(model=self.model_name, contents=[text])

        return call_genai_with_retry(
            api_call=api_call, max_retries=3, base_delay=1.0, max_delay=10.0
        )

    def calculate_cosine_similarity(
        self, embedding1: List[float], embedding2: List[float]
    ) -> float:
        """Calculate cosine similarity between two embeddings."""
        if len(embedding1) != len(embedding2):
            raise ValueError(
                f"Embedding dimensions must match: {len(embedding1)} != {len(embedding2)}"
            )

        # Calculate dot product
        dot_product = sum(a * b for a, b in zip(embedding1, embedding2))

        # Calculate magnitudes
        magnitude1 = math.sqrt(sum(a * a for a in embedding1))
        magnitude2 = math.sqrt(sum(a * a for a in embedding2))

        # Avoid division by zero
        if magnitude1 == 0 or magnitude2 == 0:
            return 0.0

        # Calculate cosine similarity
        similarity = dot_product / (magnitude1 * magnitude2)
        return similarity

    def find_similar_memories(
        self,
        query_embedding: List[float],
        memory_embeddings: List[Tuple[str, List[float]]],
        threshold: Optional[float] = None,
    ) -> List[Tuple[str, float]]:
        """Find memories with embeddings similar to query embedding."""
        if threshold is None:
            threshold = self.similarity_threshold

        similar_memories = []

        for memory_id, memory_embedding in memory_embeddings:
            try:
                similarity = self.calculate_cosine_similarity(
                    query_embedding, memory_embedding
                )

                if similarity >= threshold:
                    similar_memories.append((memory_id, similarity))

            except Exception as e:
                logging.exception(
                    f"Error calculating similarity for memory {memory_id}: {e}",
                    extra={"details": {"memory_id": memory_id, "error": str(e)}},
                )
                continue

        # Sort by similarity score (descending)
        similar_memories.sort(key=lambda x: x[1], reverse=True)

        return similar_memories

    def batch_generate_embeddings(self, texts: List[str]) -> List[EmbeddingResult]:
        """Generate embeddings for multiple texts."""
        results = []

        for i, text in enumerate(texts):
            try:
                result = self.generate_embedding(text)
                results.append(result)

                # Log progress for large batches
                if i > 0 and i % 10 == 0:
                    logging.info(
                        f"Generated embeddings for {i}/{len(texts)} texts",
                        extra={"details": {"batch_size": len(texts), "completed": i}},
                    )

            except Exception as e:
                logging.exception(
                    f"Failed to generate embedding for text {i}: {e}",
                    extra={"details": {"text_index": i, "error": str(e)}},
                )
                # Continue with other texts even if one fails
                continue

        logging.info(
            f"Batch embedding generation completed: {len(results)}/{len(texts)} successful",
            extra={
                "details": {
                    "total_texts": len(texts),
                    "successful": len(results),
                    "failed": len(texts) - len(results),
                }
            },
        )

        return results

    def update_similarity_threshold(self, threshold: float):
        """Update the similarity threshold for memory matching."""
        if not 0.0 <= threshold <= 1.0:
            raise ValueError("Similarity threshold must be between 0.0 and 1.0")

        old_threshold = self.similarity_threshold
        self.similarity_threshold = threshold

        logging.info(
            f"Updated similarity threshold: {old_threshold} -> {threshold}",
            extra={
                "details": {"old_threshold": old_threshold, "new_threshold": threshold}
            },
        )

    def get_embedding_stats(self) -> Dict[str, Any]:
        """Get statistics about the embedding service."""
        return {
            "model_name": self.model_name,
            "embedding_dimension": self.embedding_dimension,
            "similarity_threshold": self.similarity_threshold,
            "client_available": self.genai_manager.is_available(),
        }

    def validate_embedding(self, embedding: List[float]) -> bool:
        """Validate that an embedding has the correct format and dimension."""
        if not isinstance(embedding, list):
            return False

        if len(embedding) != self.embedding_dimension:
            return False

        # Check that all values are numbers
        try:
            for value in embedding:
                if not isinstance(value, (int, float)):
                    return False

                # Check for NaN or infinity
                if math.isnan(value) or math.isinf(value):
                    return False

        except Exception:
            return False

        return True
