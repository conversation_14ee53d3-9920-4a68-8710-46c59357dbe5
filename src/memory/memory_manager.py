"""
Memory Manager for the Alfred memory system.
Provides CRUD operations and semantic search for all memory types.
"""

import logging
from typing import Any, Dict, List, Optional

from ..database_manager import DatabaseManager
from .embedding_service import EmbeddingService
from .memory_models import (
    AgentGuidanceMemory,
    DomainWorkflowMemory,
    FieldKnowledgeMemory,
    MemoryUnit,
    TranslationMappingMemory,
    create_memory_from_dict,
)


class MemoryStore:
    """Interface for memory storage operations."""

    def search(
        self, query: str, type_filter: Optional[str] = None, top_k: int = 10
    ) -> List[Dict[str, Any]]:
        """Search memories using semantic similarity."""
        raise NotImplementedError

    def get(self, memory_id: str) -> Optional[MemoryUnit]:
        """Retrieve specific memory unit by ID."""
        raise NotImplementedError

    def create(self, memory: MemoryUnit) -> str:
        """Store new memory unit."""
        raise NotImplementedError

    def update(self, memory_id: str, changes: Dict[str, Any], user: str) -> bool:
        """Update existing memory with version tracking."""
        raise NotImplementedError

    def rollback(self, memory_id: str, to_version: int) -> bool:
        """Rollback to previous version."""
        raise NotImplementedError


class MemoryManager(MemoryStore):
    """Core memory management class implementing the MemoryStore interface."""

    def __init__(self, config):
        """Initialize memory manager with database and embedding service."""
        self.config = config
        self.db_manager = DatabaseManager(config)
        self.embedding_service = EmbeddingService(config)
        self.memory_collection = "memory"

        # Ensure indexes exist
        self.ensure_indexes()

        logging.info("MemoryManager initialized successfully")

    def ensure_indexes(self):
        """Create MongoDB indexes for efficient memory operations."""
        try:
            database = self.db_manager.database
            collection = database[self.memory_collection]

            # Core indexes (_id is automatically indexed by MongoDB)
            collection.create_index("type")
            collection.create_index("tags")
            collection.create_index("created_by")
            collection.create_index("updated_at")
            collection.create_index("version")
            collection.create_index("is_latest_version")
            collection.create_index("parent_id")

            # Combined text search index (MongoDB only allows one text index per collection)
            try:
                collection.create_index(
                    [
                        ("canonical_question", "text"),
                        ("title", "text"),
                        ("body", "text"),
                    ]
                )
            except Exception as e:
                # If index already exists, ignore the error
                if "already exists" in str(e) or "IndexOptionsConflict" in str(e):
                    pass
                else:
                    raise

            # Field-specific indexes
            collection.create_index("field_type")
            collection.create_index("aliases")
            collection.create_index("confidence_score")

            # Compound indexes for common queries
            collection.create_index([("type", 1), ("is_latest_version", 1)])
            collection.create_index([("type", 1), ("tags", 1)])
            collection.create_index([("field_type", 1), ("is_latest_version", 1)])

            # Vector search index for embeddings (Atlas Vector Search)
            self._ensure_vector_search_index(collection)

            logging.info("Memory collection indexes ensured")

        except Exception as e:
            logging.exception(f"Failed to ensure memory indexes: {e}")
            raise

    def _ensure_vector_search_index(self, collection):
        """Create vector search index for semantic similarity search."""
        try:
            # Vector search index definition for Atlas Vector Search
            vector_index_definition = {
                "fields": [
                    {
                        "type": "vector",
                        "path": "embedding",
                        "numDimensions": self.config.embedding_dimension,
                        "similarity": "cosine",
                    },
                    {"type": "filter", "path": "type"},
                    {"type": "filter", "path": "is_latest_version"},
                ]
            }

            # Check if vector search index already exists
            try:
                existing_indexes = list(collection.list_search_indexes())
                vector_index_exists = any(
                    idx.get("name") == "memory_vector_search"
                    for idx in existing_indexes
                )

                if vector_index_exists:
                    logging.info(
                        "Vector search index 'memory_vector_search' found and ready"
                    )
                else:
                    logging.info(
                        "Vector search index 'memory_vector_search' not found - will use traditional search"
                    )

            except Exception as e:
                # Vector search might not be available in all MongoDB deployments
                logging.warning(f"Vector search index check failed: {e}")
                logging.info("Falling back to traditional similarity search")

        except Exception as e:
            logging.warning(f"Failed to ensure vector search index: {e}")
            # Don't raise - vector search is optional, we can fall back to cosine similarity

    def search(
        self, query: str, type_filter: Optional[str] = None, top_k: int = 10
    ) -> List[Dict[str, Any]]:
        """Search memories using semantic similarity with vector search or fallback."""
        try:
            # Generate embedding for query
            embedding_result = self.embedding_service.generate_embedding(query)
            query_embedding = embedding_result.embedding

            # Try vector search first (Atlas Vector Search)
            try:
                results = self._vector_search(query_embedding, type_filter, top_k)
                if results:
                    logging.info(f"Vector search completed: {len(results)} results")
                    return results
            except Exception as e:
                logging.warning(
                    f"Vector search failed, falling back to traditional search: {e}"
                )

            # Fallback to traditional similarity search
            results = self._traditional_search(query_embedding, type_filter, top_k)

            logging.info(
                f"Memory search completed: {len(results)} results",
                extra={
                    "details": {
                        "query_length": len(query),
                        "type_filter": type_filter,
                        "top_k": top_k,
                        "results_count": len(results),
                    }
                },
            )

            return results

        except Exception as e:
            logging.exception(f"Memory search failed: {e}")
            raise

    def _vector_search(
        self, query_embedding: List[float], type_filter: Optional[str], top_k: int
    ) -> List[Dict[str, Any]]:
        """Perform vector search using MongoDB Atlas Vector Search with Exact Nearest Neighbor."""
        try:
            database = self.db_manager.database
            collection = database[self.memory_collection]

            # Build the vector search pipeline with exact=true for ENN search
            pipeline = [
                {
                    "$vectorSearch": {
                        "index": "memory_vector_search",
                        "path": "embedding",
                        "queryVector": query_embedding,
                        "limit": top_k,
                        "exact": True,  # Use Exact Nearest Neighbor search
                        "filter": self._build_vector_search_filter(type_filter),
                    }
                },
                {"$addFields": {"similarity_score": {"$meta": "vectorSearchScore"}}},
            ]

            # Execute aggregation pipeline
            search_results = list(collection.aggregate(pipeline))

            # Convert results to expected format
            results = []
            for doc in search_results:
                # Extract and remove similarity_score before creating memory object
                similarity_score = doc.pop("similarity_score")

                # Get full memory data
                memory_data = create_memory_from_dict(doc)
                if memory_data:
                    result = memory_data.to_dict()
                    result["_id"] = str(doc["_id"])
                    result["similarity_score"] = similarity_score
                    results.append(result)

            return results

        except Exception as e:
            logging.warning(f"Vector search execution failed: {e}")
            return []

    def _build_vector_search_filter(self, type_filter: Optional[str]) -> Dict[str, Any]:
        """Build filter for vector search."""
        filter_conditions = {"is_latest_version": True}

        if type_filter:
            filter_conditions["type"] = type_filter

        return filter_conditions

    def _traditional_search(
        self, query_embedding: List[float], type_filter: Optional[str], top_k: int
    ) -> List[Dict[str, Any]]:
        """Fallback to traditional cosine similarity search."""
        try:
            # Build MongoDB filter
            filter_query = {"is_latest_version": True}
            if type_filter:
                filter_query["type"] = type_filter

            # Retrieve all memories with embeddings
            memories = self.db_manager.query_mongodb(
                collection=self.memory_collection,
                query_type="find",
                filter=filter_query,
                projection={
                    "_id": 1,
                    "embedding": 1,
                    "type": 1,
                    "canonical_question": 1,
                    "title": 1,
                },
            )

            # Prepare embeddings for similarity calculation
            memory_embeddings = []
            for memory in memories:
                if memory.get("embedding"):
                    memory_embeddings.append((str(memory["_id"]), memory["embedding"]))

            # Find similar memories
            similar_memories = self.embedding_service.find_similar_memories(
                query_embedding, memory_embeddings
            )

            # Retrieve full memory data for top results
            results = []
            for memory_id, similarity in similar_memories[:top_k]:
                memory_data = self.get(memory_id)
                if memory_data:
                    result = memory_data.to_dict()
                    result["_id"] = memory_id  # Include the MongoDB _id
                    result["similarity_score"] = similarity
                    results.append(result)

            return results

        except Exception as e:
            logging.exception(f"Traditional search failed: {e}")
            return []

    def get(self, memory_id: str) -> Optional[MemoryUnit]:
        """Retrieve specific memory unit by MongoDB _id."""
        try:
            from bson import ObjectId

            # Convert string ID to ObjectId for MongoDB query
            try:
                object_id = ObjectId(memory_id)
            except Exception:
                logging.warning(f"Invalid ObjectId format: {memory_id}")
                return None

            # Get memory by _id
            results = self.db_manager.query_mongodb(
                collection=self.memory_collection,
                query_type="find",
                filter={"_id": object_id},
                limit=1,
            )

            if results:
                return create_memory_from_dict(results[0])

            return None

        except Exception as e:
            logging.exception(f"Failed to retrieve memory {memory_id}: {e}")
            return None

    def create(self, memory: MemoryUnit) -> str:
        """Store new memory unit."""
        try:
            # Generate embedding for the memory
            embedding_text = self._extract_text_for_embedding(memory)
            embedding_result = self.embedding_service.generate_embedding(embedding_text)
            memory.embedding = embedding_result.embedding

            # Store in MongoDB
            memory_dict = memory.to_dict()
            result = self.db_manager.database[self.memory_collection].insert_one(
                memory_dict
            )

            logging.info(
                f"Created memory: {result.inserted_id}",
                extra={
                    "details": {
                        "memory_id": str(result.inserted_id),
                        "type": memory.type,
                        "created_by": memory.created_by,
                        "tags": memory.tags,
                    }
                },
            )

            return str(result.inserted_id)

        except Exception as e:
            logging.exception(f"Failed to create memory: {e}")
            raise

    def update(self, memory_id: str, changes: Dict[str, Any], created_by: str) -> bool:
        """Update existing memory with version tracking."""
        try:
            # Get current memory
            current_memory = self.get(memory_id)
            if not current_memory:
                logging.warning(f"Memory {memory_id} not found for update")
                return False

            # Create new version
            new_version = current_memory.create_new_version(created_by, memory_id)

            # Apply changes
            for key, value in changes.items():
                if hasattr(new_version, key):
                    setattr(new_version, key, value)

            # Regenerate embedding if content changed
            content_fields = ["canonical_question", "title", "body", "answer_plan"]
            if any(field in changes for field in content_fields):
                embedding_text = self._extract_text_for_embedding(new_version)
                embedding_result = self.embedding_service.generate_embedding(
                    embedding_text
                )
                new_version.embedding = embedding_result.embedding

            # Atomic update: mark old version as not latest and insert new version
            database = self.db_manager.database

            from bson import ObjectId

            with database.client.start_session() as session:
                with session.start_transaction():
                    # Mark old version as not latest
                    database[self.memory_collection].update_one(
                        {"_id": ObjectId(memory_id)},
                        {"$set": {"is_latest_version": False}},
                        session=session,
                    )

                    # Insert new version
                    database[self.memory_collection].insert_one(
                        new_version.to_dict(), session=session
                    )

            logging.info(
                f"Updated memory {memory_id} to version {new_version.version}",
                extra={
                    "details": {
                        "memory_id": memory_id,
                        "new_version": new_version.version,
                        "updated_by": created_by,
                        "changes": list(changes.keys()),
                    }
                },
            )

            return True

        except Exception as e:
            logging.exception(f"Failed to update memory {memory_id}: {e}")
            return False

    def rollback(self, memory_id: str, to_version: int) -> bool:
        """Rollback to previous version."""
        try:
            # Get current memory to access history
            current_memory = self.get(memory_id)
            if not current_memory:
                logging.warning(f"Memory {memory_id} not found for rollback")
                return False

            # Find the target version in history
            target_version_data = None
            for history_entry in current_memory.history:
                if history_entry.version == to_version:
                    target_version_data = history_entry.body
                    break

            if not target_version_data:
                logging.warning(
                    f"Version {to_version} not found in history for memory {memory_id}"
                )
                return False

            # Create memory from target version data
            target_memory = create_memory_from_dict(target_version_data)

            # Atomic rollback
            database = self.db_manager.database

            with database.client.start_session() as session:
                with session.start_transaction():
                    # Mark current version as not latest
                    database[self.memory_collection].update_one(
                        {"memory_id": current_memory.memory_id},
                        {"$set": {"is_latest_version": False}},
                        session=session,
                    )

                    # Mark target version as latest
                    database[self.memory_collection].update_one(
                        {"memory_id": target_memory.memory_id},
                        {"$set": {"is_latest_version": True}},
                        session=session,
                    )

            logging.info(
                f"Rolled back memory {memory_id} to version {to_version}",
                extra={
                    "details": {
                        "memory_id": memory_id,
                        "target_version": to_version,
                        "original_version": current_memory.version,
                    }
                },
            )

            return True

        except Exception as e:
            logging.exception(f"Failed to rollback memory {memory_id}: {e}")
            return False

    def teach_domain_workflow(
        self,
        question: str,
        logic: Dict[str, Any],
        example_phrasings: List[str],
        created_by: str,
    ) -> str:
        """Create domain workflow memory from question and logic."""
        try:
            memory = DomainWorkflowMemory(
                canonical_question=question,
                answer_plan=logic,
                example_phrasings=example_phrasings,
                created_by=created_by,
                tags=["domain_workflow", "question_answering"],
            )

            return self.create(memory)

        except Exception as e:
            logging.exception(f"Failed to create domain workflow memory: {e}")
            raise

    def teach_field_knowledge(
        self,
        title: str,
        body: str,
        field_type: str,
        aliases: List[str],
        created_by: str,
    ) -> str:
        """Create field knowledge memory from factual information."""
        try:
            memory = FieldKnowledgeMemory(
                title=title,
                body=body,
                field_type=field_type,
                aliases=aliases,
                created_by=created_by,
                tags=["field_knowledge", "knowledge", field_type],
            )

            return self.create(memory)

        except Exception as e:
            logging.exception(f"Failed to create field knowledge memory: {e}")
            raise

    def teach_translation_mapping(
        self,
        business_term: str,
        technical_mapping: str,
        aliases: List[str],
        created_by: str,
    ) -> str:
        """Create translation mapping memory for business lingo mapping."""
        try:
            memory = TranslationMappingMemory(
                created_by=created_by,
                tags=["translation_mapping", "business_lingo", "mapping"],
            )
            memory.set_mapping(business_term, technical_mapping, aliases)

            return self.create(memory)

        except Exception as e:
            logging.exception(f"Failed to create translation mapping memory: {e}")
            raise

    def teach_agent_guidance(self, title: str, body: str, created_by: str) -> str:
        """Create agent guidance memory for internal rules and guidance."""
        try:
            memory = AgentGuidanceMemory(
                title=title,
                body=body,
                created_by=created_by,
                tags=["agent_guidance", "rules", "guidance"],
            )

            return self.create(memory)

        except Exception as e:
            logging.exception(f"Failed to create agent guidance memory: {e}")
            raise

    def add_feedback(
        self, memory_id: str, user_id: str, rating: str, notes: Optional[str] = None
    ) -> bool:
        """Add feedback to a memory unit."""
        try:
            memory = self.get(memory_id)
            if not memory:
                logging.warning(f"Memory {memory_id} not found for feedback")
                return False

            # Add feedback
            memory.add_feedback(user_id, rating, notes)

            # Update feedback counts for domain workflow memories
            if isinstance(memory, DomainWorkflowMemory):
                memory.update_feedback_counts(rating)

            # Update confidence score
            memory.confidence_score = self._calculate_confidence_score(memory, rating)

            # Save updated memory
            return self.update(
                memory_id,
                {
                    "feedback": memory.feedback,
                    "confidence_score": memory.confidence_score,
                },
                user_id,
            )

        except Exception as e:
            logging.exception(f"Failed to add feedback to memory {memory_id}: {e}")
            return False

    def get_memories_by_type(
        self, memory_type: str, limit: int = 50
    ) -> List[MemoryUnit]:
        """Get memories filtered by type."""
        try:
            filter_query = {"type": memory_type, "is_latest_version": True}

            results = self.db_manager.query_mongodb(
                collection=self.memory_collection,
                query_type="find",
                filter=filter_query,
                limit=limit,
                sort={"updated_at": -1},
            )

            return [create_memory_from_dict(result) for result in results]

        except Exception as e:
            logging.exception(f"Failed to get memories by type {memory_type}: {e}")
            return []

    def get_memory_stats(self) -> Dict[str, Any]:
        """Get statistics about the memory system."""
        try:
            database = self.db_manager.database
            collection = database[self.memory_collection]

            # Count by type
            type_counts = list(
                collection.aggregate(
                    [
                        {"$match": {"is_latest_version": True}},
                        {"$group": {"_id": "$type", "count": {"$sum": 1}}},
                        {"$sort": {"count": -1}},
                    ]
                )
            )

            # Total memories
            total_memories = collection.count_documents({"is_latest_version": True})

            # Average confidence score
            avg_confidence = list(
                collection.aggregate(
                    [
                        {"$match": {"is_latest_version": True}},
                        {
                            "$group": {
                                "_id": None,
                                "avg_confidence": {"$avg": "$confidence_score"},
                            }
                        },
                    ]
                )
            )

            return {
                "total_memories": total_memories,
                "by_type": {item["_id"]: item["count"] for item in type_counts},
                "average_confidence": (
                    avg_confidence[0]["avg_confidence"] if avg_confidence else 0.0
                ),
                "embedding_stats": self.embedding_service.get_embedding_stats(),
            }

        except Exception as e:
            logging.exception(f"Failed to get memory stats: {e}")
            return {}

    def _extract_text_for_embedding(self, memory: MemoryUnit) -> str:
        """Extract text content from memory for embedding generation."""
        if isinstance(memory, DomainWorkflowMemory):
            return memory.canonical_question
        elif isinstance(memory, FieldKnowledgeMemory):
            return f"{memory.title} {memory.body}"
        elif isinstance(memory, TranslationMappingMemory):
            return f"{memory.title} {memory.body}"
        elif isinstance(memory, AgentGuidanceMemory):
            return f"{memory.title} {memory.body}"
        else:
            raise ValueError(f"Unknown memory type for embedding: {type(memory)}")

    def _calculate_confidence_score(self, memory: MemoryUnit, new_rating: str) -> float:
        """Calculate updated confidence score based on feedback."""
        if new_rating == "thumbs_up":
            return min(1.0, memory.confidence_score + 0.05)
        elif new_rating == "thumbs_down":
            return max(0.0, memory.confidence_score - 0.1)
        elif new_rating == "wrong_answer":
            return max(0.0, memory.confidence_score - 0.2)

        # Calculate based on acceptance ratio
        acceptance_ratio = memory.calculate_acceptance_ratio()
        return (memory.confidence_score * 0.7) + (acceptance_ratio * 0.3)
