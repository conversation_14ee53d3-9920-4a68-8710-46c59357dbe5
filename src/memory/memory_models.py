"""
Memory data models for the Alfred memory system.
Implements base and specialized memory unit classes.
"""

from dataclasses import asdict, dataclass, field
from datetime import datetime
from typing import Any, Dict, List, Optional


@dataclass
class FeedbackEntry:
    """Represents a user feedback entry for memory quality."""

    user_id: str
    rating: str  # "thumbs_up", "thumbs_down", "text_correction", "wrong_answer"
    notes: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.utcnow)


@dataclass
class HistoryEntry:
    """Represents a version history entry."""

    version: int
    body: Dict[str, Any]
    updated_by: str
    timestamp: datetime = field(default_factory=datetime.utcnow)


@dataclass
class MemoryUnit:
    """Base memory unit class that all memory types inherit from."""

    # Base fields - MongoDB _id will be used as primary identifier
    type: str = field(default="base")
    version: int = field(default=1)
    embedding: Optional[List[float]] = None
    tags: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    created_by: str = field(default="system")
    history: List[HistoryEntry] = field(default_factory=list)
    feedback: List[FeedbackEntry] = field(default_factory=list)

    # Version control fields
    parent_id: Optional[str] = None  # Will store parent's MongoDB _id
    is_latest_version: bool = True
    confidence_score: float = 0.6

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for MongoDB storage."""
        result = asdict(self)
        # Convert datetime objects to ISO format
        result["created_at"] = self.created_at.isoformat()
        result["updated_at"] = self.updated_at.isoformat()

        # Convert history timestamps
        for i, entry in enumerate(result["history"]):
            entry["timestamp"] = entry["timestamp"].isoformat()

        # Convert feedback timestamps
        for i, entry in enumerate(result["feedback"]):
            entry["timestamp"] = entry["timestamp"].isoformat()

        return result

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "MemoryUnit":
        """Create instance from dictionary (MongoDB document)."""
        # Remove MongoDB-specific fields that are not part of the dataclass
        clean_data = {k: v for k, v in data.items() if k != "_id"}

        # Convert ISO timestamps back to datetime
        if "created_at" in clean_data and isinstance(clean_data["created_at"], str):
            clean_data["created_at"] = datetime.fromisoformat(clean_data["created_at"])
        if "updated_at" in clean_data and isinstance(clean_data["updated_at"], str):
            clean_data["updated_at"] = datetime.fromisoformat(clean_data["updated_at"])

        # Convert history timestamps
        for entry in clean_data.get("history", []):
            if "timestamp" in entry and isinstance(entry["timestamp"], str):
                entry["timestamp"] = datetime.fromisoformat(entry["timestamp"])

        # Convert feedback timestamps
        for entry in clean_data.get("feedback", []):
            if "timestamp" in entry and isinstance(entry["timestamp"], str):
                entry["timestamp"] = datetime.fromisoformat(entry["timestamp"])

        # Convert feedback entries to FeedbackEntry objects
        feedback_entries = []
        for fb in clean_data.get("feedback", []):
            if isinstance(fb, dict):
                feedback_entries.append(FeedbackEntry(**fb))
            else:
                feedback_entries.append(fb)
        clean_data["feedback"] = feedback_entries

        # Convert history entries to HistoryEntry objects
        history_entries = []
        for hist in clean_data.get("history", []):
            if isinstance(hist, dict):
                history_entries.append(HistoryEntry(**hist))
            else:
                history_entries.append(hist)
        clean_data["history"] = history_entries

        return cls(**clean_data)

    def create_new_version(self, updated_by: str, parent_id: str) -> "MemoryUnit":
        """Create a new version of this memory unit."""
        # Create history entry for current version
        history_entry = HistoryEntry(
            version=self.version, body=self.to_dict(), updated_by=updated_by
        )

        # Create new version
        new_version = self.__class__(**asdict(self))
        new_version.parent_id = parent_id  # Store parent's MongoDB _id
        new_version.version = self.version + 1
        new_version.updated_at = datetime.utcnow()
        new_version.history = self.history + [history_entry]
        new_version.is_latest_version = True
        new_version.confidence_score = 0.6  # Reset confidence for new version

        return new_version

    def add_feedback(self, user_id: str, rating: str, notes: Optional[str] = None):
        """Add feedback entry to this memory unit."""
        feedback_entry = FeedbackEntry(user_id=user_id, rating=rating, notes=notes)
        self.feedback.append(feedback_entry)
        self.updated_at = datetime.utcnow()

    def calculate_acceptance_ratio(self) -> float:
        """Calculate acceptance ratio from feedback."""
        if not self.feedback:
            return 0.5  # Neutral score with no feedback

        thumbs_up = sum(1 for fb in self.feedback if fb.rating == "thumbs_up")
        thumbs_down = sum(1 for fb in self.feedback if fb.rating == "thumbs_down")
        wrong_answers = sum(1 for fb in self.feedback if fb.rating == "wrong_answer")

        total_feedback = thumbs_up + thumbs_down + wrong_answers
        if total_feedback == 0:
            return 0.5

        return thumbs_up / total_feedback


@dataclass
class DomainWorkflowMemory(MemoryUnit):
    """Domain workflow memory for storing step-by-step analytical workflows."""

    type: str = field(default="domain_workflow")
    canonical_question: str = field(default="")
    answer_plan: Dict[str, Any] = field(default_factory=dict)
    example_phrasings: List[str] = field(default_factory=list)
    usage_count: int = field(default=0)
    acceptance_count: int = field(default=0)
    rejection_count: int = field(default=0)

    def increment_usage(self):
        """Increment usage count and update timestamp."""
        self.usage_count += 1
        self.updated_at = datetime.utcnow()

    def add_example_phrasing(self, phrasing: str):
        """Add a new example phrasing if it doesn't exist."""
        if phrasing not in self.example_phrasings:
            self.example_phrasings.append(phrasing)
            self.updated_at = datetime.utcnow()

    def update_feedback_counts(self, rating: str):
        """Update acceptance/rejection counts based on feedback."""
        if rating == "thumbs_up":
            self.acceptance_count += 1
        elif rating in ["thumbs_down", "wrong_answer"]:
            self.rejection_count += 1
        self.updated_at = datetime.utcnow()


@dataclass
class FieldKnowledgeMemory(MemoryUnit):
    """Field knowledge memory for storing factual information about log fields and data structure."""

    type: str = field(default="field_knowledge")
    title: str = field(default="")
    body: str = field(default="")
    aliases: List[str] = field(default_factory=list)
    field_type: str = field(
        default="general"
    )  # "log_schema", "query_syntax", "data_processing", etc.

    def add_alias(self, alias: str):
        """Add a new alias if it doesn't exist."""
        if alias not in self.aliases:
            self.aliases.append(alias)
            self.updated_at = datetime.utcnow()

    def update_content(self, title: Optional[str] = None, body: Optional[str] = None):
        """Update the content of this field knowledge memory."""
        if title is not None:
            self.title = title
        if body is not None:
            self.body = body
        self.updated_at = datetime.utcnow()


@dataclass
class TranslationMappingMemory(MemoryUnit):
    """Translation mapping memory for business terminology to technical log fields."""

    type: str = field(default="translation_mapping")
    title: str = field(default="")
    body: str = field(default="")
    aliases: List[str] = field(default_factory=list)

    def add_alias(self, alias: str):
        """Add a new alias if it doesn't exist."""
        if alias not in self.aliases:
            self.aliases.append(alias)
            self.updated_at = datetime.utcnow()

    def set_mapping(
        self,
        business_term: str,
        technical_mapping: str,
        aliases: Optional[List[str]] = None,
    ):
        """Set the business term to technical mapping."""
        self.title = f"Business term: {business_term}"
        self.body = technical_mapping
        if aliases:
            self.aliases = aliases
        self.updated_at = datetime.utcnow()


@dataclass
class AgentGuidanceMemory(MemoryUnit):
    """Agent guidance memory for storing internal guidance, rules, and decision-making logic."""

    type: str = field(default="agent_guidance")
    title: str = field(default="")
    body: str = field(default="")

    def update_content(self, title: Optional[str] = None, body: Optional[str] = None):
        """Update the content of this agent guidance memory."""
        if title is not None:
            self.title = title
        if body is not None:
            self.body = body
        self.updated_at = datetime.utcnow()


# Factory function to create appropriate memory type from dictionary
def create_memory_from_dict(data: Dict[str, Any]) -> MemoryUnit:
    """Factory function to create appropriate memory type from dictionary."""
    memory_type = data.get("type", "base")

    if memory_type == "domain_workflow":
        return DomainWorkflowMemory.from_dict(data)
    elif memory_type == "field_knowledge":
        return FieldKnowledgeMemory.from_dict(data)
    elif memory_type == "translation_mapping":
        return TranslationMappingMemory.from_dict(data)
    elif memory_type == "agent_guidance":
        return AgentGuidanceMemory.from_dict(data)
    else:
        return MemoryUnit.from_dict(data)
