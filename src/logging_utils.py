import logging
from datetime import datetime

# Removed DatabaseManager import to avoid circular imports


class LoggingUtils:
    """Utility class for comprehensive logging functionality."""

    @staticmethod
    def convert_protobuf_to_dict(obj):
        """Convert protobuf objects to native Python types for MongoDB storage"""
        try:
            if obj is None:
                return None

            # Handle protobuf repeated/list types
            if hasattr(obj, "__iter__") and not isinstance(obj, (str, bytes, dict)):
                try:
                    # Try to convert to list
                    return [LoggingUtils.convert_protobuf_to_dict(item) for item in obj]
                except:
                    # If iteration fails, convert to string
                    return str(obj)

            # Handle protobuf message types
            if hasattr(obj, "__class__") and "proto" in str(type(obj)):
                try:
                    # Try to convert to dict if it has the right methods
                    if hasattr(obj, "_pb"):
                        return str(obj)  # Fallback to string representation
                    elif hasattr(obj, "to_dict"):
                        return obj.to_dict()
                    else:
                        return str(obj)
                except:
                    return str(obj)

            # Handle regular Python types
            if isinstance(obj, (dict, list, tuple, str, int, float, bool)):
                if isinstance(obj, dict):
                    return {
                        k: LoggingUtils.convert_protobuf_to_dict(v)
                        for k, v in obj.items()
                    }
                elif isinstance(obj, (list, tuple)):
                    return [LoggingUtils.convert_protobuf_to_dict(item) for item in obj]
                else:
                    return obj

            # For everything else, convert to string
            return str(obj)

        except Exception as e:
            # If all else fails, return string representation
            return f"<conversion_error: {str(e)}>"

    @staticmethod
    def truncate_for_logging(data, max_length=2000):
        """Truncate data for logging while preserving structure info"""
        if data is None:
            return None

        data_str = str(data)
        if len(data_str) <= max_length:
            return data

        # For large responses, provide summary info
        truncated = data_str[:max_length]

        # Try to parse as JSON to provide structure info
        try:
            if isinstance(data, (list, dict)):
                if isinstance(data, list):
                    summary_info = f"[List with {len(data)} items]"
                else:
                    summary_info = f"[Dict with keys: {list(data.keys())[:10]}]"  # Show first 10 keys
                return {
                    "truncated_content": truncated + "...",
                    "full_length": len(data_str),
                    "structure_info": summary_info,
                }
        except:
            pass

        return {"truncated_content": truncated + "...", "full_length": len(data_str)}

    @staticmethod
    def log_tool_execution(tool_name, args, response, execution_time=None, error=None):
        """Log comprehensive tool execution details"""
        log_data = {
            "tool_name": tool_name,
            "execution_timestamp": datetime.utcnow().isoformat(),
            "args": LoggingUtils.truncate_for_logging(args, 1500),
            "execution_time_ms": execution_time,
            "success": error is None,
        }

        if error:
            log_data["error"] = str(error)
            log_data["error_type"] = type(error).__name__
            logging.error("Tool execution failed", extra={"details": log_data})
        else:
            log_data["response"] = LoggingUtils.truncate_for_logging(response, 2000)
            logging.info("Tool execution completed", extra={"details": log_data})


# MongoDBLogHandler moved to src/mongodb_log_handler.py to avoid circular imports
