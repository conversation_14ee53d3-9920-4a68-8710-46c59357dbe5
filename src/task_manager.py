import logging
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional

from .logging_utils import LoggingUtils


class TaskStatus(Enum):
    """Enumeration for task status values."""

    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    FAILED = "failed"


class Task:
    """Represents a single task in the agent's plan."""

    def __init__(self, task_id: str, description: str, priority: int = 1):
        self.task_id = task_id
        self.description = description
        self.priority = priority
        self.status = TaskStatus.PENDING
        self.created_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        self.notes = []
        self.dependencies = []  # List of task_ids this task depends on

    def update_status(self, new_status: TaskStatus, note: str = None):
        """Update the task status and add an optional note."""
        old_status = self.status
        self.status = new_status
        self.updated_at = datetime.utcnow()

        if note:
            self.notes.append(
                {
                    "timestamp": self.updated_at,
                    "note": note,
                    "status_change": f"{old_status.value} -> {new_status.value}",
                }
            )

        logging.info(
            "Task status updated",
            extra={
                "details": {
                    "task_id": self.task_id,
                    "old_status": old_status.value,
                    "new_status": new_status.value,
                    "note": (
                        LoggingUtils.truncate_for_logging(note, 500) if note else None
                    ),
                    "status_transition": f"{old_status.value} -> {new_status.value}",
                }
            },
        )

    def to_dict(self):
        """Convert task to dictionary representation."""
        return {
            "task_id": self.task_id,
            "description": self.description,
            "priority": self.priority,
            "status": self.status.value,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "notes": self.notes,
            "dependencies": self.dependencies,
        }


class TaskManager:
    """Manages task planning and execution tracking for the AI agent."""

    def __init__(self, session_id: str):
        self.session_id = session_id
        self.tasks: Dict[str, Task] = {}
        self.task_order: List[str] = []  # Maintains insertion order
        self.current_task_counter = 0

        logging.info(f"Task manager initialized for session: {session_id}")

    def add_tasks(
        self, task_descriptions: List[str], priorities: List[int] = None
    ) -> List[str]:
        """Add multiple tasks to the plan."""
        if not task_descriptions:
            raise ValueError("Task descriptions cannot be empty")

        if priorities is None:
            priorities = [1] * len(task_descriptions)
        elif len(priorities) != len(task_descriptions):
            raise ValueError(
                "Number of priorities must match number of task descriptions"
            )

        added_task_ids = []

        for i, description in enumerate(task_descriptions):
            self.current_task_counter += 1
            task_id = f"task_{self.current_task_counter:03d}"

            task = Task(
                task_id=task_id, description=description, priority=priorities[i]
            )

            self.tasks[task_id] = task
            self.task_order.append(task_id)
            added_task_ids.append(task_id)

        logging.info(
            "Tasks added to execution plan",
            extra={
                "details": {
                    "session_id": self.session_id,
                    "tasks_added": len(task_descriptions),
                    "added_task_ids": added_task_ids,
                    "total_tasks": len(self.tasks),
                    "task_descriptions": [
                        LoggingUtils.truncate_for_logging(desc, 200)
                        for desc in task_descriptions
                    ],
                }
            },
        )

        # Log milestone if this is the first task plan for the session
        if len(self.tasks) == len(added_task_ids):
            self.log_plan_milestone(
                "initial_plan_created", {"initial_task_count": len(added_task_ids)}
            )

        return added_task_ids

    def update_task_status(
        self, task_id: str, status_str: str, note: str = None
    ) -> bool:
        """Update the status of a specific task."""
        if task_id not in self.tasks:
            logging.warning(
                "Task not found for status update",
                extra={
                    "details": {
                        "session_id": self.session_id,
                        "task_id": task_id,
                        "attempted_status": status_str,
                        "available_tasks": list(self.tasks.keys()),
                    }
                },
            )
            return False

        try:
            new_status = TaskStatus(status_str.lower())
        except ValueError:
            logging.error(
                "Invalid task status provided",
                extra={
                    "details": {
                        "session_id": self.session_id,
                        "task_id": task_id,
                        "invalid_status": status_str,
                        "valid_statuses": [status.value for status in TaskStatus],
                    }
                },
            )
            return False

        self.tasks[task_id].update_status(new_status, note)

        # Check if this status change completes the task plan
        if new_status in [
            TaskStatus.COMPLETED,
            TaskStatus.FAILED,
            TaskStatus.CANCELLED,
        ]:
            self.check_and_log_completion()

        return True

    def cancel_task(self, task_id: str, reason: str = None) -> bool:
        """Cancel a specific task."""
        success = self.update_task_status(task_id, "cancelled", reason)

        if success:
            logging.info(
                "Task cancelled",
                extra={
                    "details": {
                        "session_id": self.session_id,
                        "task_id": task_id,
                        "cancellation_reason": (
                            LoggingUtils.truncate_for_logging(reason, 500)
                            if reason
                            else "No reason provided"
                        ),
                        "remaining_active_tasks": len(
                            [
                                t
                                for t in self.tasks.values()
                                if t.status
                                in [TaskStatus.PENDING, TaskStatus.IN_PROGRESS]
                            ]
                        ),
                    }
                },
            )

        return success

    def get_tasks(
        self, status_filter: str = None, include_cancelled: bool = False
    ) -> List[Dict]:
        """Get tasks, optionally filtered by status."""
        tasks = []

        for task_id in self.task_order:
            task = self.tasks[task_id]

            # Apply status filter
            if status_filter:
                try:
                    filter_status = TaskStatus(status_filter.lower())
                    if task.status != filter_status:
                        continue
                except ValueError:
                    continue

            # Skip cancelled tasks unless explicitly requested
            if not include_cancelled and task.status == TaskStatus.CANCELLED:
                continue

            tasks.append(task.to_dict())

        # Log task queries for audit trail
        logging.debug(
            "Task list queried",
            extra={
                "details": {
                    "session_id": self.session_id,
                    "status_filter": status_filter,
                    "include_cancelled": include_cancelled,
                    "results_count": len(tasks),
                    "total_tasks": len(self.tasks),
                }
            },
        )

        return tasks

    def get_task_summary(self) -> Dict:
        """Get a summary of all tasks by status."""
        summary = {
            "total_tasks": len(self.tasks),
            "by_status": {},
            "pending_tasks": [],
            "in_progress_tasks": [],
            "completed_tasks": [],
            "failed_tasks": [],
        }

        for task_id in self.task_order:
            task = self.tasks[task_id]
            status = task.status.value

            # Count by status
            summary["by_status"][status] = summary["by_status"].get(status, 0) + 1

            # Add to status-specific lists
            task_dict = task.to_dict()
            if task.status == TaskStatus.PENDING:
                summary["pending_tasks"].append(task_dict)
            elif task.status == TaskStatus.IN_PROGRESS:
                summary["in_progress_tasks"].append(task_dict)
            elif task.status == TaskStatus.COMPLETED:
                summary["completed_tasks"].append(task_dict)
            elif task.status == TaskStatus.FAILED:
                summary["failed_tasks"].append(task_dict)

        return summary

    def get_next_pending_task(self) -> Optional[Dict]:
        """Get the next pending task based on priority and order."""
        pending_tasks = [
            task for task in self.tasks.values() if task.status == TaskStatus.PENDING
        ]

        if not pending_tasks:
            return None

        # Sort by priority (higher first), then by creation order
        next_task = min(pending_tasks, key=lambda t: (-t.priority, t.created_at))
        return next_task.to_dict()

    def mark_current_task_in_progress(self, task_id: str = None) -> bool:
        """Mark a task as in progress. If no task_id provided, uses next pending task."""
        if task_id is None:
            next_task = self.get_next_pending_task()
            if not next_task:
                return False
            task_id = next_task["task_id"]

        return self.update_task_status(
            task_id, "in_progress", "Starting task execution"
        )

    def log_plan_milestone(self, milestone_type: str, details: Dict = None):
        """Log significant task plan milestones."""
        summary = self.get_task_summary()

        log_details = {
            "session_id": self.session_id,
            "milestone_type": milestone_type,
            "total_tasks": summary["total_tasks"],
            "status_breakdown": summary["by_status"],
        }

        if details:
            log_details.update(details)

        logging.info(
            f"Task plan milestone: {milestone_type}", extra={"details": log_details}
        )

    def check_and_log_completion(self):
        """Check if all tasks are completed and log milestone if so."""
        summary = self.get_task_summary()

        total_tasks = summary["total_tasks"]
        completed_count = len(summary["completed_tasks"])
        failed_count = len(summary["failed_tasks"])
        cancelled_count = summary["by_status"].get("cancelled", 0)

        # All tasks are done (completed, failed, or cancelled)
        if (
            total_tasks > 0
            and (completed_count + failed_count + cancelled_count) == total_tasks
        ):
            self.log_plan_milestone(
                "all_tasks_finished",
                {
                    "completed_tasks": completed_count,
                    "failed_tasks": failed_count,
                    "cancelled_tasks": cancelled_count,
                    "success_rate": (
                        completed_count / total_tasks if total_tasks > 0 else 0
                    ),
                },
            )

    def archive_and_reset(self) -> Dict:
        """Archive current tasks and reset for next question."""

        # Get final state before clearing
        final_summary = self.get_task_summary()
        final_tasks = [self.tasks[task_id].to_dict() for task_id in self.task_order]

        # Log the archival
        logging.info(
            "Archiving completed task plan and resetting for next question",
            extra={
                "details": {
                    "session_id": self.session_id,
                    "archived_task_count": len(self.tasks),
                    "completed_tasks": len(final_summary["completed_tasks"]),
                    "failed_tasks": len(final_summary["failed_tasks"]),
                    "success_rate": (
                        len(final_summary["completed_tasks"]) / len(self.tasks)
                        if len(self.tasks) > 0
                        else 0
                    ),
                }
            },
        )

        # Clear all tasks and reset counters
        self.tasks.clear()
        self.task_order.clear()
        self.current_task_counter = 0

        # Return archived state for reference
        return {
            "archived_at": datetime.utcnow().isoformat(),
            "session_id": self.session_id,
            "total_tasks": len(final_tasks),
            "tasks": final_tasks,
            "summary": final_summary,
        }

    def to_dict(self) -> Dict:
        """Convert entire task manager state to dictionary."""
        return {
            "session_id": self.session_id,
            "total_tasks": len(self.tasks),
            "task_order": self.task_order,
            "tasks": [self.tasks[task_id].to_dict() for task_id in self.task_order],
            "summary": self.get_task_summary(),
        }
