#!/usr/bin/env python3
"""
Enhanced utility script to view and filter model logging from audit_trail.log
This helps you easily see model thoughts, responses, API metrics, and performance data.
"""

import ast
import json
import re
import sys


def safe_eval_details(details_str):
    """Safely evaluate a string representation of Python objects"""
    if not isinstance(details_str, str) or not details_str.startswith("{"):
        return details_str

    try:
        # First try ast.literal_eval for safety
        return ast.literal_eval(details_str)
    except (ValueError, SyntaxError):
        try:
            # If that fails, try a more permissive approach
            # Replace Python objects that ast.literal_eval can't handle
            cleaned = details_str

            # Replace enum values like <FinishReason.STOP: 1> with just the number
            cleaned = re.sub(r"<[^>]+:\s*(\d+)>", r"\1", cleaned)

            # Try again with cleaned string
            return ast.literal_eval(cleaned)
        except (ValueError, SyntaxError):
            # If all else fails, return the original string
            return details_str


def load_logs(filename="audit_trail.log"):
    """Load and parse JSON logs from file"""
    logs = []
    try:
        with open(filename, "r") as f:
            for line in f:
                try:
                    log = json.loads(line.strip())

                    # Fix details field if it's a string representation of a dict
                    details = log.get("details")
                    if details:
                        log["details"] = safe_eval_details(details)

                    logs.append(log)
                except json.JSONDecodeError:
                    continue
    except FileNotFoundError:
        print(f"❌ Error: {filename} not found")
        sys.exit(1)
    return logs


def filter_model_logs(logs, log_type=None, run_id=None, search_term=None):
    """Filter logs for model-related entries"""
    model_keywords = [
        "MODEL THOUGHT",
        "MODEL RESPONSE",
        "FINAL MODEL ANSWER",
        "INITIAL MODEL RESPONSE",
        "Model response details",
        "Tool execution",
    ]

    filtered = []
    for log in logs:
        message = log.get("message", "")

        # Filter by run_id if specified
        if run_id and log.get("run_id") != run_id:
            continue

        # Filter by search term
        if search_term:
            log_str = json.dumps(log, default=str).lower()
            if search_term.lower() not in log_str:
                continue

        # Filter by log type
        if log_type:
            if log_type == "thoughts" and "MODEL THOUGHT" not in message:
                continue
            elif log_type == "responses" and not any(
                kw in message
                for kw in [
                    "MODEL RESPONSE",
                    "FINAL MODEL ANSWER",
                    "INITIAL MODEL RESPONSE",
                ]
            ):
                continue
            elif log_type == "api_metrics" and "Model response details" not in message:
                continue
            elif log_type == "tools" and "Tool execution" not in message:
                continue

        # Include if contains model keywords
        if any(keyword in message for keyword in model_keywords):
            filtered.append(log)

    return filtered


def get_recent_runs(logs, limit=5):
    """Get the most recent run IDs"""
    run_ids = []
    seen = set()

    # Reverse to get most recent first
    for log in reversed(logs):
        run_id = log.get("run_id")
        if run_id and run_id not in seen:
            run_ids.append(run_id)
            seen.add(run_id)
            if len(run_ids) >= limit:
                break

    return run_ids


def print_log_summary(logs):
    """Print a comprehensive summary of available logs"""
    run_ids = set()
    log_types = {}
    timestamps = []

    for log in logs:
        run_id = log.get("run_id", "unknown")
        run_ids.add(run_id)

        timestamp = log.get("timestamp")
        if timestamp:
            timestamps.append(timestamp)

        message = log.get("message", "")
        if "MODEL THOUGHT" in message:
            log_types.setdefault("thoughts", 0)
            log_types["thoughts"] += 1
        elif "MODEL RESPONSE" in message or "FINAL MODEL ANSWER" in message:
            log_types.setdefault("responses", 0)
            log_types["responses"] += 1
        elif "Model response details" in message:
            log_types.setdefault("api_metrics", 0)
            log_types["api_metrics"] += 1
        elif "Tool execution" in message:
            log_types.setdefault("tools", 0)
            log_types["tools"] += 1

    print("📊 ENHANCED LOG SUMMARY")
    print("=" * 60)
    print(f"📈 Total model-related logs: {len(logs)}")
    print(f"🔄 Available run sessions: {len(run_ids)}")

    # Show recent runs
    recent_runs = get_recent_runs(logs, 5)
    print(f"\n🕒 Most recent runs:")
    for i, run_id in enumerate(recent_runs, 1):
        print(f"  {i}. {run_id}")

    # Show time range
    if timestamps:
        timestamps.sort()
        print(f"\n📅 Time range: {timestamps[0]} → {timestamps[-1]}")

    print(f"\n📋 Log categories:")
    for log_type, count in sorted(log_types.items()):
        emoji = {
            "thoughts": "🧠",
            "responses": "🤖",
            "api_metrics": "📈",
            "tools": "🔧",
        }.get(log_type, "📄")
        print(f"  {emoji} {log_type}: {count}")
    print()


def print_model_thoughts(logs):
    """Print model thoughts/reasoning with enhanced formatting"""
    print("🧠 MODEL THOUGHTS & REASONING")
    print("=" * 60)

    if not logs:
        print("❌ No model thoughts found for the specified criteria")
        return

    for i, log in enumerate(logs, 1):
        if "MODEL THOUGHT" in log.get("message", ""):
            details = log.get("details", {})
            timestamp = log.get("timestamp", "")
            run_id = log.get("run_id", "")
            iteration = details.get("iteration", "")
            reasoning = details.get("model_reasoning", "")

            print(f"\n💭 THOUGHT #{i}")
            print(f"🕒 {timestamp}")
            print(f"🔄 Run: {run_id} | Iteration: {iteration}")
            print(f"📝 Length: {len(reasoning)} characters")
            print("-" * 50)
            print(reasoning)
            print("-" * 50)


def print_model_responses(logs):
    """Print model text responses with enhanced formatting"""
    print("🤖 MODEL TEXT RESPONSES")
    print("=" * 60)

    if not logs:
        print("❌ No model responses found for the specified criteria")
        return

    for i, log in enumerate(logs, 1):
        message = log.get("message", "")
        if any(
            kw in message
            for kw in ["MODEL RESPONSE", "FINAL MODEL ANSWER", "INITIAL MODEL RESPONSE"]
        ):
            details = log.get("details", {})
            timestamp = log.get("timestamp", "")
            run_id = log.get("run_id", "")

            # Extract the actual text content
            text_content = (
                details.get("model_text")
                or details.get("final_answer_text")
                or "No text content found"
            )

            response_type = details.get("response_type", "unknown")
            iteration = details.get("iteration", "")

            # Determine emoji based on response type
            emoji = {
                "initial_response": "🚀",
                "post_tool_response": "🔄",
                "final_answer": "✅",
                "summary_response": "📝",
            }.get(response_type, "🤖")

            print(f"\n{emoji} RESPONSE #{i}")
            print(f"🕒 {timestamp}")
            print(f"🔄 Run: {run_id} | Type: {response_type}")
            if iteration:
                print(f"🔢 Iteration: {iteration}")
            print(f"📏 Length: {len(text_content)} characters")
            print("-" * 50)
            print(text_content)
            print("-" * 50)


def print_api_metrics(logs):
    """Print API metrics and performance data with enhanced formatting"""
    print("📈 API METRICS & PERFORMANCE")
    print("=" * 60)

    if not logs:
        print("❌ No API metrics found for the specified criteria")
        return

    total_cost = 0
    total_tokens = 0

    for i, log in enumerate(logs, 1):
        message = log.get("message", "")

        if "Model response details" in message:
            details = log.get("details", {})

            if not isinstance(details, dict):
                continue

            timestamp = log.get("timestamp", "")
            run_id = log.get("run_id", "")
            step = details.get("step", "")

            api_metrics = details.get("api_metrics", {})
            if api_metrics and isinstance(api_metrics, dict):
                input_tokens = api_metrics.get("prompt_token_count", 0) or 0
                output_tokens = api_metrics.get("candidates_token_count", 0) or 0
                total_tokens_call = api_metrics.get("total_token_count", 0) or 0
                cost = details.get("estimated_cost_usd", 0) or 0

                total_cost += cost
                total_tokens += total_tokens_call

                print(f"\n📊 API CALL #{i}")
                print(f"🕒 {timestamp}")
                print(f"🔄 Run: {run_id} | Step: {step}")
                print(f"🔢 Token Usage:")
                print(f"  📥 Input tokens: {input_tokens:,}")
                print(f"  📤 Output tokens: {output_tokens:,}")
                print(f"  📊 Total tokens: {total_tokens_call:,}")
                if cost > 0:
                    print(f"  💰 Estimated cost: ${cost:.6f}")

                # Show token breakdown if available
                prompt_details = api_metrics.get("prompt_tokens_details", {})
                if prompt_details and isinstance(prompt_details, dict):
                    print(f"  📋 Input breakdown:")
                    for token_type, count in prompt_details.items():
                        if count and count > 0:
                            print(f"    - {token_type}: {count:,}")

                candidates_details = api_metrics.get("candidates_tokens_details", {})
                if candidates_details and isinstance(candidates_details, dict):
                    print(f"  📋 Output breakdown:")
                    for token_type, count in candidates_details.items():
                        if count and count > 0:
                            print(f"    - {token_type}: {count:,}")

                print("-" * 50)

    if total_cost > 0 or total_tokens > 0:
        print(f"\n💡 SUMMARY:")
        print(f"  📊 Total tokens across all calls: {total_tokens:,}")
        if total_cost > 0:
            print(f"  💰 Total estimated cost: ${total_cost:.6f}")


def print_tool_execution(logs):
    """Print tool execution logs with enhanced formatting"""
    print("🔧 TOOL EXECUTION LOGS")
    print("=" * 60)

    if not logs:
        print("❌ No tool execution logs found for the specified criteria")
        return

    for i, log in enumerate(logs, 1):
        if "Tool execution" in log.get("message", ""):
            details = log.get("details", {})
            if not isinstance(details, dict):
                continue

            timestamp = log.get("timestamp", "")
            run_id = log.get("run_id", "")

            tool_name = details.get("tool_name", "unknown")
            success = details.get("success", False)
            execution_time = details.get("execution_time_ms", 0)

            status_emoji = "✅" if success else "❌"

            print(f"\n{status_emoji} TOOL #{i}")
            print(f"🕒 {timestamp}")
            print(f"🔄 Run: {run_id}")
            print(f"🔧 Tool: {tool_name}")
            print(f"⏱️  Execution time: {execution_time:.2f}ms")

            if not success and details.get("error"):
                print(f"❌ Error: {details.get('error')}")

            print("-" * 50)


def search_logs(logs, search_term):
    """Search through logs for specific content"""
    print(f"🔍 SEARCH RESULTS FOR: '{search_term}'")
    print("=" * 60)

    matching_logs = []
    for log in logs:
        log_str = json.dumps(log, default=str)
        if search_term.lower() in log_str.lower():
            matching_logs.append(log)

    if not matching_logs:
        print(f"❌ No logs found containing '{search_term}'")
        return

    print(f"📊 Found {len(matching_logs)} matching logs")
    print()

    for i, log in enumerate(matching_logs[:10], 1):  # Show first 10 results
        message = log.get("message", "")
        timestamp = log.get("timestamp", "")
        run_id = log.get("run_id", "")

        print(f"{i}. [{timestamp}] {run_id}")
        print(f"   📝 {message}")

        # Highlight matching content in details
        details = log.get("details", {})
        if details:
            details_str = json.dumps(details, default=str)
            if search_term.lower() in details_str.lower():
                # Find and show context around the match
                match_pos = details_str.lower().find(search_term.lower())
                start = max(0, match_pos - 50)
                end = min(len(details_str), match_pos + len(search_term) + 50)
                context = details_str[start:end]
                print(f"   🎯 ...{context}...")
        print()

    if len(matching_logs) > 10:
        print(f"... and {len(matching_logs) - 10} more results")


def main():
    if len(sys.argv) < 2:
        print("🚀 ENHANCED MODEL LOG VIEWER")
        print("=" * 50)
        print("Usage: python view_model_logs.py <command> [options]")
        print("\n📋 Commands:")
        print("  summary                    - Show comprehensive log summary")
        print("  thoughts [run_id]          - Show model thoughts/reasoning")
        print("  responses [run_id]         - Show model text responses")
        print("  api_metrics [run_id]       - Show API metrics and performance")
        print("  tools [run_id]             - Show tool execution logs")
        print("  search <term> [run_id]     - Search logs for specific content")
        print("  all [run_id]               - Show all model-related logs")
        print("  recent [count]             - Show recent runs (default: 5)")
        print("\n💡 Examples:")
        print("  python view_model_logs.py summary")
        print("  python view_model_logs.py thoughts run-abc123")
        print("  python view_model_logs.py search 'MongoDB query'")
        print("  python view_model_logs.py api_metrics")
        print("  python view_model_logs.py recent 3")
        return

    command = sys.argv[1]

    # Load logs
    logs = load_logs()
    model_logs = filter_model_logs(logs)

    if command == "summary":
        print_log_summary(model_logs)

    elif command == "recent":
        count = int(sys.argv[2]) if len(sys.argv) > 2 else 5
        recent_runs = get_recent_runs(model_logs, count)
        print(f"🕒 {count} MOST RECENT RUNS")
        print("=" * 40)
        for i, run_id in enumerate(recent_runs, 1):
            run_logs = [log for log in model_logs if log.get("run_id") == run_id]
            print(f"{i}. {run_id} ({len(run_logs)} logs)")

    elif command == "thoughts":
        run_id = sys.argv[2] if len(sys.argv) > 2 else None
        filtered_logs = filter_model_logs(model_logs, "thoughts", run_id)
        print_model_thoughts(filtered_logs)

    elif command == "responses":
        run_id = sys.argv[2] if len(sys.argv) > 2 else None
        filtered_logs = filter_model_logs(model_logs, "responses", run_id)
        print_model_responses(filtered_logs)

    elif command == "api_metrics":
        run_id = sys.argv[2] if len(sys.argv) > 2 else None
        filtered_logs = filter_model_logs(model_logs, "api_metrics", run_id)
        print_api_metrics(filtered_logs)

    elif command == "tools":
        run_id = sys.argv[2] if len(sys.argv) > 2 else None
        filtered_logs = filter_model_logs(model_logs, "tools", run_id)
        print_tool_execution(filtered_logs)

    elif command == "search":
        if len(sys.argv) < 3:
            print("❌ Error: Search term required")
            print("Usage: python view_model_logs.py search <term> [run_id]")
            return

        search_term = sys.argv[2]
        run_id = sys.argv[3] if len(sys.argv) > 3 else None
        filtered_logs = filter_model_logs(model_logs, None, run_id, search_term)
        search_logs(filtered_logs, search_term)

    elif command == "all":
        run_id = sys.argv[2] if len(sys.argv) > 2 else None
        if run_id:
            model_logs = [log for log in model_logs if log.get("run_id") == run_id]
            print(f"🔄 ALL LOGS FOR RUN: {run_id}")
            print("=" * 60)
        else:
            print("🔄 ALL MODEL LOGS")
            print("=" * 60)

        print_model_thoughts(filter_model_logs(model_logs, "thoughts"))
        print("\n" + "=" * 70 + "\n")
        print_model_responses(filter_model_logs(model_logs, "responses"))
        print("\n" + "=" * 70 + "\n")
        print_api_metrics(filter_model_logs(model_logs, "api_metrics"))
        print("\n" + "=" * 70 + "\n")
        print_tool_execution(filter_model_logs(model_logs, "tools"))

    else:
        print(f"❌ Unknown command: {command}")
        print("Use 'python view_model_logs.py' to see usage.")


if __name__ == "__main__":
    main()
